﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class frmIMAP
    Inherits DevExpress.XtraEditors.XtraForm
    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub
    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer
    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmIMAP))
        Me.trmfadein = New System.Windows.Forms.Timer(Me.components)
        Me.trmfadeout = New System.Windows.Forms.Timer(Me.components)
        Me.Label1 = New System.Windows.Forms.Label()
        Me.BunifuImageButton7 = New Bunifu.Framework.UI.BunifuImageButton()
        Me.RadialMenu1 = New DevExpress.XtraBars.Ribbon.RadialMenu(Me.components)
        Me.ToolTip1 = New System.Windows.Forms.ToolTip(Me.components)
        Me.PictureBox1 = New System.Windows.Forms.PictureBox()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.TextHost = New DevExpress.XtraEditors.TextEdit()
        Me.TextPort = New DevExpress.XtraEditors.TextEdit()
        Me.PictureBox2 = New System.Windows.Forms.PictureBox()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.Label5 = New System.Windows.Forms.Label()
        Me.TextEmail = New DevExpress.XtraEditors.TextEdit()
        Me.PictureBox3 = New System.Windows.Forms.PictureBox()
        Me.Label6 = New System.Windows.Forms.Label()
        Me.Label7 = New System.Windows.Forms.Label()
        Me.txtPassword = New DevExpress.XtraEditors.TextEdit()
        Me.PictureBox4 = New System.Windows.Forms.PictureBox()
        Me.Label8 = New System.Windows.Forms.Label()
        Me.Label9 = New System.Windows.Forms.Label()
        Me.CheckEdit1 = New DevExpress.XtraEditors.CheckEdit()
        Me.BNT_New = New DevExpress.XtraEditors.SimpleButton()
        Me.BNT_CheckEmail = New DevExpress.XtraEditors.SimpleButton()
        Me.BNT_Connect = New DevExpress.XtraEditors.SimpleButton()
        Me.PictureBox5 = New System.Windows.Forms.PictureBox()
        Me.Label10 = New System.Windows.Forms.Label()
        Me.SimpleButton1 = New DevExpress.XtraEditors.SimpleButton()
        Me.txtMailNumber = New DevExpress.XtraEditors.TextEdit()
        Me.DxErrorProvider1 = New DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider(Me.components)
        Me.picStatus = New System.Windows.Forms.PictureBox()
        Me.txtMailList = New DevExpress.XtraEditors.MemoEdit()
        Me.CheckSSL = New DevExpress.XtraEditors.CheckEdit()
        Me.BackgroundWorker2 = New System.ComponentModel.BackgroundWorker()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.SimpleButton2 = New DevExpress.XtraEditors.SimpleButton()
        Me.ProgressPanel1 = New DevExpress.XtraWaitForm.ProgressPanel()
        Me.BackgroundWorker1 = New System.ComponentModel.BackgroundWorker()
        Me.cbTags = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Label12 = New System.Windows.Forms.Label()
        Me.SeparatorControl3 = New DevExpress.XtraEditors.SeparatorControl()
        Me.SeparatorControl4 = New DevExpress.XtraEditors.SeparatorControl()
        Me.Label11 = New System.Windows.Forms.Label()
        CType(Me.BunifuImageButton7, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RadialMenu1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextHost.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextPort.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureBox2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEmail.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureBox3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtPassword.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureBox4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckEdit1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureBox5, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtMailNumber.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DxErrorProvider1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.picStatus, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtMailList.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckSSL.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Panel1.SuspendLayout()
        CType(Me.cbTags.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SeparatorControl3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SeparatorControl4, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'trmfadein
        '
        Me.trmfadein.Enabled = True
        Me.trmfadein.Interval = 1
        '
        'trmfadeout
        '
        Me.trmfadeout.Interval = 1
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.Font = New System.Drawing.Font("Audiowide", 14.25!, System.Drawing.FontStyle.Bold)
        Me.Label1.ForeColor = System.Drawing.Color.White
        Me.Label1.Location = New System.Drawing.Point(95, 51)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(136, 24)
        Me.Label1.TabIndex = 23
        Me.Label1.Text = "IMAP Tools"
        '
        'BunifuImageButton7
        '
        Me.BunifuImageButton7.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.BunifuImageButton7.BackColor = System.Drawing.Color.FromArgb(CType(CType(40, Byte), Integer), CType(CType(42, Byte), Integer), CType(CType(59, Byte), Integer))
        Me.BunifuImageButton7.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Center
        Me.BunifuImageButton7.Image = CType(resources.GetObject("BunifuImageButton7.Image"), System.Drawing.Image)
        Me.BunifuImageButton7.ImageActive = Nothing
        Me.BunifuImageButton7.Location = New System.Drawing.Point(961, 7)
        Me.BunifuImageButton7.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.BunifuImageButton7.Name = "BunifuImageButton7"
        Me.BunifuImageButton7.Size = New System.Drawing.Size(32, 25)
        Me.BunifuImageButton7.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom
        Me.BunifuImageButton7.TabIndex = 298
        Me.BunifuImageButton7.TabStop = False
        Me.BunifuImageButton7.Visible = False
        Me.BunifuImageButton7.Zoom = 10
        '
        'RadialMenu1
        '
        Me.RadialMenu1.AlphaChannel = CType(245, Byte)
        Me.RadialMenu1.ArcHoveredThickness = 2
        Me.RadialMenu1.AutoExpand = True
        Me.RadialMenu1.ButtonRadius = 30
        Me.RadialMenu1.Glyph = CType(resources.GetObject("RadialMenu1.Glyph"), System.Drawing.Image)
        Me.RadialMenu1.InnerRadius = 0
        Me.RadialMenu1.ItemAutoSize = DevExpress.XtraBars.Ribbon.RadialMenuItemAutoSize.Spring
        Me.RadialMenu1.Name = "RadialMenu1"
        '
        'PictureBox1
        '
        Me.PictureBox1.Image = CType(resources.GetObject("PictureBox1.Image"), System.Drawing.Image)
        Me.PictureBox1.Location = New System.Drawing.Point(57, 171)
        Me.PictureBox1.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.PictureBox1.Name = "PictureBox1"
        Me.PictureBox1.Size = New System.Drawing.Size(460, 41)
        Me.PictureBox1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage
        Me.PictureBox1.TabIndex = 434
        Me.PictureBox1.TabStop = False
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Font = New System.Drawing.Font("Comfortaa", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label2.ForeColor = System.Drawing.Color.LightSkyBlue
        Me.Label2.Location = New System.Drawing.Point(76, 141)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(106, 26)
        Me.Label2.TabIndex = 438
        Me.Label2.Text = "Host IMAP :"
        '
        'Label3
        '
        Me.Label3.AutoSize = True
        Me.Label3.Font = New System.Drawing.Font("Comfortaa", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label3.ForeColor = System.Drawing.Color.Salmon
        Me.Label3.Location = New System.Drawing.Point(59, 147)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(18, 26)
        Me.Label3.TabIndex = 439
        Me.Label3.Text = "*"
        '
        'TextHost
        '
        Me.TextHost.EditValue = ""
        Me.TextHost.Location = New System.Drawing.Point(67, 179)
        Me.TextHost.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.TextHost.Name = "TextHost"
        Me.TextHost.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.TextHost.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.0!)
        Me.TextHost.Properties.Appearance.ForeColor = System.Drawing.Color.Gainsboro
        Me.TextHost.Properties.Appearance.Options.UseBackColor = True
        Me.TextHost.Properties.Appearance.Options.UseFont = True
        Me.TextHost.Properties.Appearance.Options.UseForeColor = True
        Me.TextHost.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.TextHost.Properties.NullValuePrompt = "Enter your host IMAP name"
        Me.TextHost.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TextHost.Size = New System.Drawing.Size(442, 26)
        Me.TextHost.TabIndex = 0
        '
        'TextPort
        '
        Me.TextPort.EditValue = "993"
        Me.TextPort.Location = New System.Drawing.Point(67, 296)
        Me.TextPort.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.TextPort.Name = "TextPort"
        Me.TextPort.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.TextPort.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.0!)
        Me.TextPort.Properties.Appearance.ForeColor = System.Drawing.Color.Gainsboro
        Me.TextPort.Properties.Appearance.Options.UseBackColor = True
        Me.TextPort.Properties.Appearance.Options.UseFont = True
        Me.TextPort.Properties.Appearance.Options.UseForeColor = True
        Me.TextPort.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.TextPort.Properties.NullValuePrompt = "Enter your host IMAP port number"
        Me.TextPort.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TextPort.Size = New System.Drawing.Size(442, 26)
        Me.TextPort.TabIndex = 1
        '
        'PictureBox2
        '
        Me.PictureBox2.Image = CType(resources.GetObject("PictureBox2.Image"), System.Drawing.Image)
        Me.PictureBox2.Location = New System.Drawing.Point(57, 288)
        Me.PictureBox2.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.PictureBox2.Name = "PictureBox2"
        Me.PictureBox2.Size = New System.Drawing.Size(460, 41)
        Me.PictureBox2.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage
        Me.PictureBox2.TabIndex = 441
        Me.PictureBox2.TabStop = False
        '
        'Label4
        '
        Me.Label4.AutoSize = True
        Me.Label4.Font = New System.Drawing.Font("Comfortaa", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label4.ForeColor = System.Drawing.Color.Salmon
        Me.Label4.Location = New System.Drawing.Point(59, 264)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(18, 26)
        Me.Label4.TabIndex = 443
        Me.Label4.Text = "*"
        '
        'Label5
        '
        Me.Label5.AutoSize = True
        Me.Label5.Font = New System.Drawing.Font("Comfortaa", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label5.ForeColor = System.Drawing.Color.LightSkyBlue
        Me.Label5.Location = New System.Drawing.Point(76, 258)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(54, 26)
        Me.Label5.TabIndex = 442
        Me.Label5.Text = "Port :"
        '
        'TextEmail
        '
        Me.TextEmail.EditValue = ""
        Me.TextEmail.Location = New System.Drawing.Point(67, 419)
        Me.TextEmail.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.TextEmail.Name = "TextEmail"
        Me.TextEmail.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.TextEmail.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.0!)
        Me.TextEmail.Properties.Appearance.ForeColor = System.Drawing.Color.Gainsboro
        Me.TextEmail.Properties.Appearance.Options.UseBackColor = True
        Me.TextEmail.Properties.Appearance.Options.UseFont = True
        Me.TextEmail.Properties.Appearance.Options.UseForeColor = True
        Me.TextEmail.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.TextEmail.Properties.NullValuePrompt = "Enter your host IMAP user name"
        Me.TextEmail.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TextEmail.Size = New System.Drawing.Size(442, 26)
        Me.TextEmail.TabIndex = 2
        '
        'PictureBox3
        '
        Me.PictureBox3.Image = CType(resources.GetObject("PictureBox3.Image"), System.Drawing.Image)
        Me.PictureBox3.Location = New System.Drawing.Point(57, 412)
        Me.PictureBox3.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.PictureBox3.Name = "PictureBox3"
        Me.PictureBox3.Size = New System.Drawing.Size(460, 41)
        Me.PictureBox3.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage
        Me.PictureBox3.TabIndex = 445
        Me.PictureBox3.TabStop = False
        '
        'Label6
        '
        Me.Label6.AutoSize = True
        Me.Label6.Font = New System.Drawing.Font("Comfortaa", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label6.ForeColor = System.Drawing.Color.Salmon
        Me.Label6.Location = New System.Drawing.Point(59, 388)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(18, 26)
        Me.Label6.TabIndex = 447
        Me.Label6.Text = "*"
        '
        'Label7
        '
        Me.Label7.AutoSize = True
        Me.Label7.Font = New System.Drawing.Font("Comfortaa", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label7.ForeColor = System.Drawing.Color.LightSkyBlue
        Me.Label7.Location = New System.Drawing.Point(76, 382)
        Me.Label7.Name = "Label7"
        Me.Label7.Size = New System.Drawing.Size(113, 26)
        Me.Label7.TabIndex = 446
        Me.Label7.Text = "User Name :"
        '
        'txtPassword
        '
        Me.txtPassword.EditValue = ""
        Me.txtPassword.Location = New System.Drawing.Point(67, 546)
        Me.txtPassword.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.txtPassword.Name = "txtPassword"
        Me.txtPassword.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.txtPassword.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.0!)
        Me.txtPassword.Properties.Appearance.ForeColor = System.Drawing.Color.Gainsboro
        Me.txtPassword.Properties.Appearance.Options.UseBackColor = True
        Me.txtPassword.Properties.Appearance.Options.UseFont = True
        Me.txtPassword.Properties.Appearance.Options.UseForeColor = True
        Me.txtPassword.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.txtPassword.Properties.NullValuePrompt = "Enter your host IMAP user name password"
        Me.txtPassword.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txtPassword.Properties.PasswordChar = Global.Microsoft.VisualBasic.ChrW(8226)
        Me.txtPassword.Size = New System.Drawing.Size(442, 26)
        Me.txtPassword.TabIndex = 3
        '
        'PictureBox4
        '
        Me.PictureBox4.Image = CType(resources.GetObject("PictureBox4.Image"), System.Drawing.Image)
        Me.PictureBox4.Location = New System.Drawing.Point(57, 538)
        Me.PictureBox4.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.PictureBox4.Name = "PictureBox4"
        Me.PictureBox4.Size = New System.Drawing.Size(460, 41)
        Me.PictureBox4.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage
        Me.PictureBox4.TabIndex = 449
        Me.PictureBox4.TabStop = False
        '
        'Label8
        '
        Me.Label8.AutoSize = True
        Me.Label8.Font = New System.Drawing.Font("Comfortaa", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label8.ForeColor = System.Drawing.Color.Salmon
        Me.Label8.Location = New System.Drawing.Point(59, 510)
        Me.Label8.Name = "Label8"
        Me.Label8.Size = New System.Drawing.Size(18, 26)
        Me.Label8.TabIndex = 451
        Me.Label8.Text = "*"
        '
        'Label9
        '
        Me.Label9.AutoSize = True
        Me.Label9.Font = New System.Drawing.Font("Comfortaa", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label9.ForeColor = System.Drawing.Color.LightSkyBlue
        Me.Label9.Location = New System.Drawing.Point(76, 508)
        Me.Label9.Name = "Label9"
        Me.Label9.Size = New System.Drawing.Size(99, 26)
        Me.Label9.TabIndex = 450
        Me.Label9.Text = "Password :"
        '
        'CheckEdit1
        '
        Me.CheckEdit1.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.CheckEdit1.Cursor = System.Windows.Forms.Cursors.Hand
        Me.CheckEdit1.Location = New System.Drawing.Point(486, 546)
        Me.CheckEdit1.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.CheckEdit1.Name = "CheckEdit1"
        Me.CheckEdit1.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.CheckEdit1.Properties.Appearance.ForeColor = System.Drawing.Color.Gray
        Me.CheckEdit1.Properties.Appearance.Options.UseBackColor = True
        Me.CheckEdit1.Properties.Appearance.Options.UseForeColor = True
        Me.CheckEdit1.Properties.Caption = "View password"
        Me.CheckEdit1.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Style15
        Me.CheckEdit1.Size = New System.Drawing.Size(16, 22)
        Me.CheckEdit1.TabIndex = 7
        '
        'BNT_New
        '
        Me.BNT_New.AllowFocus = False
        Me.BNT_New.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.BNT_New.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(32, Byte), Integer), CType(CType(95, Byte), Integer), CType(CType(95, Byte), Integer))
        Me.BNT_New.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.8!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.BNT_New.Appearance.Options.UseBackColor = True
        Me.BNT_New.Appearance.Options.UseFont = True
        Me.BNT_New.AppearanceDisabled.Font = New System.Drawing.Font("Comfortaa", 10.8!)
        Me.BNT_New.AppearanceDisabled.ForeColor = System.Drawing.Color.DimGray
        Me.BNT_New.AppearanceDisabled.Options.UseFont = True
        Me.BNT_New.AppearanceDisabled.Options.UseForeColor = True
        Me.BNT_New.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.BNT_New.AppearanceHovered.Font = New System.Drawing.Font("Comfortaa", 10.8!)
        Me.BNT_New.AppearanceHovered.Options.UseBackColor = True
        Me.BNT_New.AppearanceHovered.Options.UseFont = True
        Me.BNT_New.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.BNT_New.AppearancePressed.Options.UseBackColor = True
        Me.BNT_New.Location = New System.Drawing.Point(135, 632)
        Me.BNT_New.LookAndFeel.SkinName = "Darkroom"
        Me.BNT_New.LookAndFeel.UseDefaultLookAndFeel = False
        Me.BNT_New.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.BNT_New.Name = "BNT_New"
        Me.BNT_New.Size = New System.Drawing.Size(130, 40)
        Me.BNT_New.TabIndex = 6
        Me.BNT_New.Text = "&New"
        '
        'BNT_CheckEmail
        '
        Me.BNT_CheckEmail.AllowFocus = False
        Me.BNT_CheckEmail.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.BNT_CheckEmail.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(32, Byte), Integer), CType(CType(95, Byte), Integer), CType(CType(95, Byte), Integer))
        Me.BNT_CheckEmail.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.8!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.BNT_CheckEmail.Appearance.Options.UseBackColor = True
        Me.BNT_CheckEmail.Appearance.Options.UseFont = True
        Me.BNT_CheckEmail.AppearanceDisabled.Font = New System.Drawing.Font("Comfortaa", 10.8!)
        Me.BNT_CheckEmail.AppearanceDisabled.ForeColor = System.Drawing.Color.DimGray
        Me.BNT_CheckEmail.AppearanceDisabled.Options.UseFont = True
        Me.BNT_CheckEmail.AppearanceDisabled.Options.UseForeColor = True
        Me.BNT_CheckEmail.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.BNT_CheckEmail.AppearanceHovered.Font = New System.Drawing.Font("Comfortaa", 10.8!)
        Me.BNT_CheckEmail.AppearanceHovered.Options.UseBackColor = True
        Me.BNT_CheckEmail.AppearanceHovered.Options.UseFont = True
        Me.BNT_CheckEmail.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.BNT_CheckEmail.AppearancePressed.Options.UseBackColor = True
        Me.BNT_CheckEmail.Location = New System.Drawing.Point(270, 632)
        Me.BNT_CheckEmail.LookAndFeel.SkinName = "Darkroom"
        Me.BNT_CheckEmail.LookAndFeel.UseDefaultLookAndFeel = False
        Me.BNT_CheckEmail.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.BNT_CheckEmail.Name = "BNT_CheckEmail"
        Me.BNT_CheckEmail.Size = New System.Drawing.Size(130, 40)
        Me.BNT_CheckEmail.TabIndex = 4
        Me.BNT_CheckEmail.Text = "&Check Email"
        '
        'BNT_Connect
        '
        Me.BNT_Connect.AllowFocus = False
        Me.BNT_Connect.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.BNT_Connect.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(40, Byte), Integer), CType(CType(42, Byte), Integer), CType(CType(59, Byte), Integer))
        Me.BNT_Connect.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.8!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.BNT_Connect.Appearance.Options.UseBackColor = True
        Me.BNT_Connect.Appearance.Options.UseFont = True
        Me.BNT_Connect.AppearanceDisabled.Font = New System.Drawing.Font("Comfortaa", 10.8!)
        Me.BNT_Connect.AppearanceDisabled.ForeColor = System.Drawing.Color.DimGray
        Me.BNT_Connect.AppearanceDisabled.Options.UseFont = True
        Me.BNT_Connect.AppearanceDisabled.Options.UseForeColor = True
        Me.BNT_Connect.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.BNT_Connect.AppearanceHovered.Font = New System.Drawing.Font("Comfortaa", 10.8!)
        Me.BNT_Connect.AppearanceHovered.Options.UseBackColor = True
        Me.BNT_Connect.AppearanceHovered.Options.UseFont = True
        Me.BNT_Connect.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.BNT_Connect.AppearancePressed.Options.UseBackColor = True
        Me.BNT_Connect.Location = New System.Drawing.Point(640, 604)
        Me.BNT_Connect.LookAndFeel.SkinName = "Darkroom"
        Me.BNT_Connect.LookAndFeel.UseDefaultLookAndFeel = False
        Me.BNT_Connect.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.BNT_Connect.Name = "BNT_Connect"
        Me.BNT_Connect.Size = New System.Drawing.Size(130, 40)
        Me.BNT_Connect.TabIndex = 5
        Me.BNT_Connect.Text = "&Connect"
        '
        'PictureBox5
        '
        Me.PictureBox5.Image = CType(resources.GetObject("PictureBox5.Image"), System.Drawing.Image)
        Me.PictureBox5.Location = New System.Drawing.Point(592, 165)
        Me.PictureBox5.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.PictureBox5.Name = "PictureBox5"
        Me.PictureBox5.Size = New System.Drawing.Size(353, 414)
        Me.PictureBox5.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage
        Me.PictureBox5.TabIndex = 452
        Me.PictureBox5.TabStop = False
        '
        'Label10
        '
        Me.Label10.AutoSize = True
        Me.Label10.Font = New System.Drawing.Font("Comfortaa", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label10.ForeColor = System.Drawing.Color.LightSkyBlue
        Me.Label10.Location = New System.Drawing.Point(607, 133)
        Me.Label10.Name = "Label10"
        Me.Label10.Size = New System.Drawing.Size(128, 26)
        Me.Label10.TabIndex = 454
        Me.Label10.Text = "Mails List of  :"
        '
        'SimpleButton1
        '
        Me.SimpleButton1.AllowFocus = False
        Me.SimpleButton1.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton1.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(40, Byte), Integer), CType(CType(42, Byte), Integer), CType(CType(59, Byte), Integer))
        Me.SimpleButton1.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.8!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton1.Appearance.Options.UseBackColor = True
        Me.SimpleButton1.Appearance.Options.UseFont = True
        Me.SimpleButton1.AppearanceDisabled.Font = New System.Drawing.Font("Comfortaa", 10.8!)
        Me.SimpleButton1.AppearanceDisabled.ForeColor = System.Drawing.Color.DimGray
        Me.SimpleButton1.AppearanceDisabled.Options.UseFont = True
        Me.SimpleButton1.AppearanceDisabled.Options.UseForeColor = True
        Me.SimpleButton1.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.SimpleButton1.AppearanceHovered.Font = New System.Drawing.Font("Comfortaa", 10.8!)
        Me.SimpleButton1.AppearanceHovered.Options.UseBackColor = True
        Me.SimpleButton1.AppearanceHovered.Options.UseFont = True
        Me.SimpleButton1.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.SimpleButton1.AppearancePressed.Options.UseBackColor = True
        Me.SimpleButton1.Location = New System.Drawing.Point(775, 604)
        Me.SimpleButton1.LookAndFeel.SkinName = "Darkroom"
        Me.SimpleButton1.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton1.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.SimpleButton1.Name = "SimpleButton1"
        Me.SimpleButton1.Size = New System.Drawing.Size(130, 40)
        Me.SimpleButton1.TabIndex = 456
        Me.SimpleButton1.Text = "&Save List"
        '
        'txtMailNumber
        '
        Me.txtMailNumber.EditValue = "0"
        Me.txtMailNumber.Location = New System.Drawing.Point(738, 133)
        Me.txtMailNumber.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.txtMailNumber.Name = "txtMailNumber"
        Me.txtMailNumber.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(40, Byte), Integer), CType(CType(42, Byte), Integer), CType(CType(59, Byte), Integer))
        Me.txtMailNumber.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtMailNumber.Properties.Appearance.ForeColor = System.Drawing.Color.Gainsboro
        Me.txtMailNumber.Properties.Appearance.Options.UseBackColor = True
        Me.txtMailNumber.Properties.Appearance.Options.UseFont = True
        Me.txtMailNumber.Properties.Appearance.Options.UseForeColor = True
        Me.txtMailNumber.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.txtMailNumber.Properties.MaskSettings.Set("MaskManagerType", GetType(DevExpress.Data.Mask.NumericMaskManager))
        Me.txtMailNumber.Properties.MaskSettings.Set("mask", "d")
        Me.txtMailNumber.Properties.NullValuePrompt = "Enter your host IMAP name"
        Me.txtMailNumber.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txtMailNumber.Size = New System.Drawing.Size(193, 30)
        Me.txtMailNumber.TabIndex = 457
        '
        'DxErrorProvider1
        '
        Me.DxErrorProvider1.ContainerControl = Me
        '
        'picStatus
        '
        Me.picStatus.Image = CType(resources.GetObject("picStatus.Image"), System.Drawing.Image)
        Me.picStatus.Location = New System.Drawing.Point(181, 141)
        Me.picStatus.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.picStatus.Name = "picStatus"
        Me.picStatus.Size = New System.Drawing.Size(26, 25)
        Me.picStatus.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage
        Me.picStatus.TabIndex = 458
        Me.picStatus.TabStop = False
        Me.picStatus.Visible = False
        '
        'txtMailList
        '
        Me.txtMailList.EditValue = ""
        Me.txtMailList.Location = New System.Drawing.Point(608, 181)
        Me.txtMailList.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.txtMailList.Name = "txtMailList"
        Me.txtMailList.Properties.AllowFocused = False
        Me.txtMailList.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.txtMailList.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtMailList.Properties.Appearance.ForeColor = System.Drawing.Color.Gainsboro
        Me.txtMailList.Properties.Appearance.Options.UseBackColor = True
        Me.txtMailList.Properties.Appearance.Options.UseFont = True
        Me.txtMailList.Properties.Appearance.Options.UseForeColor = True
        Me.txtMailList.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.txtMailList.Properties.LookAndFeel.SkinName = "Sharp Plus"
        Me.txtMailList.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.txtMailList.Properties.Padding = New System.Windows.Forms.Padding(15)
        Me.txtMailList.Properties.ReadOnly = True
        Me.txtMailList.Size = New System.Drawing.Size(323, 390)
        Me.txtMailList.TabIndex = 459
        '
        'CheckSSL
        '
        Me.CheckSSL.Location = New System.Drawing.Point(316, 585)
        Me.CheckSSL.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.CheckSSL.Name = "CheckSSL"
        Me.CheckSSL.Properties.AllowFocused = False
        Me.CheckSSL.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.CheckSSL.Properties.Appearance.ForeColor = System.Drawing.Color.Gray
        Me.CheckSSL.Properties.Appearance.Options.UseFont = True
        Me.CheckSSL.Properties.Appearance.Options.UseForeColor = True
        Me.CheckSSL.Properties.Caption = " Enable SSL during testing."
        Me.CheckSSL.Properties.LookAndFeel.SkinName = "Dark Side"
        Me.CheckSSL.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.CheckSSL.Size = New System.Drawing.Size(201, 23)
        Me.CheckSSL.TabIndex = 460
        '
        'BackgroundWorker2
        '
        Me.BackgroundWorker2.WorkerReportsProgress = True
        Me.BackgroundWorker2.WorkerSupportsCancellation = True
        '
        'Panel1
        '
        Me.Panel1.Controls.Add(Me.SimpleButton2)
        Me.Panel1.Controls.Add(Me.ProgressPanel1)
        Me.Panel1.Location = New System.Drawing.Point(592, 587)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(353, 100)
        Me.Panel1.TabIndex = 462
        '
        'SimpleButton2
        '
        Me.SimpleButton2.AllowFocus = False
        Me.SimpleButton2.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton2.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(32, Byte), Integer), CType(CType(95, Byte), Integer), CType(CType(95, Byte), Integer))
        Me.SimpleButton2.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.8!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton2.Appearance.Options.UseBackColor = True
        Me.SimpleButton2.Appearance.Options.UseFont = True
        Me.SimpleButton2.AppearanceDisabled.Font = New System.Drawing.Font("Comfortaa", 10.8!)
        Me.SimpleButton2.AppearanceDisabled.ForeColor = System.Drawing.Color.DimGray
        Me.SimpleButton2.AppearanceDisabled.Options.UseFont = True
        Me.SimpleButton2.AppearanceDisabled.Options.UseForeColor = True
        Me.SimpleButton2.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.SimpleButton2.AppearanceHovered.Font = New System.Drawing.Font("Comfortaa", 10.8!)
        Me.SimpleButton2.AppearanceHovered.Options.UseBackColor = True
        Me.SimpleButton2.AppearanceHovered.Options.UseFont = True
        Me.SimpleButton2.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.SimpleButton2.AppearancePressed.Options.UseBackColor = True
        Me.SimpleButton2.Location = New System.Drawing.Point(214, 30)
        Me.SimpleButton2.LookAndFeel.SkinName = "Darkroom"
        Me.SimpleButton2.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton2.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.SimpleButton2.Name = "SimpleButton2"
        Me.SimpleButton2.Size = New System.Drawing.Size(103, 40)
        Me.SimpleButton2.TabIndex = 463
        Me.SimpleButton2.Text = "&Stop"
        '
        'ProgressPanel1
        '
        Me.ProgressPanel1.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(40, Byte), Integer), CType(CType(42, Byte), Integer), CType(CType(59, Byte), Integer))
        Me.ProgressPanel1.Appearance.Options.UseBackColor = True
        Me.ProgressPanel1.AppearanceCaption.ForeColor = System.Drawing.Color.White
        Me.ProgressPanel1.AppearanceCaption.Options.UseForeColor = True
        Me.ProgressPanel1.AppearanceDescription.ForeColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(192, Byte), Integer))
        Me.ProgressPanel1.AppearanceDescription.Options.UseForeColor = True
        Me.ProgressPanel1.Caption = "Please Wait..."
        Me.ProgressPanel1.CaptionToDescriptionDistance = 10
        Me.ProgressPanel1.ContentAlignment = System.Drawing.ContentAlignment.MiddleCenter
        Me.ProgressPanel1.Description = "0 out of 0 Found"
        Me.ProgressPanel1.Location = New System.Drawing.Point(3, 10)
        Me.ProgressPanel1.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ProgressPanel1.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.ProgressPanel1.Name = "ProgressPanel1"
        Me.ProgressPanel1.Size = New System.Drawing.Size(205, 78)
        Me.ProgressPanel1.TabIndex = 462
        Me.ProgressPanel1.Text = "ProgressPanel2"
        '
        'BackgroundWorker1
        '
        Me.BackgroundWorker1.WorkerReportsProgress = True
        Me.BackgroundWorker1.WorkerSupportsCancellation = True
        '
        'cbTags
        '
        Me.cbTags.EditValue = ""
        Me.cbTags.Location = New System.Drawing.Point(252, 126)
        Me.cbTags.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.cbTags.Name = "cbTags"
        Me.cbTags.Properties.AllowFocused = False
        Me.cbTags.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.cbTags.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.cbTags.Properties.Appearance.ForeColor = System.Drawing.Color.White
        Me.cbTags.Properties.Appearance.Options.UseBackColor = True
        Me.cbTags.Properties.Appearance.Options.UseFont = True
        Me.cbTags.Properties.Appearance.Options.UseForeColor = True
        Me.cbTags.Properties.AppearanceDropDown.BackColor = System.Drawing.Color.FromArgb(CType(CType(32, Byte), Integer), CType(CType(44, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.cbTags.Properties.AppearanceDropDown.ForeColor = System.Drawing.Color.White
        Me.cbTags.Properties.AppearanceDropDown.Options.UseBackColor = True
        Me.cbTags.Properties.AppearanceDropDown.Options.UseForeColor = True
        Me.cbTags.Properties.AppearanceFocused.ForeColor = System.Drawing.Color.White
        Me.cbTags.Properties.AppearanceFocused.Options.UseForeColor = True
        Me.cbTags.Properties.AppearanceItemHighlight.BackColor = System.Drawing.Color.DarkSlateGray
        Me.cbTags.Properties.AppearanceItemHighlight.ForeColor = System.Drawing.Color.White
        Me.cbTags.Properties.AppearanceItemHighlight.Options.UseBackColor = True
        Me.cbTags.Properties.AppearanceItemHighlight.Options.UseForeColor = True
        Me.cbTags.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.cbTags.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cbTags.Properties.DropDownRows = 20
        Me.cbTags.Properties.Items.AddRange(New Object() {"Deleted", "inbox", "Junk Email"})
        Me.cbTags.Properties.LookAndFeel.SkinName = "Sharp"
        Me.cbTags.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.cbTags.Properties.NullValuePrompt = "Extract Mail From..."
        Me.cbTags.Properties.Sorted = True
        Me.cbTags.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.cbTags.Size = New System.Drawing.Size(263, 22)
        Me.cbTags.TabIndex = 468
        '
        'Label12
        '
        Me.Label12.Font = New System.Drawing.Font("Comfortaa", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.Label12.ForeColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer))
        Me.Label12.Location = New System.Drawing.Point(233, 103)
        Me.Label12.Name = "Label12"
        Me.Label12.Size = New System.Drawing.Size(316, 18)
        Me.Label12.TabIndex = 467
        Me.Label12.Text = "Extract mail from |inbox | Deleted | Junk Email"
        Me.Label12.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'SeparatorControl3
        '
        Me.SeparatorControl3.LineThickness = 1
        Me.SeparatorControl3.Location = New System.Drawing.Point(46, 77)
        Me.SeparatorControl3.LookAndFeel.SkinName = "DevExpress Style"
        Me.SeparatorControl3.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SeparatorControl3.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.SeparatorControl3.Name = "SeparatorControl3"
        Me.SeparatorControl3.Padding = New System.Windows.Forms.Padding(8, 7, 8, 7)
        Me.SeparatorControl3.Size = New System.Drawing.Size(249, 19)
        Me.SeparatorControl3.TabIndex = 469
        '
        'SeparatorControl4
        '
        Me.SeparatorControl4.LineThickness = 1
        Me.SeparatorControl4.Location = New System.Drawing.Point(638, 99)
        Me.SeparatorControl4.LookAndFeel.SkinName = "DevExpress Style"
        Me.SeparatorControl4.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SeparatorControl4.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.SeparatorControl4.Name = "SeparatorControl4"
        Me.SeparatorControl4.Padding = New System.Windows.Forms.Padding(8, 7, 8, 7)
        Me.SeparatorControl4.Size = New System.Drawing.Size(249, 19)
        Me.SeparatorControl4.TabIndex = 471
        '
        'Label11
        '
        Me.Label11.AutoSize = True
        Me.Label11.Font = New System.Drawing.Font("Audiowide", 14.25!, System.Drawing.FontStyle.Bold)
        Me.Label11.ForeColor = System.Drawing.Color.White
        Me.Label11.Location = New System.Drawing.Point(623, 77)
        Me.Label11.Name = "Label11"
        Me.Label11.Size = New System.Drawing.Size(286, 24)
        Me.Label11.TabIndex = 470
        Me.Label11.Text = "www.BestSendervip.com"
        '
        'frmIMAP
        '
        Me.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(40, Byte), Integer), CType(CType(42, Byte), Integer), CType(CType(59, Byte), Integer))
        Me.Appearance.Options.UseBackColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(999, 695)
        Me.Controls.Add(Me.SeparatorControl4)
        Me.Controls.Add(Me.Label11)
        Me.Controls.Add(Me.SeparatorControl3)
        Me.Controls.Add(Me.cbTags)
        Me.Controls.Add(Me.Label12)
        Me.Controls.Add(Me.Panel1)
        Me.Controls.Add(Me.CheckSSL)
        Me.Controls.Add(Me.txtMailList)
        Me.Controls.Add(Me.picStatus)
        Me.Controls.Add(Me.SimpleButton1)
        Me.Controls.Add(Me.Label10)
        Me.Controls.Add(Me.PictureBox5)
        Me.Controls.Add(Me.CheckEdit1)
        Me.Controls.Add(Me.TextEmail)
        Me.Controls.Add(Me.TextPort)
        Me.Controls.Add(Me.TextHost)
        Me.Controls.Add(Me.BNT_New)
        Me.Controls.Add(Me.BNT_CheckEmail)
        Me.Controls.Add(Me.BNT_Connect)
        Me.Controls.Add(Me.txtPassword)
        Me.Controls.Add(Me.PictureBox3)
        Me.Controls.Add(Me.Label6)
        Me.Controls.Add(Me.Label7)
        Me.Controls.Add(Me.PictureBox2)
        Me.Controls.Add(Me.Label4)
        Me.Controls.Add(Me.Label5)
        Me.Controls.Add(Me.PictureBox1)
        Me.Controls.Add(Me.Label3)
        Me.Controls.Add(Me.Label2)
        Me.Controls.Add(Me.BunifuImageButton7)
        Me.Controls.Add(Me.Label1)
        Me.Controls.Add(Me.PictureBox4)
        Me.Controls.Add(Me.Label8)
        Me.Controls.Add(Me.Label9)
        Me.Controls.Add(Me.txtMailNumber)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle
        Me.IconOptions.Image = Global.Best_Sender.My.Resources.Resources.Logo_NewBestSender
        Me.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.Name = "frmIMAP"
        Me.Opacity = 0R
        Me.ShowInTaskbar = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "frmLetterEncoder"
        CType(Me.BunifuImageButton7, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RadialMenu1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextHost.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextPort.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureBox2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEmail.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureBox3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtPassword.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureBox4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckEdit1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureBox5, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtMailNumber.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DxErrorProvider1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.picStatus, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtMailList.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckSSL.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Panel1.ResumeLayout(False)
        CType(Me.cbTags.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SeparatorControl3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SeparatorControl4, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()
    End Sub
    Friend WithEvents trmfadein As Timer
    Friend WithEvents trmfadeout As Timer
    Friend WithEvents Label1 As Label
    Friend WithEvents BunifuImageButton7 As Bunifu.Framework.UI.BunifuImageButton
    Friend WithEvents RadialMenu1 As DevExpress.XtraBars.Ribbon.RadialMenu
    Friend WithEvents ToolTip1 As ToolTip
    Friend WithEvents PictureBox1 As PictureBox
    Friend WithEvents Label2 As Label
    Friend WithEvents Label3 As Label
    Friend WithEvents TextHost As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TextPort As DevExpress.XtraEditors.TextEdit
    Friend WithEvents PictureBox2 As PictureBox
    Friend WithEvents Label4 As Label
    Friend WithEvents Label5 As Label
    Friend WithEvents TextEmail As DevExpress.XtraEditors.TextEdit
    Friend WithEvents PictureBox3 As PictureBox
    Friend WithEvents Label6 As Label
    Friend WithEvents Label7 As Label
    Friend WithEvents txtPassword As DevExpress.XtraEditors.TextEdit
    Friend WithEvents PictureBox4 As PictureBox
    Friend WithEvents Label8 As Label
    Friend WithEvents Label9 As Label
    Friend WithEvents CheckEdit1 As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents BNT_New As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents BNT_CheckEmail As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents BNT_Connect As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents PictureBox5 As PictureBox
    Friend WithEvents Label10 As Label
    Friend WithEvents SimpleButton1 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents txtMailNumber As DevExpress.XtraEditors.TextEdit
    Friend WithEvents DxErrorProvider1 As DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider
    Friend WithEvents picStatus As PictureBox
    Friend WithEvents txtMailList As DevExpress.XtraEditors.MemoEdit
    Friend WithEvents CheckSSL As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents BackgroundWorker2 As System.ComponentModel.BackgroundWorker
    Friend WithEvents Panel1 As Panel
    Friend WithEvents ProgressPanel1 As DevExpress.XtraWaitForm.ProgressPanel
    Friend WithEvents SimpleButton2 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents BackgroundWorker1 As System.ComponentModel.BackgroundWorker
    Friend WithEvents cbTags As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Label12 As Label
    Friend WithEvents SeparatorControl3 As DevExpress.XtraEditors.SeparatorControl
    Friend WithEvents SeparatorControl4 As DevExpress.XtraEditors.SeparatorControl
    Friend WithEvents Label11 As Label
End Class
