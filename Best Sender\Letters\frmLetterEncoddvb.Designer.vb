﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class frmLetterEncoddvb
    Inherits DevExpress.XtraEditors.XtraForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.LayoutControl1 = New DevExpress.XtraLayout.LayoutControl()
        Me.lblStatus = New System.Windows.Forms.Label()
        Me.ProgressBarControl1 = New DevExpress.XtraEditors.ProgressBarControl()
        Me.XtraTabControl1 = New DevExpress.XtraTab.XtraTabControl()
        Me.XtraTabPage1 = New DevExpress.XtraTab.XtraTabPage()
        Me.LayoutControl3 = New DevExpress.XtraLayout.LayoutControl()
        Me.textSershNormal = New DevExpress.XtraEditors.TextEdit()
        Me.bntClearNormal = New DevExpress.XtraEditors.SimpleButton()
        Me.BntSerchNormal = New DevExpress.XtraEditors.SimpleButton()
        Me.LetterEncoder_Richtext = New System.Windows.Forms.RichTextBox()
        Me.BntClearAll = New DevExpress.XtraEditors.SimpleButton()
        Me.BntAddLetter = New DevExpress.XtraEditors.SimpleButton()
        Me.LetterEncoder_txtFilepath = New DevExpress.XtraEditors.TextEdit()
        Me.LayoutControlGroup2 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem3 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.EmptySpaceItem2 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.LayoutControlItem4 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem5 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem6 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem15 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem14 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem20 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.XtraTabPage2 = New DevExpress.XtraTab.XtraTabPage()
        Me.LayoutControl4 = New DevExpress.XtraLayout.LayoutControl()
        Me.bntSendToSender = New DevExpress.XtraEditors.SimpleButton()
        Me.txtSearch2 = New DevExpress.XtraEditors.TextEdit()
        Me.bntSerch2 = New DevExpress.XtraEditors.SimpleButton()
        Me.bntCLear2 = New DevExpress.XtraEditors.SimpleButton()
        Me.LetterEncoder_Richtext2 = New System.Windows.Forms.RichTextBox()
        Me.bntSaveLetter = New DevExpress.XtraEditors.SimpleButton()
        Me.bntEncodedLetter = New DevExpress.XtraEditors.SimpleButton()
        Me.CheckFrench = New DevExpress.XtraEditors.CheckEdit()
        Me.CheckRussian = New DevExpress.XtraEditors.CheckEdit()
        Me.CheckHindi = New DevExpress.XtraEditors.CheckEdit()
        Me.CheckArabic = New DevExpress.XtraEditors.CheckEdit()
        Me.CheckJapanese = New DevExpress.XtraEditors.CheckEdit()
        Me.cmbEncryption = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.LayoutControlGroup3 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem7 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.EmptySpaceItem1 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.LayoutControlItem8 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem9 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem10 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem11 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem12 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem13 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem16 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem17 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem19 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem18 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem22 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem21 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.WebBrowser1 = New System.Windows.Forms.WebBrowser()
        Me.Root = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem1 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem2 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem23 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem24 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.BackgroundWorker1 = New System.ComponentModel.BackgroundWorker()
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.LayoutControl1.SuspendLayout()
        CType(Me.ProgressBarControl1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.XtraTabControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.XtraTabControl1.SuspendLayout()
        Me.XtraTabPage1.SuspendLayout()
        CType(Me.LayoutControl3, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.LayoutControl3.SuspendLayout()
        CType(Me.textSershNormal.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LetterEncoder_txtFilepath.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem6, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem15, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem14, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem20, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.XtraTabPage2.SuspendLayout()
        CType(Me.LayoutControl4, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.LayoutControl4.SuspendLayout()
        CType(Me.txtSearch2.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckFrench.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckRussian.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckHindi.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckArabic.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckJapanese.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbEncryption.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem7, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem8, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem9, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem10, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem11, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem12, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem13, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem16, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem17, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem19, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem18, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem22, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem21, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Root, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem23, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem24, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'LayoutControl1
        '
        Me.LayoutControl1.Controls.Add(Me.lblStatus)
        Me.LayoutControl1.Controls.Add(Me.ProgressBarControl1)
        Me.LayoutControl1.Controls.Add(Me.XtraTabControl1)
        Me.LayoutControl1.Controls.Add(Me.WebBrowser1)
        Me.LayoutControl1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.LayoutControl1.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControl1.Name = "LayoutControl1"
        Me.LayoutControl1.Root = Me.Root
        Me.LayoutControl1.Size = New System.Drawing.Size(972, 602)
        Me.LayoutControl1.TabIndex = 0
        Me.LayoutControl1.Text = "LayoutControl1"
        '
        'lblStatus
        '
        Me.lblStatus.Location = New System.Drawing.Point(506, 16)
        Me.lblStatus.Name = "lblStatus"
        Me.lblStatus.Size = New System.Drawing.Size(450, 20)
        Me.lblStatus.TabIndex = 524
        '
        'ProgressBarControl1
        '
        Me.ProgressBarControl1.Location = New System.Drawing.Point(16, 562)
        Me.ProgressBarControl1.Name = "ProgressBarControl1"
        Me.ProgressBarControl1.Properties.ShowTitle = True
        Me.ProgressBarControl1.Size = New System.Drawing.Size(940, 24)
        Me.ProgressBarControl1.StyleController = Me.LayoutControl1
        Me.ProgressBarControl1.TabIndex = 523
        '
        'XtraTabControl1
        '
        Me.XtraTabControl1.AppearancePage.Header.BackColor = System.Drawing.Color.FromArgb(CType(CType(96, Byte), Integer), CType(CType(96, Byte), Integer), CType(CType(96, Byte), Integer))
        Me.XtraTabControl1.AppearancePage.Header.Options.UseBackColor = True
        Me.XtraTabControl1.AppearancePage.HeaderActive.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.XtraTabControl1.AppearancePage.HeaderActive.Options.UseBackColor = True
        Me.XtraTabControl1.Location = New System.Drawing.Point(16, 16)
        Me.XtraTabControl1.Name = "XtraTabControl1"
        Me.XtraTabControl1.SelectedTabPage = Me.XtraTabPage1
        Me.XtraTabControl1.Size = New System.Drawing.Size(484, 540)
        Me.XtraTabControl1.TabIndex = 521
        Me.XtraTabControl1.TabPages.AddRange(New DevExpress.XtraTab.XtraTabPage() {Me.XtraTabPage1, Me.XtraTabPage2})
        '
        'XtraTabPage1
        '
        Me.XtraTabPage1.Controls.Add(Me.LayoutControl3)
        Me.XtraTabPage1.Name = "XtraTabPage1"
        Me.XtraTabPage1.Size = New System.Drawing.Size(482, 509)
        Me.XtraTabPage1.Text = "Normal"
        '
        'LayoutControl3
        '
        Me.LayoutControl3.Controls.Add(Me.textSershNormal)
        Me.LayoutControl3.Controls.Add(Me.bntClearNormal)
        Me.LayoutControl3.Controls.Add(Me.BntSerchNormal)
        Me.LayoutControl3.Controls.Add(Me.LetterEncoder_Richtext)
        Me.LayoutControl3.Controls.Add(Me.BntClearAll)
        Me.LayoutControl3.Controls.Add(Me.BntAddLetter)
        Me.LayoutControl3.Controls.Add(Me.LetterEncoder_txtFilepath)
        Me.LayoutControl3.Dock = System.Windows.Forms.DockStyle.Fill
        Me.LayoutControl3.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControl3.Name = "LayoutControl3"
        Me.LayoutControl3.Root = Me.LayoutControlGroup2
        Me.LayoutControl3.Size = New System.Drawing.Size(482, 509)
        Me.LayoutControl3.TabIndex = 0
        Me.LayoutControl3.Text = "LayoutControl3"
        '
        'textSershNormal
        '
        Me.textSershNormal.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.textSershNormal.EditValue = ""
        Me.textSershNormal.Location = New System.Drawing.Point(16, 463)
        Me.textSershNormal.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.textSershNormal.Name = "textSershNormal"
        Me.textSershNormal.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.textSershNormal.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.textSershNormal.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.textSershNormal.Properties.Appearance.Options.UseBackColor = True
        Me.textSershNormal.Properties.Appearance.Options.UseFont = True
        Me.textSershNormal.Properties.Appearance.Options.UseForeColor = True
        Me.textSershNormal.Properties.ContextImageOptions.Image = Global.Best_Sender.My.Resources.Resources.search_16x16
        Me.textSershNormal.Properties.NullValuePrompt = "Type Word To Find"
        Me.textSershNormal.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.textSershNormal.Size = New System.Drawing.Size(450, 30)
        Me.textSershNormal.StyleController = Me.LayoutControl3
        Me.textSershNormal.TabIndex = 78
        '
        'bntClearNormal
        '
        Me.bntClearNormal.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntClearNormal.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntClearNormal.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.bntClearNormal.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntClearNormal.Appearance.Options.UseBackColor = True
        Me.bntClearNormal.Appearance.Options.UseBorderColor = True
        Me.bntClearNormal.Appearance.Options.UseFont = True
        Me.bntClearNormal.Appearance.Options.UseForeColor = True
        Me.bntClearNormal.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.bntClearNormal.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.bntClearNormal.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.bntClearNormal.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.bntClearNormal.AppearanceDisabled.Options.UseBackColor = True
        Me.bntClearNormal.AppearanceDisabled.Options.UseBorderColor = True
        Me.bntClearNormal.AppearanceDisabled.Options.UseFont = True
        Me.bntClearNormal.AppearanceDisabled.Options.UseForeColor = True
        Me.bntClearNormal.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.bntClearNormal.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntClearNormal.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.bntClearNormal.AppearanceHovered.Options.UseBackColor = True
        Me.bntClearNormal.AppearanceHovered.Options.UseBorderColor = True
        Me.bntClearNormal.AppearanceHovered.Options.UseForeColor = True
        Me.bntClearNormal.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntClearNormal.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntClearNormal.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.bntClearNormal.AppearancePressed.Options.UseBackColor = True
        Me.bntClearNormal.AppearancePressed.Options.UseBorderColor = True
        Me.bntClearNormal.AppearancePressed.Options.UseForeColor = True
        Me.bntClearNormal.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_32x322
        Me.bntClearNormal.Location = New System.Drawing.Point(243, 419)
        Me.bntClearNormal.Name = "bntClearNormal"
        Me.bntClearNormal.Size = New System.Drawing.Size(223, 38)
        Me.bntClearNormal.StyleController = Me.LayoutControl3
        Me.bntClearNormal.TabIndex = 77
        Me.bntClearNormal.Text = "Clear"
        '
        'BntSerchNormal
        '
        Me.BntSerchNormal.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntSerchNormal.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntSerchNormal.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntSerchNormal.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntSerchNormal.Appearance.Options.UseBackColor = True
        Me.BntSerchNormal.Appearance.Options.UseBorderColor = True
        Me.BntSerchNormal.Appearance.Options.UseFont = True
        Me.BntSerchNormal.Appearance.Options.UseForeColor = True
        Me.BntSerchNormal.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntSerchNormal.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntSerchNormal.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntSerchNormal.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntSerchNormal.AppearanceDisabled.Options.UseBackColor = True
        Me.BntSerchNormal.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntSerchNormal.AppearanceDisabled.Options.UseFont = True
        Me.BntSerchNormal.AppearanceDisabled.Options.UseForeColor = True
        Me.BntSerchNormal.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntSerchNormal.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntSerchNormal.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntSerchNormal.AppearanceHovered.Options.UseBackColor = True
        Me.BntSerchNormal.AppearanceHovered.Options.UseBorderColor = True
        Me.BntSerchNormal.AppearanceHovered.Options.UseForeColor = True
        Me.BntSerchNormal.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntSerchNormal.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntSerchNormal.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntSerchNormal.AppearancePressed.Options.UseBackColor = True
        Me.BntSerchNormal.AppearancePressed.Options.UseBorderColor = True
        Me.BntSerchNormal.AppearancePressed.Options.UseForeColor = True
        Me.BntSerchNormal.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.search_32x32
        Me.BntSerchNormal.Location = New System.Drawing.Point(16, 419)
        Me.BntSerchNormal.Name = "BntSerchNormal"
        Me.BntSerchNormal.Size = New System.Drawing.Size(221, 38)
        Me.BntSerchNormal.StyleController = Me.LayoutControl3
        Me.BntSerchNormal.TabIndex = 76
        Me.BntSerchNormal.Text = "Search"
        '
        'LetterEncoder_Richtext
        '
        Me.LetterEncoder_Richtext.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.LetterEncoder_Richtext.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.LetterEncoder_Richtext.ForeColor = System.Drawing.Color.White
        Me.LetterEncoder_Richtext.Location = New System.Drawing.Point(16, 106)
        Me.LetterEncoder_Richtext.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.LetterEncoder_Richtext.Name = "LetterEncoder_Richtext"
        Me.LetterEncoder_Richtext.Size = New System.Drawing.Size(450, 286)
        Me.LetterEncoder_Richtext.TabIndex = 75
        Me.LetterEncoder_Richtext.Text = ""
        '
        'BntClearAll
        '
        Me.BntClearAll.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntClearAll.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntClearAll.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntClearAll.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntClearAll.Appearance.Options.UseBackColor = True
        Me.BntClearAll.Appearance.Options.UseBorderColor = True
        Me.BntClearAll.Appearance.Options.UseFont = True
        Me.BntClearAll.Appearance.Options.UseForeColor = True
        Me.BntClearAll.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntClearAll.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntClearAll.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntClearAll.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntClearAll.AppearanceDisabled.Options.UseBackColor = True
        Me.BntClearAll.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntClearAll.AppearanceDisabled.Options.UseFont = True
        Me.BntClearAll.AppearanceDisabled.Options.UseForeColor = True
        Me.BntClearAll.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntClearAll.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntClearAll.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntClearAll.AppearanceHovered.Options.UseBackColor = True
        Me.BntClearAll.AppearanceHovered.Options.UseBorderColor = True
        Me.BntClearAll.AppearanceHovered.Options.UseForeColor = True
        Me.BntClearAll.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntClearAll.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntClearAll.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntClearAll.AppearancePressed.Options.UseBackColor = True
        Me.BntClearAll.AppearancePressed.Options.UseBorderColor = True
        Me.BntClearAll.AppearancePressed.Options.UseForeColor = True
        Me.BntClearAll.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_32x322
        Me.BntClearAll.Location = New System.Drawing.Point(243, 52)
        Me.BntClearAll.Name = "BntClearAll"
        Me.BntClearAll.Size = New System.Drawing.Size(223, 38)
        Me.BntClearAll.StyleController = Me.LayoutControl3
        Me.BntClearAll.TabIndex = 74
        Me.BntClearAll.Text = "Clear All"
        '
        'BntAddLetter
        '
        Me.BntAddLetter.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntAddLetter.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntAddLetter.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntAddLetter.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntAddLetter.Appearance.Options.UseBackColor = True
        Me.BntAddLetter.Appearance.Options.UseBorderColor = True
        Me.BntAddLetter.Appearance.Options.UseFont = True
        Me.BntAddLetter.Appearance.Options.UseForeColor = True
        Me.BntAddLetter.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntAddLetter.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntAddLetter.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntAddLetter.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntAddLetter.AppearanceDisabled.Options.UseBackColor = True
        Me.BntAddLetter.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntAddLetter.AppearanceDisabled.Options.UseFont = True
        Me.BntAddLetter.AppearanceDisabled.Options.UseForeColor = True
        Me.BntAddLetter.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntAddLetter.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntAddLetter.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntAddLetter.AppearanceHovered.Options.UseBackColor = True
        Me.BntAddLetter.AppearanceHovered.Options.UseBorderColor = True
        Me.BntAddLetter.AppearanceHovered.Options.UseForeColor = True
        Me.BntAddLetter.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntAddLetter.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntAddLetter.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntAddLetter.AppearancePressed.Options.UseBackColor = True
        Me.BntAddLetter.AppearancePressed.Options.UseBorderColor = True
        Me.BntAddLetter.AppearancePressed.Options.UseForeColor = True
        Me.BntAddLetter.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.add32x32
        Me.BntAddLetter.Location = New System.Drawing.Point(16, 52)
        Me.BntAddLetter.Name = "BntAddLetter"
        Me.BntAddLetter.Size = New System.Drawing.Size(221, 38)
        Me.BntAddLetter.StyleController = Me.LayoutControl3
        Me.BntAddLetter.TabIndex = 73
        Me.BntAddLetter.Text = "Browse..."
        '
        'LetterEncoder_txtFilepath
        '
        Me.LetterEncoder_txtFilepath.EditValue = ""
        Me.LetterEncoder_txtFilepath.Location = New System.Drawing.Point(119, 16)
        Me.LetterEncoder_txtFilepath.Margin = New System.Windows.Forms.Padding(4, 1, 4, 1)
        Me.LetterEncoder_txtFilepath.Name = "LetterEncoder_txtFilepath"
        Me.LetterEncoder_txtFilepath.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.LetterEncoder_txtFilepath.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LetterEncoder_txtFilepath.Properties.Appearance.ForeColor = System.Drawing.Color.DarkGoldenrod
        Me.LetterEncoder_txtFilepath.Properties.Appearance.Options.UseBackColor = True
        Me.LetterEncoder_txtFilepath.Properties.Appearance.Options.UseFont = True
        Me.LetterEncoder_txtFilepath.Properties.Appearance.Options.UseForeColor = True
        Me.LetterEncoder_txtFilepath.Properties.NullValuePrompt = "Path File HTML"
        Me.LetterEncoder_txtFilepath.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.LetterEncoder_txtFilepath.Size = New System.Drawing.Size(347, 30)
        Me.LetterEncoder_txtFilepath.StyleController = Me.LayoutControl3
        Me.LetterEncoder_txtFilepath.TabIndex = 64
        '
        'LayoutControlGroup2
        '
        Me.LayoutControlGroup2.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.LayoutControlGroup2.GroupBordersVisible = False
        Me.LayoutControlGroup2.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem3, Me.EmptySpaceItem2, Me.LayoutControlItem4, Me.LayoutControlItem5, Me.LayoutControlItem6, Me.LayoutControlItem15, Me.LayoutControlItem14, Me.LayoutControlItem20})
        Me.LayoutControlGroup2.Name = "LayoutControlGroup2"
        Me.LayoutControlGroup2.Size = New System.Drawing.Size(482, 509)
        Me.LayoutControlGroup2.TextVisible = False
        '
        'LayoutControlItem3
        '
        Me.LayoutControlItem3.Control = Me.LetterEncoder_txtFilepath
        Me.LayoutControlItem3.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem3.Name = "LayoutControlItem3"
        Me.LayoutControlItem3.Size = New System.Drawing.Size(456, 36)
        Me.LayoutControlItem3.Text = "Path File HTML:"
        Me.LayoutControlItem3.TextSize = New System.Drawing.Size(87, 18)
        '
        'EmptySpaceItem2
        '
        Me.EmptySpaceItem2.Location = New System.Drawing.Point(0, 382)
        Me.EmptySpaceItem2.Name = "EmptySpaceItem2"
        Me.EmptySpaceItem2.Size = New System.Drawing.Size(456, 21)
        '
        'LayoutControlItem4
        '
        Me.LayoutControlItem4.Control = Me.BntAddLetter
        Me.LayoutControlItem4.Location = New System.Drawing.Point(0, 36)
        Me.LayoutControlItem4.Name = "LayoutControlItem4"
        Me.LayoutControlItem4.Size = New System.Drawing.Size(227, 44)
        Me.LayoutControlItem4.TextVisible = False
        '
        'LayoutControlItem5
        '
        Me.LayoutControlItem5.Control = Me.BntClearAll
        Me.LayoutControlItem5.Location = New System.Drawing.Point(227, 36)
        Me.LayoutControlItem5.Name = "LayoutControlItem5"
        Me.LayoutControlItem5.Size = New System.Drawing.Size(229, 44)
        Me.LayoutControlItem5.TextVisible = False
        '
        'LayoutControlItem6
        '
        Me.LayoutControlItem6.Control = Me.LetterEncoder_Richtext
        Me.LayoutControlItem6.Location = New System.Drawing.Point(0, 80)
        Me.LayoutControlItem6.Name = "LayoutControlItem6"
        Me.LayoutControlItem6.Size = New System.Drawing.Size(456, 302)
        Me.LayoutControlItem6.Spacing = New DevExpress.XtraLayout.Utils.Padding(0, 0, 10, 0)
        Me.LayoutControlItem6.TextVisible = False
        '
        'LayoutControlItem15
        '
        Me.LayoutControlItem15.Control = Me.BntSerchNormal
        Me.LayoutControlItem15.Location = New System.Drawing.Point(0, 403)
        Me.LayoutControlItem15.Name = "LayoutControlItem15"
        Me.LayoutControlItem15.Size = New System.Drawing.Size(227, 44)
        Me.LayoutControlItem15.TextVisible = False
        '
        'LayoutControlItem14
        '
        Me.LayoutControlItem14.Control = Me.bntClearNormal
        Me.LayoutControlItem14.Location = New System.Drawing.Point(227, 403)
        Me.LayoutControlItem14.Name = "LayoutControlItem14"
        Me.LayoutControlItem14.Size = New System.Drawing.Size(229, 44)
        Me.LayoutControlItem14.TextVisible = False
        '
        'LayoutControlItem20
        '
        Me.LayoutControlItem20.Control = Me.textSershNormal
        Me.LayoutControlItem20.Location = New System.Drawing.Point(0, 447)
        Me.LayoutControlItem20.Name = "LayoutControlItem20"
        Me.LayoutControlItem20.Size = New System.Drawing.Size(456, 36)
        Me.LayoutControlItem20.TextVisible = False
        '
        'XtraTabPage2
        '
        Me.XtraTabPage2.Controls.Add(Me.LayoutControl4)
        Me.XtraTabPage2.Name = "XtraTabPage2"
        Me.XtraTabPage2.Size = New System.Drawing.Size(482, 509)
        Me.XtraTabPage2.Text = "Letter Encoded"
        '
        'LayoutControl4
        '
        Me.LayoutControl4.Controls.Add(Me.bntSendToSender)
        Me.LayoutControl4.Controls.Add(Me.txtSearch2)
        Me.LayoutControl4.Controls.Add(Me.bntSerch2)
        Me.LayoutControl4.Controls.Add(Me.bntCLear2)
        Me.LayoutControl4.Controls.Add(Me.LetterEncoder_Richtext2)
        Me.LayoutControl4.Controls.Add(Me.bntSaveLetter)
        Me.LayoutControl4.Controls.Add(Me.bntEncodedLetter)
        Me.LayoutControl4.Controls.Add(Me.CheckFrench)
        Me.LayoutControl4.Controls.Add(Me.CheckRussian)
        Me.LayoutControl4.Controls.Add(Me.CheckHindi)
        Me.LayoutControl4.Controls.Add(Me.CheckArabic)
        Me.LayoutControl4.Controls.Add(Me.CheckJapanese)
        Me.LayoutControl4.Controls.Add(Me.cmbEncryption)
        Me.LayoutControl4.Dock = System.Windows.Forms.DockStyle.Fill
        Me.LayoutControl4.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControl4.Name = "LayoutControl4"
        Me.LayoutControl4.Root = Me.LayoutControlGroup3
        Me.LayoutControl4.Size = New System.Drawing.Size(482, 509)
        Me.LayoutControl4.TabIndex = 0
        Me.LayoutControl4.Text = "LayoutControl4"
        '
        'bntSendToSender
        '
        Me.bntSendToSender.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntSendToSender.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntSendToSender.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.bntSendToSender.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntSendToSender.Appearance.Options.UseBackColor = True
        Me.bntSendToSender.Appearance.Options.UseBorderColor = True
        Me.bntSendToSender.Appearance.Options.UseFont = True
        Me.bntSendToSender.Appearance.Options.UseForeColor = True
        Me.bntSendToSender.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.bntSendToSender.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.bntSendToSender.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.bntSendToSender.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.bntSendToSender.AppearanceDisabled.Options.UseBackColor = True
        Me.bntSendToSender.AppearanceDisabled.Options.UseBorderColor = True
        Me.bntSendToSender.AppearanceDisabled.Options.UseFont = True
        Me.bntSendToSender.AppearanceDisabled.Options.UseForeColor = True
        Me.bntSendToSender.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.bntSendToSender.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntSendToSender.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.bntSendToSender.AppearanceHovered.Options.UseBackColor = True
        Me.bntSendToSender.AppearanceHovered.Options.UseBorderColor = True
        Me.bntSendToSender.AppearanceHovered.Options.UseForeColor = True
        Me.bntSendToSender.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntSendToSender.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntSendToSender.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.bntSendToSender.AppearancePressed.Options.UseBackColor = True
        Me.bntSendToSender.AppearancePressed.Options.UseBorderColor = True
        Me.bntSendToSender.AppearancePressed.Options.UseForeColor = True
        Me.bntSendToSender.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Transfer32x32
        Me.bntSendToSender.Location = New System.Drawing.Point(318, 93)
        Me.bntSendToSender.Name = "bntSendToSender"
        Me.bntSendToSender.Size = New System.Drawing.Size(148, 38)
        Me.bntSendToSender.StyleController = Me.LayoutControl4
        Me.bntSendToSender.TabIndex = 529
        Me.bntSendToSender.Text = "Send To Sender"
        '
        'txtSearch2
        '
        Me.txtSearch2.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txtSearch2.EditValue = ""
        Me.txtSearch2.Location = New System.Drawing.Point(16, 463)
        Me.txtSearch2.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txtSearch2.Name = "txtSearch2"
        Me.txtSearch2.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txtSearch2.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtSearch2.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txtSearch2.Properties.Appearance.Options.UseBackColor = True
        Me.txtSearch2.Properties.Appearance.Options.UseFont = True
        Me.txtSearch2.Properties.Appearance.Options.UseForeColor = True
        Me.txtSearch2.Properties.ContextImageOptions.Image = Global.Best_Sender.My.Resources.Resources.search_16x16
        Me.txtSearch2.Properties.NullValuePrompt = "Type Word To Find"
        Me.txtSearch2.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txtSearch2.Size = New System.Drawing.Size(450, 30)
        Me.txtSearch2.StyleController = Me.LayoutControl4
        Me.txtSearch2.TabIndex = 528
        '
        'bntSerch2
        '
        Me.bntSerch2.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntSerch2.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntSerch2.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.bntSerch2.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntSerch2.Appearance.Options.UseBackColor = True
        Me.bntSerch2.Appearance.Options.UseBorderColor = True
        Me.bntSerch2.Appearance.Options.UseFont = True
        Me.bntSerch2.Appearance.Options.UseForeColor = True
        Me.bntSerch2.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.bntSerch2.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.bntSerch2.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.bntSerch2.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.bntSerch2.AppearanceDisabled.Options.UseBackColor = True
        Me.bntSerch2.AppearanceDisabled.Options.UseBorderColor = True
        Me.bntSerch2.AppearanceDisabled.Options.UseFont = True
        Me.bntSerch2.AppearanceDisabled.Options.UseForeColor = True
        Me.bntSerch2.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.bntSerch2.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntSerch2.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.bntSerch2.AppearanceHovered.Options.UseBackColor = True
        Me.bntSerch2.AppearanceHovered.Options.UseBorderColor = True
        Me.bntSerch2.AppearanceHovered.Options.UseForeColor = True
        Me.bntSerch2.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntSerch2.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntSerch2.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.bntSerch2.AppearancePressed.Options.UseBackColor = True
        Me.bntSerch2.AppearancePressed.Options.UseBorderColor = True
        Me.bntSerch2.AppearancePressed.Options.UseForeColor = True
        Me.bntSerch2.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.search_32x32
        Me.bntSerch2.Location = New System.Drawing.Point(16, 419)
        Me.bntSerch2.Name = "bntSerch2"
        Me.bntSerch2.Size = New System.Drawing.Size(221, 38)
        Me.bntSerch2.StyleController = Me.LayoutControl4
        Me.bntSerch2.TabIndex = 527
        Me.bntSerch2.Text = "Search"
        '
        'bntCLear2
        '
        Me.bntCLear2.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntCLear2.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntCLear2.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.bntCLear2.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntCLear2.Appearance.Options.UseBackColor = True
        Me.bntCLear2.Appearance.Options.UseBorderColor = True
        Me.bntCLear2.Appearance.Options.UseFont = True
        Me.bntCLear2.Appearance.Options.UseForeColor = True
        Me.bntCLear2.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.bntCLear2.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.bntCLear2.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.bntCLear2.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.bntCLear2.AppearanceDisabled.Options.UseBackColor = True
        Me.bntCLear2.AppearanceDisabled.Options.UseBorderColor = True
        Me.bntCLear2.AppearanceDisabled.Options.UseFont = True
        Me.bntCLear2.AppearanceDisabled.Options.UseForeColor = True
        Me.bntCLear2.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.bntCLear2.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntCLear2.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.bntCLear2.AppearanceHovered.Options.UseBackColor = True
        Me.bntCLear2.AppearanceHovered.Options.UseBorderColor = True
        Me.bntCLear2.AppearanceHovered.Options.UseForeColor = True
        Me.bntCLear2.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntCLear2.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntCLear2.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.bntCLear2.AppearancePressed.Options.UseBackColor = True
        Me.bntCLear2.AppearancePressed.Options.UseBorderColor = True
        Me.bntCLear2.AppearancePressed.Options.UseForeColor = True
        Me.bntCLear2.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_32x322
        Me.bntCLear2.Location = New System.Drawing.Point(243, 419)
        Me.bntCLear2.Name = "bntCLear2"
        Me.bntCLear2.Size = New System.Drawing.Size(223, 38)
        Me.bntCLear2.StyleController = Me.LayoutControl4
        Me.bntCLear2.TabIndex = 526
        Me.bntCLear2.Text = "Clear"
        '
        'LetterEncoder_Richtext2
        '
        Me.LetterEncoder_Richtext2.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.LetterEncoder_Richtext2.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.LetterEncoder_Richtext2.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.LetterEncoder_Richtext2.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(35, Byte), Integer))
        Me.LetterEncoder_Richtext2.Location = New System.Drawing.Point(16, 147)
        Me.LetterEncoder_Richtext2.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.LetterEncoder_Richtext2.Name = "LetterEncoder_Richtext2"
        Me.LetterEncoder_Richtext2.Size = New System.Drawing.Size(450, 244)
        Me.LetterEncoder_Richtext2.TabIndex = 525
        Me.LetterEncoder_Richtext2.Text = ""
        '
        'bntSaveLetter
        '
        Me.bntSaveLetter.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntSaveLetter.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntSaveLetter.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.bntSaveLetter.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntSaveLetter.Appearance.Options.UseBackColor = True
        Me.bntSaveLetter.Appearance.Options.UseBorderColor = True
        Me.bntSaveLetter.Appearance.Options.UseFont = True
        Me.bntSaveLetter.Appearance.Options.UseForeColor = True
        Me.bntSaveLetter.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.bntSaveLetter.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.bntSaveLetter.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.bntSaveLetter.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.bntSaveLetter.AppearanceDisabled.Options.UseBackColor = True
        Me.bntSaveLetter.AppearanceDisabled.Options.UseBorderColor = True
        Me.bntSaveLetter.AppearanceDisabled.Options.UseFont = True
        Me.bntSaveLetter.AppearanceDisabled.Options.UseForeColor = True
        Me.bntSaveLetter.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.bntSaveLetter.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntSaveLetter.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.bntSaveLetter.AppearanceHovered.Options.UseBackColor = True
        Me.bntSaveLetter.AppearanceHovered.Options.UseBorderColor = True
        Me.bntSaveLetter.AppearanceHovered.Options.UseForeColor = True
        Me.bntSaveLetter.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntSaveLetter.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntSaveLetter.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.bntSaveLetter.AppearancePressed.Options.UseBackColor = True
        Me.bntSaveLetter.AppearancePressed.Options.UseBorderColor = True
        Me.bntSaveLetter.AppearancePressed.Options.UseForeColor = True
        Me.bntSaveLetter.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Save_Image
        Me.bntSaveLetter.Location = New System.Drawing.Point(176, 93)
        Me.bntSaveLetter.Name = "bntSaveLetter"
        Me.bntSaveLetter.Size = New System.Drawing.Size(136, 38)
        Me.bntSaveLetter.StyleController = Me.LayoutControl4
        Me.bntSaveLetter.TabIndex = 524
        Me.bntSaveLetter.Text = "Save Letter"
        '
        'bntEncodedLetter
        '
        Me.bntEncodedLetter.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntEncodedLetter.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntEncodedLetter.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.bntEncodedLetter.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntEncodedLetter.Appearance.Options.UseBackColor = True
        Me.bntEncodedLetter.Appearance.Options.UseBorderColor = True
        Me.bntEncodedLetter.Appearance.Options.UseFont = True
        Me.bntEncodedLetter.Appearance.Options.UseForeColor = True
        Me.bntEncodedLetter.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.bntEncodedLetter.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.bntEncodedLetter.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.bntEncodedLetter.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.bntEncodedLetter.AppearanceDisabled.Options.UseBackColor = True
        Me.bntEncodedLetter.AppearanceDisabled.Options.UseBorderColor = True
        Me.bntEncodedLetter.AppearanceDisabled.Options.UseFont = True
        Me.bntEncodedLetter.AppearanceDisabled.Options.UseForeColor = True
        Me.bntEncodedLetter.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.bntEncodedLetter.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntEncodedLetter.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.bntEncodedLetter.AppearanceHovered.Options.UseBackColor = True
        Me.bntEncodedLetter.AppearanceHovered.Options.UseBorderColor = True
        Me.bntEncodedLetter.AppearanceHovered.Options.UseForeColor = True
        Me.bntEncodedLetter.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntEncodedLetter.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntEncodedLetter.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.bntEncodedLetter.AppearancePressed.Options.UseBackColor = True
        Me.bntEncodedLetter.AppearancePressed.Options.UseBorderColor = True
        Me.bntEncodedLetter.AppearancePressed.Options.UseForeColor = True
        Me.bntEncodedLetter.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.EncodedLetter32x32
        Me.bntEncodedLetter.Location = New System.Drawing.Point(16, 93)
        Me.bntEncodedLetter.Name = "bntEncodedLetter"
        Me.bntEncodedLetter.Size = New System.Drawing.Size(154, 38)
        Me.bntEncodedLetter.StyleController = Me.LayoutControl4
        Me.bntEncodedLetter.TabIndex = 523
        Me.bntEncodedLetter.Text = "Encoded Letter"
        '
        'CheckFrench
        '
        Me.CheckFrench.Location = New System.Drawing.Point(384, 60)
        Me.CheckFrench.Name = "CheckFrench"
        Me.CheckFrench.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.CheckFrench.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.CheckFrench.Properties.Appearance.Options.UseFont = True
        Me.CheckFrench.Properties.Appearance.Options.UseForeColor = True
        Me.CheckFrench.Properties.Caption = " French"
        Me.CheckFrench.Size = New System.Drawing.Size(82, 27)
        Me.CheckFrench.StyleController = Me.LayoutControl4
        Me.CheckFrench.TabIndex = 520
        '
        'CheckRussian
        '
        Me.CheckRussian.Location = New System.Drawing.Point(289, 60)
        Me.CheckRussian.Name = "CheckRussian"
        Me.CheckRussian.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.CheckRussian.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.CheckRussian.Properties.Appearance.Options.UseFont = True
        Me.CheckRussian.Properties.Appearance.Options.UseForeColor = True
        Me.CheckRussian.Properties.Caption = " Russian"
        Me.CheckRussian.Size = New System.Drawing.Size(89, 27)
        Me.CheckRussian.StyleController = Me.LayoutControl4
        Me.CheckRussian.TabIndex = 519
        '
        'CheckHindi
        '
        Me.CheckHindi.Location = New System.Drawing.Point(211, 60)
        Me.CheckHindi.Name = "CheckHindi"
        Me.CheckHindi.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.CheckHindi.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.CheckHindi.Properties.Appearance.Options.UseFont = True
        Me.CheckHindi.Properties.Appearance.Options.UseForeColor = True
        Me.CheckHindi.Properties.Caption = " Hindi"
        Me.CheckHindi.Size = New System.Drawing.Size(72, 27)
        Me.CheckHindi.StyleController = Me.LayoutControl4
        Me.CheckHindi.TabIndex = 518
        '
        'CheckArabic
        '
        Me.CheckArabic.Location = New System.Drawing.Point(126, 60)
        Me.CheckArabic.Name = "CheckArabic"
        Me.CheckArabic.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.CheckArabic.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.CheckArabic.Properties.Appearance.Options.UseFont = True
        Me.CheckArabic.Properties.Appearance.Options.UseForeColor = True
        Me.CheckArabic.Properties.Caption = " Arabic"
        Me.CheckArabic.Size = New System.Drawing.Size(79, 27)
        Me.CheckArabic.StyleController = Me.LayoutControl4
        Me.CheckArabic.TabIndex = 517
        '
        'CheckJapanese
        '
        Me.CheckJapanese.Location = New System.Drawing.Point(16, 60)
        Me.CheckJapanese.Name = "CheckJapanese"
        Me.CheckJapanese.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.CheckJapanese.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.CheckJapanese.Properties.Appearance.Options.UseFont = True
        Me.CheckJapanese.Properties.Appearance.Options.UseForeColor = True
        Me.CheckJapanese.Properties.Caption = " Japanese"
        Me.CheckJapanese.Size = New System.Drawing.Size(104, 27)
        Me.CheckJapanese.StyleController = Me.LayoutControl4
        Me.CheckJapanese.TabIndex = 516
        '
        'cmbEncryption
        '
        Me.cmbEncryption.Cursor = System.Windows.Forms.Cursors.Hand
        Me.cmbEncryption.EditValue = ""
        Me.cmbEncryption.Location = New System.Drawing.Point(176, 16)
        Me.cmbEncryption.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.cmbEncryption.Name = "cmbEncryption"
        Me.cmbEncryption.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.cmbEncryption.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.cmbEncryption.Properties.Appearance.Options.UseBackColor = True
        Me.cmbEncryption.Properties.Appearance.Options.UseFont = True
        Me.cmbEncryption.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cmbEncryption.Properties.Items.AddRange(New Object() {"Best Sender Vip", "Devil Encryption"})
        Me.cmbEncryption.Size = New System.Drawing.Size(290, 38)
        Me.cmbEncryption.StyleController = Me.LayoutControl4
        Me.cmbEncryption.TabIndex = 70
        '
        'LayoutControlGroup3
        '
        Me.LayoutControlGroup3.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.LayoutControlGroup3.GroupBordersVisible = False
        Me.LayoutControlGroup3.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem7, Me.EmptySpaceItem1, Me.LayoutControlItem8, Me.LayoutControlItem9, Me.LayoutControlItem10, Me.LayoutControlItem11, Me.LayoutControlItem12, Me.LayoutControlItem13, Me.LayoutControlItem16, Me.LayoutControlItem17, Me.LayoutControlItem19, Me.LayoutControlItem18, Me.LayoutControlItem22, Me.LayoutControlItem21})
        Me.LayoutControlGroup3.Name = "LayoutControlGroup3"
        Me.LayoutControlGroup3.Size = New System.Drawing.Size(482, 509)
        Me.LayoutControlGroup3.TextVisible = False
        '
        'LayoutControlItem7
        '
        Me.LayoutControlItem7.Control = Me.cmbEncryption
        Me.LayoutControlItem7.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem7.Name = "LayoutControlItem7"
        Me.LayoutControlItem7.Size = New System.Drawing.Size(456, 44)
        Me.LayoutControlItem7.Text = "Select Algorithm Method :"
        Me.LayoutControlItem7.TextSize = New System.Drawing.Size(144, 18)
        '
        'EmptySpaceItem1
        '
        Me.EmptySpaceItem1.Location = New System.Drawing.Point(0, 381)
        Me.EmptySpaceItem1.Name = "EmptySpaceItem1"
        Me.EmptySpaceItem1.Size = New System.Drawing.Size(456, 22)
        '
        'LayoutControlItem8
        '
        Me.LayoutControlItem8.Control = Me.CheckJapanese
        Me.LayoutControlItem8.Location = New System.Drawing.Point(0, 44)
        Me.LayoutControlItem8.Name = "LayoutControlItem8"
        Me.LayoutControlItem8.Size = New System.Drawing.Size(110, 33)
        Me.LayoutControlItem8.TextVisible = False
        '
        'LayoutControlItem9
        '
        Me.LayoutControlItem9.Control = Me.CheckArabic
        Me.LayoutControlItem9.Location = New System.Drawing.Point(110, 44)
        Me.LayoutControlItem9.Name = "LayoutControlItem9"
        Me.LayoutControlItem9.Size = New System.Drawing.Size(85, 33)
        Me.LayoutControlItem9.TextVisible = False
        '
        'LayoutControlItem10
        '
        Me.LayoutControlItem10.Control = Me.CheckHindi
        Me.LayoutControlItem10.Location = New System.Drawing.Point(195, 44)
        Me.LayoutControlItem10.Name = "LayoutControlItem10"
        Me.LayoutControlItem10.Size = New System.Drawing.Size(78, 33)
        Me.LayoutControlItem10.TextVisible = False
        '
        'LayoutControlItem11
        '
        Me.LayoutControlItem11.Control = Me.CheckRussian
        Me.LayoutControlItem11.Location = New System.Drawing.Point(273, 44)
        Me.LayoutControlItem11.Name = "LayoutControlItem11"
        Me.LayoutControlItem11.Size = New System.Drawing.Size(95, 33)
        Me.LayoutControlItem11.TextVisible = False
        '
        'LayoutControlItem12
        '
        Me.LayoutControlItem12.Control = Me.CheckFrench
        Me.LayoutControlItem12.Location = New System.Drawing.Point(368, 44)
        Me.LayoutControlItem12.Name = "LayoutControlItem12"
        Me.LayoutControlItem12.Size = New System.Drawing.Size(88, 33)
        Me.LayoutControlItem12.TextVisible = False
        '
        'LayoutControlItem13
        '
        Me.LayoutControlItem13.Control = Me.bntEncodedLetter
        Me.LayoutControlItem13.Location = New System.Drawing.Point(0, 77)
        Me.LayoutControlItem13.Name = "LayoutControlItem13"
        Me.LayoutControlItem13.Size = New System.Drawing.Size(160, 44)
        Me.LayoutControlItem13.TextVisible = False
        '
        'LayoutControlItem16
        '
        Me.LayoutControlItem16.Control = Me.bntSaveLetter
        Me.LayoutControlItem16.Location = New System.Drawing.Point(160, 77)
        Me.LayoutControlItem16.Name = "LayoutControlItem16"
        Me.LayoutControlItem16.Size = New System.Drawing.Size(142, 44)
        Me.LayoutControlItem16.TextVisible = False
        '
        'LayoutControlItem17
        '
        Me.LayoutControlItem17.Control = Me.LetterEncoder_Richtext2
        Me.LayoutControlItem17.Location = New System.Drawing.Point(0, 121)
        Me.LayoutControlItem17.Name = "LayoutControlItem17"
        Me.LayoutControlItem17.Size = New System.Drawing.Size(456, 260)
        Me.LayoutControlItem17.Spacing = New DevExpress.XtraLayout.Utils.Padding(0, 0, 10, 0)
        Me.LayoutControlItem17.TextVisible = False
        '
        'LayoutControlItem19
        '
        Me.LayoutControlItem19.Control = Me.bntSerch2
        Me.LayoutControlItem19.Location = New System.Drawing.Point(0, 403)
        Me.LayoutControlItem19.Name = "LayoutControlItem19"
        Me.LayoutControlItem19.Size = New System.Drawing.Size(227, 44)
        Me.LayoutControlItem19.TextVisible = False
        '
        'LayoutControlItem18
        '
        Me.LayoutControlItem18.Control = Me.bntCLear2
        Me.LayoutControlItem18.Location = New System.Drawing.Point(227, 403)
        Me.LayoutControlItem18.Name = "LayoutControlItem18"
        Me.LayoutControlItem18.Size = New System.Drawing.Size(229, 44)
        Me.LayoutControlItem18.TextVisible = False
        '
        'LayoutControlItem22
        '
        Me.LayoutControlItem22.Control = Me.txtSearch2
        Me.LayoutControlItem22.Location = New System.Drawing.Point(0, 447)
        Me.LayoutControlItem22.Name = "LayoutControlItem22"
        Me.LayoutControlItem22.Size = New System.Drawing.Size(456, 36)
        Me.LayoutControlItem22.TextVisible = False
        '
        'LayoutControlItem21
        '
        Me.LayoutControlItem21.Control = Me.bntSendToSender
        Me.LayoutControlItem21.Location = New System.Drawing.Point(302, 77)
        Me.LayoutControlItem21.Name = "LayoutControlItem21"
        Me.LayoutControlItem21.Size = New System.Drawing.Size(154, 44)
        Me.LayoutControlItem21.TextVisible = False
        '
        'WebBrowser1
        '
        Me.WebBrowser1.Location = New System.Drawing.Point(506, 42)
        Me.WebBrowser1.MinimumSize = New System.Drawing.Size(20, 20)
        Me.WebBrowser1.Name = "WebBrowser1"
        Me.WebBrowser1.ScriptErrorsSuppressed = True
        Me.WebBrowser1.Size = New System.Drawing.Size(450, 514)
        Me.WebBrowser1.TabIndex = 522
        '
        'Root
        '
        Me.Root.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.Root.GroupBordersVisible = False
        Me.Root.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem1, Me.LayoutControlItem2, Me.LayoutControlItem23, Me.LayoutControlItem24})
        Me.Root.Name = "Root"
        Me.Root.Size = New System.Drawing.Size(972, 602)
        Me.Root.TextVisible = False
        '
        'LayoutControlItem1
        '
        Me.LayoutControlItem1.Control = Me.WebBrowser1
        Me.LayoutControlItem1.Location = New System.Drawing.Point(490, 26)
        Me.LayoutControlItem1.Name = "LayoutControlItem1"
        Me.LayoutControlItem1.Size = New System.Drawing.Size(456, 520)
        Me.LayoutControlItem1.TextVisible = False
        '
        'LayoutControlItem2
        '
        Me.LayoutControlItem2.Control = Me.XtraTabControl1
        Me.LayoutControlItem2.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem2.Name = "LayoutControlItem2"
        Me.LayoutControlItem2.Size = New System.Drawing.Size(490, 546)
        Me.LayoutControlItem2.TextVisible = False
        '
        'LayoutControlItem23
        '
        Me.LayoutControlItem23.Control = Me.ProgressBarControl1
        Me.LayoutControlItem23.Location = New System.Drawing.Point(0, 546)
        Me.LayoutControlItem23.Name = "LayoutControlItem23"
        Me.LayoutControlItem23.Size = New System.Drawing.Size(946, 30)
        Me.LayoutControlItem23.TextVisible = False
        '
        'LayoutControlItem24
        '
        Me.LayoutControlItem24.Control = Me.lblStatus
        Me.LayoutControlItem24.Location = New System.Drawing.Point(490, 0)
        Me.LayoutControlItem24.Name = "LayoutControlItem24"
        Me.LayoutControlItem24.Size = New System.Drawing.Size(456, 26)
        Me.LayoutControlItem24.TextVisible = False
        '
        'BackgroundWorker1
        '
        Me.BackgroundWorker1.WorkerReportsProgress = True
        Me.BackgroundWorker1.WorkerSupportsCancellation = True
        '
        'frmLetterEncoddvb
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 18.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(972, 602)
        Me.Controls.Add(Me.LayoutControl1)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle
        Me.IconOptions.ShowIcon = False
        Me.Name = "frmLetterEncoddvb"
        Me.ShowInTaskbar = False
        Me.Text = "Encoded Letter"
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.LayoutControl1.ResumeLayout(False)
        CType(Me.ProgressBarControl1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.XtraTabControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.XtraTabControl1.ResumeLayout(False)
        Me.XtraTabPage1.ResumeLayout(False)
        CType(Me.LayoutControl3, System.ComponentModel.ISupportInitialize).EndInit()
        Me.LayoutControl3.ResumeLayout(False)
        CType(Me.textSershNormal.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LetterEncoder_txtFilepath.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem6, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem15, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem14, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem20, System.ComponentModel.ISupportInitialize).EndInit()
        Me.XtraTabPage2.ResumeLayout(False)
        CType(Me.LayoutControl4, System.ComponentModel.ISupportInitialize).EndInit()
        Me.LayoutControl4.ResumeLayout(False)
        CType(Me.txtSearch2.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckFrench.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckRussian.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckHindi.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckArabic.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckJapanese.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbEncryption.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem7, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem8, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem9, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem10, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem11, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem12, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem13, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem16, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem17, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem19, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem18, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem22, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem21, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Root, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem23, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem24, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub

    Friend WithEvents LayoutControl1 As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents Root As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents XtraTabControl1 As DevExpress.XtraTab.XtraTabControl
    Friend WithEvents XtraTabPage1 As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents LayoutControl3 As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents textSershNormal As DevExpress.XtraEditors.TextEdit
    Friend WithEvents bntClearNormal As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents BntSerchNormal As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LetterEncoder_Richtext As RichTextBox
    Friend WithEvents BntClearAll As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents BntAddLetter As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LetterEncoder_txtFilepath As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LayoutControlGroup2 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem3 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents EmptySpaceItem2 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents LayoutControlItem4 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem5 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem6 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem15 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem14 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem20 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents XtraTabPage2 As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents LayoutControl4 As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents txtSearch2 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents bntSerch2 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents bntCLear2 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LetterEncoder_Richtext2 As RichTextBox
    Friend WithEvents bntSaveLetter As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents bntEncodedLetter As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents CheckFrench As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents CheckRussian As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents CheckHindi As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents CheckArabic As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents CheckJapanese As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents cmbEncryption As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents LayoutControlGroup3 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem7 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents EmptySpaceItem1 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents LayoutControlItem8 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem9 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem10 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem11 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem12 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem13 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem16 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem17 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem19 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem18 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem22 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents WebBrowser1 As WebBrowser
    Friend WithEvents LayoutControlItem1 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem2 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents BackgroundWorker1 As System.ComponentModel.BackgroundWorker
    Friend WithEvents bntSendToSender As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LayoutControlItem21 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ProgressBarControl1 As DevExpress.XtraEditors.ProgressBarControl
    Friend WithEvents LayoutControlItem23 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents lblStatus As Label
    Friend WithEvents LayoutControlItem24 As DevExpress.XtraLayout.LayoutControlItem
End Class
