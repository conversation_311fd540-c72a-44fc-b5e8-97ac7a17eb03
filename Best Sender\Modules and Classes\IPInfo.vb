﻿Imports System.IO
Imports System.Management
Imports System.Net
Imports System.Net.NetworkInformation
Imports System.Security.Cryptography
Imports System.Text
Module IPInfo
    Public MAILtO_list As New List(Of String)
    Public SMTPInfo As String = ""
#Region "Collect IP-Info"
    Public ipLookup As String = String.Empty
    Public flagLocation As String = String.Empty
    Public fingerPrint As String = String.Empty
    Function getMacAddress()
        Dim nics() As NetworkInterface =
              NetworkInterface.GetAllNetworkInterfaces
        Return nics(0).Description.ToString
    End Function
    Function downloadData(ByVal ur As Uri) As String
        Dim result As String = Nothing
        Try
            Dim req As HttpWebRequest = HttpWebRequest.Create(ur)
            Dim res As HttpWebResponse = req.GetResponse()
            If (res.StatusCode = HttpStatusCode.OK) Then
                Dim receiveStream As Stream = res.GetResponseStream()
                Dim readStream As StreamReader = Nothing
                If (res.CharacterSet = Nothing) Then
                    readStream = New StreamReader(receiveStream)
                Else
                    readStream = New StreamReader(receiveStream, Encoding.GetEncoding(res.CharacterSet))
                    Dim data As String = readStream.ReadToEnd()
                    res.Close()
                    readStream.Close()
                    result = data
                End If
            End If
        Catch
        End Try
        Return result
    End Function
    Function extractSubject(ByVal data As String, ByVal left As String, ByVal right As String)
        Try
            Dim indexLeft As Integer = data.IndexOf(left)
            Dim indexRight As Integer = data.IndexOf(right)
            Return data.Substring(indexLeft + left.Length, indexRight - indexLeft - left.Length)
        Catch
            Return "Not Resolved!"
        End Try
    End Function
    Function country() As String
        Return extractSubject(ipLookup, ":&nbsp;&nbsp;<font color='#980000'> ",
                              "</font>&nbsp;&nbsp;<img src='")
    End Function
    Function downloadPicture(ByVal webPath As String) As Image
        Try
            Dim myWebClient As New WebClient()
            Return Image.FromStream(New MemoryStream(myWebClient.DownloadData(webPath)))
        Catch : End Try
        Return Nothing
    End Function
    Function publicIP() As String
        Return extractSubject(ipLookup, ">My IP Address lookup</strong> for <b>", "</b> show IP which")
    End Function
    Function localIP() As String
        Try
            Dim iphostentry As Net.IPHostEntry = Net.Dns.GetHostByName(Net.Dns.GetHostName)
            Return CType(iphostentry.AddressList.GetValue(0), IPAddress).ToString
        Catch : End Try
    End Function
    Function city() As String
        Return extractSubject(ipLookup, "</b>:&nbsp;&nbsp; <font color='#980000'>",
                              "</font><br><b>")
    End Function
    Function Region() As String
        Return extractSubject(ipLookup,
                              ")<br><br><strong>My IP Address Region</strong>: <font color='#980000'>",
                              "</font><br><b>").Split("<")(0)
    End Function
    Function continent() As String
        Dim result As String = extractSubject(ipLookup, "Country Continent", "Country Latitude")
        result = (result.Split(">")(2) & result.Split(">")(4)).Replace("</font", "")
        Return result.Insert(result.Length - 4, " ")
    End Function
    Function isp() As String
        Return extractSubject(ipLookup, "Provider)</strong>:&nbsp;<font color='#980000'> ", "</font><br /")
    End Function
    Function netBroadband() As String
        Dim result As String = extractSubject(ipLookup, "show IP which belongs to", "Time zone")
        Return (result.Split(">")(1)).Split("<")(0)
    End Function
    Function countryCode() As String
        Try : Return flagLocation.Split("/")(2).Remove(2).ToUpper : Catch : End Try
    End Function
    Function phonePrefix(ByVal ISOCode As String) As String
        Dim countries() As String = My.Settings.prefixes.Split(vbNewLine.ToCharArray,
            StringSplitOptions.RemoveEmptyEntries)
        Try
            For Each country As String In countries
                Dim parts() As String = country.Split("|")
                If parts(0) = countryCode() Then Return parts(2) ' Exit For (if not Returning!)
            Next
        Catch : End Try
    End Function
    Function latitude() As String
        Return extractSubject(ipLookup, "My IP Address Latitude</b>: ", "<br><b>My IP Address Longtitude")
    End Function
    Function longtitude() As String
        Return extractSubject(ipLookup, "My IP Address Longtitude</b>: ", "<br><br><strong>My ISP")
    End Function
#End Region
#Region "Value Function"
    Public Sub setDNS(NIC As String, DNS As String)
        Dim objMC As New ManagementClass("Win32_NetworkAdapterConfiguration")
        Dim objMOC As ManagementObjectCollection = objMC.GetInstances()
        For Each objMO As ManagementObject In objMOC
            If CBool(objMO("IPEnabled")) Then
                ' if you are using the System.Net.NetworkInformation.NetworkInterface
                ' you'll need to change this line to
                ' if (objMO["Caption"].ToString().Contains(NIC))
                ' and pass in the Description property instead of the name 
                If objMO("Description").Equals(NIC) Then
                    Dim newDNS As ManagementBaseObject = objMO.GetMethodParameters("SetDNSServerSearchOrder")
                    newDNS("DNSServerSearchOrder") = DNS.Split(","c)
                    Dim setDNS As ManagementBaseObject = objMO.InvokeMethod("SetDNSServerSearchOrder", newDNS, Nothing)
                End If
            End If
        Next
    End Sub
    Public Function Value() As String
        If String.IsNullOrEmpty(fingerPrint) Then
            fingerPrint = GetHash("CPU >> " & cpuId() & vbLf & "BIOS >> " + biosId() & vbLf & "BASE >> " + baseId())
        End If
        Return fingerPrint
    End Function
    Private Function GetHash(ByVal s As String) As String
        Dim sec As MD5 = New MD5CryptoServiceProvider
        Dim enc As ASCIIEncoding = New ASCIIEncoding
        Dim bt() As Byte = enc.GetBytes(s)
        Return GetHexString(sec.ComputeHash(bt))
    End Function
    Private Function GetHexString(bt As Byte()) As String
        Dim s As String = String.Empty
        For i As Integer = 0 To bt.Length - 1
            Dim b As Byte = bt(i)
            Dim n As Integer, n1 As Integer, n2 As Integer
            n = CInt(b)
            n1 = n And 15
            n2 = (n >> 4) And 15
            If n2 > 9 Then
                s += ChrW((n2 - 10 + CInt(65))).ToString()
            Else
                s += n2.ToString()
            End If
            If n1 > 9 Then
                s += ChrW((n1 - 10 + CInt(65))).ToString()
            Else
                s += n1.ToString()
            End If
            If (i + 1) <> bt.Length AndAlso (i + 1) Mod 2 = 0 Then
                s += "-"
            End If
        Next
        Return s
    End Function
    Private Function identifier(ByVal wmiClass As String, ByVal wmiProperty As String, ByVal wmiMustBeTrue As String) As String
        Dim result As String = ""
        Dim mc As System.Management.ManagementClass = New System.Management.ManagementClass(wmiClass)
        Dim moc As System.Management.ManagementObjectCollection = mc.GetInstances
        For Each mo As System.Management.ManagementObject In moc
            If (mo(wmiMustBeTrue).ToString = "True") Then
                'Only get the first one
                If (result = "") Then
                    Try
                        result = mo(wmiProperty).ToString
                        Exit For
                    Catch ex As Exception
                    End Try
                End If
            End If
        Next
        Return result
    End Function
    'Return a hardware identifier
    Function identifier(ByVal wmiClass As String, ByVal wmiProperty As String) As String
        Dim result As String = ""
        Dim mc As System.Management.ManagementClass = New System.Management.ManagementClass(wmiClass)
        Dim moc As System.Management.ManagementObjectCollection = mc.GetInstances
        For Each mo As System.Management.ManagementObject In moc
            'Only get the first one
            If (result = "") Then
                Try
                    result = mo(wmiProperty)
                    Exit For
                Catch ex As Exception
                End Try
            End If
        Next
        Return result
    End Function
    Private Function cpuId() As String
        'Uses first CPU identifier available in order of preference
        'Don't get all identifiers, as very time consuming
        Dim retVal As String = identifier("Win32_Processor", "UniqueId")
        If (retVal = "") Then
            retVal = identifier("Win32_Processor", "ProcessorId")
            If (retVal = "") Then
                retVal = identifier("Win32_Processor", "Name")
                If (retVal = "") Then
                    retVal = identifier("Win32_Processor", "Manufacturer")
                End If
                'Add clock speed for extra security
                retVal = (retVal + identifier("Win32_Processor", "MaxClockSpeed"))
            End If
        End If
        Return retVal
    End Function
    'BIOS Identifier
    Private Function biosId() As String
        Return (identifier("Win32_BIOS", "Manufacturer") _
                    + (identifier("Win32_BIOS", "SMBIOSBIOSVersion") _
                    + (identifier("Win32_BIOS", "IdentificationCode") _
                    + (identifier("Win32_BIOS", "SerialNumber") _
                    + (identifier("Win32_BIOS", "ReleaseDate") + identifier("Win32_BIOS", "Version"))))))
    End Function
    'Main physical hard drive ID
    Private Function diskId() As String
        Return (identifier("Win32_DiskDrive", "Model") _
                    + (identifier("Win32_DiskDrive", "Manufacturer") _
                    + (identifier("Win32_DiskDrive", "Signature") + identifier("Win32_DiskDrive", "TotalHeads"))))
    End Function
    'Motherboard ID
    Private Function baseId() As String
        Return (identifier("Win32_BaseBoard", "Model") _
                    + (identifier("Win32_BaseBoard", "Manufacturer") _
                    + (identifier("Win32_BaseBoard", "Name") + identifier("Win32_BaseBoard", "SerialNumber"))))
    End Function
    'Primary video controller ID
    Private Function videoId() As String
        Return (identifier("Win32_VideoController", "DriverVersion") + identifier("Win32_VideoController", "Name"))
    End Function
    'First enabled network card ID
    Function macId() As String
        Return identifier("Win32_NetworkAdapterConfiguration", "MACAddress", "IPEnabled")
    End Function
#End Region
End Module
