﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="BackgroundWorker1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <metadata name="DxErrorProvider1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>160, 17</value>
  </metadata>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="PictureEdit4.EditValue" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAABJ0RVh0VGl0
        bGUAU3RhdHVzO0ZsYWc7gwAeYwAACBRJREFUWEfFlnlUU1cex591tNZ21GoVUWxrXSAoSNgEBBQokAAS
        ZA1E9n0AgQBGEQQqIMgmyr66xtooiAqCEkFBRNDSURCZU1vASqUyxxntOGNP53zn3gQshJn/qvPO+eS9
        e3+5v9/n3dx7gQHwf0X+8ds1g/DOJGbcGdnFdA7HMm0PI5nmB6FM/d0Apua2F3Oqw405et2JqZDaMyWX
        7ZiSJlsZxVOwYYoabeVcsmEKp8BlChq40wTe8Y4tv+odWwHv2LKrpD1znMlSr+UmeJ20wYYknQyXOVxv
        QyD3i1zmEOUChcPkU85zpgjQZDOzv7TBP149QkCSNjqHYtExKMT1h5GQDoTh0r3AF7W3fbrEN9zLK684
        huee5aymYwgyIVlykpQmphyss2byZJDncxwm75w1k3vOSkZODbkTFAVmZYm5aOoLgP9ebRDLp32PzmP4
        aRtG/96NsX9dx/BfW9H9fQEu9SSgvJGPnFqrluTqLdpkLBWZcfsvp5jhn7pkDI3e+p9kSiyZzK8spwt4
        xZSj4GQrbN0jwAtRO59Yao6qJj7qe0LR/ywNY79cwdA/izH4ohK9P1TjxoMiFF/kv0yqMjUl42USQ087
        mcdj3USCFusk3JxG+kkLJk1sPkWATuNsr5gyHD7ZAhu3MNgHqe6NPGCEsgYX1HYF4N5PKXj6qh4Dz9Pw
        9ehunO3ejtImHg7VOiL1hPldMn4O4Q/juWTrY3C0gxl8coM8ytuEqWtIQeBdL2EZ8o9fBcclFLb+az1C
        9umh+IIjJO0+uDOSiCcv69D3LBk3HwkhueWOgnoO4iv0kFJthcRK0wb/BPY6koeKzCJQmYmFTJ9nE94l
        0Dh9nqkoMMdLWIrco82wdg6GuetKI7oWCup4EF/zxK0hER7/LMHdsT1o/S4c4g5XZNeaYVe5Lv6UpYX4
        ImukneD+KioyrssSb/UWZZuxSM4P4jIsVhdInM1PXfeJqrsdVCV9EDEYV2jUQ2KzFQXe8yQCOUcuw3Jb
        INT1F3/mm6hFpngrTrR4oO3bGAy/EJPpF+HygD+Otjkg9bQBRGU6CM7QhLtIHZnHHSC+KsSZjgh82RYE
        cbsnLtwJh/R+PFm8B3H/x5No7i6BV7xGDak3Z7IAnaa5ntElyKpqwuc8P3wwf7ayTyIbB2vscEzKx9WB
        HfjuWRW6RqJxsdcLJc1WSBHrIa5UG4HpGvDYpY7QLE3EFLNR2uiKlv40DD9rwuiLDvz44jp+eN6MPw8e
        hXc8e4RtpvQpqTdLUeB9z+hiHKhogIW9Lw0o+e1l/5wt4eKI1A1X+sIwMFaC9uFwnOlxRV6DMZJO6iK2
        hA2/fevhEa+OkCwNRBdqIeG4DjLrDFF2zRKND4LQ9r0QHeRcCU83+LeJk4ojyf2erKaCwB+3RxVhf9lF
        mNl50oAyEXiedZqD6mZXNPWGoG80Fy0PA3Gsk4uc+k2kkDaiC7TglaQOj90sBGasR+ThDdhzTBsZdQYo
        atmME90cnO8TIC7fBJu2Ke+idQh0UU7ZBVRg3vaoQqQW1WGLjYAGVPyS2L9mS4jAFRciEIyex2mo7+fj
        SIc1DpA3FFVpIfygBgTxLLjvYsEvdR3C8jQgqmQjvWYjCqWmONZljZ1FetDfqlRIci4g0B0ww9L7kynn
        ADWaL4g8jJSCWphy+FiluZDtl8xG9hlrVEtd0Njrj87heFy474rKdkukndGHsFgTIQfWgS9SA3+nGnyS
        WQgm7ZgSTSSf1kNuowl8k1jQ4X5URPJ/SKDbUHYObBGoTBNYINhxCEn5Z2Fs6QpDWxVewBdsHJBYoErK
        Q2OfL/nbEIe6XmeUtpoj6ZQuIgrWISBdHU5CVbjGqUGQoAZ/0o4s0EAEmRkrvxWvNC0WxpDcU4obOS1l
        TNyWTRP40CMiHwl5X8HI3AlWglW7g9K1kCnZgiOtDrjc74dr30Yir9YCwfs14CxcA174avDC1hIBNbjE
        qsJNpArnmDXgBHwKI0flG6t05hmRvPMJdNplxdvv5zGG25QYxx3kmJgkQE+uhe7heYjPFsNgMw92QaqS
        iBw2Mk5vRmKZCXziNUGsf9lor9Sia7MkjW25xI1lsMiQjPt4a+haqVMkC5Y+K0eMXVSOqBsv+pz0LyK8
        P55bVrzlbgbTei+LMSAC9mGq0wQWJRSZ4cnfbsItSh0O4WqP/fdtgIVgxUtd2yU1LOMFvvMXz6b7lyae
        R6DJ6Xaid7q4PhqHPs8lyI9bUpjS9HUK0/xNKiP9Jp0xdFzK2AWveS1Av0AFFu8p3ILGe/5EYD3sQlT7
        zQWfJSqtnEtUmYUEun0mn/P0rSagbdo/EXtd2IAU2+igRFgiw2DbUoauAW4A+XdCQWCJa0gGhKlV0DO2
        oYGVBFqYvg2Nv046gSmfrGSFvglM3JYzm1yWkWLKsjeegLZpP0dBgE6XkkvQfkSnVBABLg0sJ8hPrPGk
        +jz5W9BENMlm9xWMuecnsmSTMRN8LIuZEEFjV7nIBLRN+/+bwFLnwDTsSC6DjqE1DZB9Its6NP5mLgUB
        ZUf/LxC2txjaBpZTBOj33gTyD7kALbRsm28SQvcUgq1vQQP0J3irAst53gkIFh2Clp4ZDdAV9lYFVOy3
        70ZgXB40dTbTwIrx/rcmsMLOYyf8YnKhqW06IUD3/dv7CWz4QvhEZUJDy5gG3uoaoAfNIguH4LZ1GzaB
        pbGxjbbH+9+4AL3ocUoPHXqWK4/faZv2Txv4eyH/kF90FuiJR9+YngmTj95pA38fwPwHn+8XE9sJxAoA
        AAAASUVORK5CYII=
</value>
  </data>
  <data name="PictureEdit1.EditValue" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAAUdEVYdFRpdGxlAEh5cGVybGluaztXZWI7Bv6gzgAA
        BGFJREFUWEfFlXtQVFUcxxUWsEwT/pFmUsnRNI0EEi2EeAwiYOqyKwuLYAGxAgt7eQTyUAZalOWZ1oxB
        BqQwU1pOBj3MxBZiWpYWQeSNoBINWiZvcMr8ds4VaWGvI39dz+znj/O933u/v3vO756dB+CJwinyCafI
        J5win3CKfMIp8gmnyCecIp9winxiKMwc82dhpAedzx6P9RvkzZj8Px7eLCCYEMwITxEWTvH0lGZM0A+b
        k39GJke4kUiWtiIwtuCLgLj836VMHvwU2fCVZ0EccQjCsIwBr71Jles3e9gQ7zMEEx9ZmhXxfzlHPy1y
        uojZBczfHaFczyiPD2kudeCvoVFyaea4PTiM6rrLCIxRjbzssN3LXSJ3iM4oGqqpb8HNPwfxz717+Pf+
        fZDftL9G2/zA/7q3O8mgRRiTSwYF0GUUSJncr+qaOtkH9d0aRNv1ATR29aOhow/a1mv4uakbal07lB+W
        w8knXCMMS6+uuKBB97XfoG3uhoZAfbr2G6hvuw5NSy/qrvQgp+gU3hBF/kQyLAmmNI+rADNpTO7w+MRd
        3Lh1BwfzS7DVn8FGz2Bs9g5FgOIQ5OlFCIx/H/5MDsSRWdglU2J7SBo8glLg/VYq3CQM7L1CWL9UnoHy
        impUN3YhObcErhJmgmSsJND+MCiANtFCv6hsMgVaewfgKAyHpzSqyFhg8tJiC0v7wLiCyebOPgyNTuD2
        8Bhu3hlBb/8fuNTWg3PVOhwrq4CjiPl7tZ2rD/UHRKR86huhxEVdJ1LySuEsjqZB62gOwaAA2qHPiiMP
        03w0dveTt4/Btt3BsQJTM1sLSysXX7lqrKm9F2ptCyrUOpz5UYviM1XIKjyNWGUhpNGZ2CJSTK6ycfZf
        ZL7UcZdf8IHQ/UdwTtPCroCjMJIGvUKgfWBQAO1Oc6EsfXR8YpLd85NnL2JHcDI8g/ZjR2ga/BUqiMKV
        2LY3BW4BiXCWxMNJHEtCGRYXSRzcpfHw2JPA+t9JOopfr1xF+Tc1eDfrOF57M+wuybAmPHIFlngGJV74
        paGNNM9VtoFqm3ugbujCeW0rTv2gwdGTlWDeOwZ/sr+27tJuN4mirfT09zhxtgqffVeLr9UNOK9pRZWu
        A9/WXkZZZQ3SjpQhNCEXNm5+9SRjNeHRPWDt4O2xhzk8ovroc7JsxThYcAIH8kuRnFeCpOxP2PC341XY
        IpRNPP/iq8y6TVtDpFGZY8oPypGo+hipZK9TiTeFkJxTjNjMQoQk5MDRZ9+45QvWQSRjKcFkOlevAPoV
        0FPMYvkaO2ennTK1izhq0NlXASeRnN0/h537sMkreHiDi6/W4rmVYmOB6SqB6YJlK9ZudH2c39zSSkSe
        vZxAT0UjgwL0iqDf6BLCMsJawgaCHcF+ClvCGgJ9E7qXCwhz9c8IZzP1J6zwoAjaD/TspgGLCIv1oHO6
        h3S1Hv7JzNlvkDdb4BtOkU84RT7hFPmEU+QTTpFPOEU+4RT5hFPkD8z7D3RuucIJQA76AAAAAElFTkSu
        QmCC
</value>
  </data>
  <data name="PictureEdit3.EditValue" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAABx0RVh0VGl0
        bGUAUnVsZXJIb3Jpem9udGFsO1J1bGVyO2vI4DYAAAWvSURBVFhH7ZZpTFRXGIantnZzBQFFY023pFWr
        VVu1imjaNDZ1A4FhERQXKtqABUFksW5opWrVarCupSAWENkHBoZ9gGFgUEBQEKmpqV2syjIzd5m5w9vv
        XrDpj0lqmgb/eJMnd3JyM8/7feece64MwBPF6uBgYnVwMLE6OJhYHRxMrA4OJlYHBxPxeuYJIl3PEs8N
        MPQ/8vxj8sIA4m/RN0R2K9657PbJheiMX4hbJ5zRcXwB4YSOY/PRfnQ+2r6Zh7bD83D94Fy0HpyDlrg5
        uHZgNpr3z0bTvvfRGPseru6ZhYbdxM6Z0H05A/UxM6CNng5t1HRotk+DJuIdVG+biuqwKVBvnYLKkMko
        C367nAIMld2Od4bQfXOAdghdRHcb3a9DeNDSz/1mopFogPmeDsI9LYTfNTD/VkWoYf61HMIvpRDuFkG4
        UwDLHQWEn3Mh3M6A8FM6LJ2psNxMgKX5CCyNX8Oki4Vy7ThxAbwo6zzhJEn5zizwHRlEOpEKrj2FuAiu
        LRHcjQRw18+Daz0N7tp34JvjwTUdJ46Au3IYXIP4pwfA6/bCVL8L5rodMNdGw6yJgLk6BOaS1RAK3GBR
        usNS6AEuxwVKfynAS7KObz+gKlvB3Uwj4Y9g2y+QMIlk34NtPQuu5TRYErKNx4ljJCRpwyFwJOTqY8Fp
        94Cr3QVeE01sh6kmHOYqkqqDwKvWwExic/7K/gAFHlIANtsFBWscxADDZDePzIXw51WwJGVbE8C2nCXh
        KbBNJ8FI0qNgqUq2Pg5s3VdgtHvBaneB08SA1USBq4oEXxUOXh0KvjIYpvJN4At9wOe6gM9zgYnkFpIL
        YvVKOfqK5FKAfD8pwHBZ2+HZMP1RS9IzYAakzJWjYHSHwNQfAFO3X5Iymt1ganaAqYoCWxUBVr0VTGUI
        uIpg8GWbwan8wRXIweWtAK8YkCtcKQBVnt/fegvJ+1SeYHNcofC1FwOMkN04OAumu+VgGkhKrTXWxcGo
        3Qdj7V4YNTthJKlRHQlj5TZiK4wVW8CWB4MpDgBT4Ac2zx1srivxSOwKnqrmSSy2X6o8X2y9HBaSiwE4
        ej5vlRRgpOxG3Eya/1ToKyOgLw+DviyE7qHI2Twe+pLNMKg2waDcAIPCF4ZcOfSU3pC1nFiG1NV24Kid
        HElFHolNVDFPYqHQEwluI6n1nlQ9yQmIAejZXB87McAoWcv+d2GsjsGDpPl4mOiEh8kLiIXIDnSEoXgd
        elI/Rm/aYvSmfwJ9xhLoM5fCmLkCTLYrUvzG0CoPBadw6xfTIuNpGswkFAq9JM6vHIk+3XaSe5PcC33F
        XrQLViKnP8Bo2bXYaTCoI/DggpNUdVfyInRdXITMjY4wlgSg59JiXF7vAP3lpdBT1Wlr7MFQF9gcNySv
        soWpNoykHkjysoGJhCalN37wGAWhaBXhg3OuYoBI9JX44Mzy4dJdDJzjPUYMYCNr2jMVenU4HiYtQLbY
        dtVadKd8hIzPxsFYGkiVL0H6OgcwNPcGarfU9uowmns5kn0ogDaC2u2DRPlo2v9RMBX5IYGqFupjYFb5
        4ZwLSXXRJPbD6WXDgAbaOQp3ZHtKAWxlV3dORnfxFtxPdEYWtb1H6U/d+BAZAWPRq9qIrpRPkbbWHobS
        IPRcXi613VgRit5Md1zwtqUdEQ5DjqdUtbg7DLneOE9VczWRYBTeOEtVczW0axReOLXkZek9wSk8kC23
        7e+AJuqtUl3Um6iPfAOlX0yCdtvr0IS/hqKgiagOeRVVWyZBGTgeFUGvoOLzicjbMA5lgRNQsnECsvwd
        oApwRNF6R2T42tHrdSy9YMbiEgVT0D5X+DogRW5DC86eWm6Hi26jpMpFUtxsyijACILeRuJiENshk415
        DOweA/t/QfwfUS6ein8fx9aO2f+Lfx7JIuKY6B1CWP1QGCyefhM+DfA0wJMOANlf0FjZsIX2l3sAAAAA
        SUVORK5CYII=
</value>
  </data>
  <data name="PictureEdit2.EditValue" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAABp0RVh0VGl0
        bGUAUnVsZXJWZXJ0aWNhbDtSdWxlcjvWKTIFAAAFh0lEQVRYR62Xe2xTVRzHQQQENhhjMF5RURRQICIG
        H/C/CuzRdgVxDIQwDHFTVgYb4f0GEXkFlASIRAfjsa3t2iKPsbF2DxiPwWBbcATBSPAfCYzee9uSfP2e
        096JyfUv7k0+6XKXc76f3++c3nPbCcD/wqsz6XJr98TK1i1j0LJ5DJo3jcbNDW/jxvq30LRuFK6vHYlr
        q0eicdUIXFnxJq4sewMXC4ef5rhusfGGc+sY3tSJTdC1bfckRP6qRfiBH6H750klwn+eQfjeKYTuehH6
        3Y3QHSe02yegtRWjoXC4GNyDvCAmMZpbx/CmDi8h0K1t1yQ8pYDWWgSt5RC0m/uhXd8H9doeqFd3QL38
        PdRLW9Besx7t/pW4WCAFepIuYhKjuXUMb+rwkgK/7ZxIAX80vPlALHwv1EYRvg1qwxaUzR+E0vkDJe65
        yWJwL/LcAqKF3W7t+AiRB1Ws/CC0pn3QRPjVnR3hysWNUOvXQmnci2DDVtTnv2aqQPfW7R8icr8iFv4D
        K4+Fs+16uFq7Eo8rC/D3cQtqHcPE4DhijkDLNgpw08nKRfgVrrmsfANK5w1EiSA7GSfmJfPvZDhnDzBV
        4KWW7yjwx8n/hKsMV+rXyMpDTXsQ4n54XPE12ity4F/4qhgcT8wRaN76ASL3PND08Avr2fY1UGqXQ61Z
        CtW/BEFfJh4enYKHx1JQnfuyuQI3v30fkbsuueay8rrVUGr08MVQTmfj+Jz+ODZngKQ0q7+pAj1ubJ6A
        yJ2SjnC1ZhnUQCEUfz7Uczl4UpaGoNOCJ+dyEazKQ9VXQ8Xg3uRFMYnR3DqGN3V4SYGmTRS4Xcy2x8Jr
        CqBU50OpzMUTVzrDKeC24NGJNDwqsaBygbkCPa9vnIBw2y/QZOUFUKsdUCu/QdBlRbAsVQoUz0qKkpWE
        ksx+YnAfYo7AtQ3vIXzrUCw8D2oVK3czvHQKFGcqFHZBKbfJ9gfPL0LF/CGmCYhN1KtxHQVa98cqz0Gw
        3MLwyax+alTAnQ7Vk4FHpRa0l9lxNnuwGJxAzBG4unY8ws0/Rjeci+td+qkUUJwpOJKZiMMz+6FoZiKK
        +HcR2398RqKpAnGX14xHpGk71zoFSsnHrJwCrD7I6lVWHwo4EK7hpjzvkN+MU3MHicF9iTkCl1a9i1Bd
        Aav+RFavUECJtV91p0Hj+mteO9qdNmLHqTnyNDRNIP7SSgrUs8JYuFo2GapzClQXBbgfNI8NP0/v28FR
        wnGJpKuYxGhuHcObOrykQMOKcewAn3giWDKV4SmsPpXVcwk8VoS80/hU5CYNLIZvtuyAeQIXlo9DuI5P
        PVG1kwIu4k6Bxg5osgMU8Nn5XMjg19MOb5Y8DfsRUwR61y97hwKLoFFAoLrYASEg1z+d62/DIXsf/JQR
        pdie8KxAZ6O5dQxv6vASm6hP3dKogAjWJCJcVJ/G9nMJvKIDGdC4BFogH55MeRglEXME6gvHIlzLDsiq
        dVIRkgKWDgHxaFbcGSj/3FyBhNoCIcDqGBwq16GAJw0Hbb1x0KoTLz+PcBk4rj8xSWDJWETq8v4Nlojq
        0xD2pvN/DsIOBfJ4YDng/kweRuYJBPJjAp5UhBkqg2PhEp8FEZ+NB5KFm9MK13QpMIDIX0dGc+sY3tTh
        JQT6+vPHUGAhwqw6Giw+RbhFEvFZcSA9nsRhf1ocDtvkEpgosGg0IrUUkJVHq46IYBke5Sk7EKrJ45ng
        gHOaPIxME0iodsQERLAvFs6qo+Hik5y0cpPymVBu1QVM2QPyMDqdOypQmTsKFTkjcHbBcJz58nX8mj0M
        J+e+At8XQ+GdNQSerMEoz0yGewZfTO1JVRynv5I9l4D8dUzEjwxxuonvtqhMR7RZkPwM4r54G+pOxBuV
        4dxR0OkfekjJZ/cdd7IAAAAASUVORK5CYII=
</value>
  </data>
  <data name="LeftAlignPictureBox.EditValue" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAABF0RVh0VGl0
        bGUAQWxpZ247TGVmdDtwpv7rAAADgElEQVRYR8WW904bQRDGKSkPERLANhBCsek2PjdMQgkGkqCQENLz
        NhABygsFUh6DIoroiCJA8Mdkvt3b4+a4YP5AZqWf8Kz35pvbb2dNERHdKr6ThcR3spD4ThYS38lCIgM9
        im1KbohS12fklZoi4AXn5+dTp6enP4+Pj2ePjo58OTw89OXg4GDufyDf2dnZFDSEpgj4S4hnh79Rdujr
        BYNfHLpATpPOfaYMkx74ZPORUs+ZfvCBea9I9o1TJN5PKII1SoSmCOwC2tIvqCaSUVQbwpoqRZpC4ZRN
        kkINmmBDwoVFwXqLAnVxB+zedQqYwML9/f25vb09h93d3Uvs7Oz88rK1taXAZ7MOz8Mi5IaG0BSBLmAa
        BfiJesWAETRsbm6qv7boDOe8w5iDmP8QnpyczGbYX3ibybGvBuOv8lj7m1SMK48TveCdwmLCsT7CW3PO
        knC0pyjc3oP8aghNEfBiHJTm5BCFGtnfRvaWCYJLHjP1cQo6HndS4EknVSpiVFkbcwqoqOkoAmYITRHw
        DnABE2gbs73YUi8bGxuXWF9f/21AjOfZhu+c8y5jbMhvARcwjQPoFveKAbcgWFtbE5hC8DxyISdyQ0No
        ioC3CwfQ9K/pYbe/id4xSvSMkdXzlqxn4A0zStbTUYp3g9fUmQUjFDN0jVB9S5ZQBDSEpgj4S/jWFB/Q
        Pcwemx7W3ro9jlLlY9BB7C+VO7RdUGVoofJQiymgVGiKQO/AJFoI2+fd1tXVVcHKyopgeXn5DzCx2wq0
        MXJDQ2iKQBcwg8V4MJ8gMKKGpaUlVQBEt7e3f3DOewwOonMXCE0R8AJ0gOV4DH8Z46/yVvtrgL+xDHhF
        sfRLRZSv8rqmDOFFbOHiukgK+dUQmiKwC+CLw/ZY++vrcTW8bdWwv+BRsFngLqAsEEF+NYSmCHQBk2gb
        9/ZiW90sLi4KFhYWBJiD/9yC+Pm9z5i7IL8FuL/RuyjguoLz8/N/vWAez+IFcH7wUvZvw5WHsBQd4O1h
        7a/2GP5G08MUTdnwtd2eAIOKVgvkJPEc1XJb43aEhtAUgV1AfVu3y2P2NsSeAuFxEz0EgQixv1RWCcKS
        ikamgR6Ua7AL+QpQ9wAqhYem1byYs+HG2OUFluF72MoXEX4brv6HBAsYHBgcHIA+vgmudQjdA8XcJM4Q
        mu7gNvCdLCS+k4XEd7JwUNE/dnqnjECQHzAAAAAASUVORK5CYII=
</value>
  </data>
  <data name="CenterAlignPictureBox.EditValue" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAABN0RVh0VGl0
        bGUAQWxpZ247Q2VudGVyO6pOXacAAAOkSURBVFhHxZbpUhNREIUDAXwIUZYEEElI2BOyEBbZAwiUKOLu
        0wgK6AspLo/BUizFTrEU64+2z517w3SYAD+ocKu+Yvom06enT98JLiK6Vxw3s4njZjZx3MwmjpvZRAbW
        ytHk3hFu2zXySk0R8BcuLi6mT09Pfx4fH/86Ojpy5PDw0JGDg4O5TCDf+fn5NDSEpgj4Q4i3D32m9sFP
        lwx8TNEGkhaJ5AdqZRL97zXvqKWP6QVvmTeKeM8EBSO9hCJYI1doikAX0JB4ThXBVkW5IWBRpkiQN9Ci
        iZPXb+Hxx2xEyeOLUmlVJAW6d2MBZ2dnX05OTkRL9/f35/b29uZ2d3cFOzs7v9PZ2tpS4Np8D/ciHz/c
        JDSEpgisZQYQw+PmqmdRBJKkiwEjaNjc3FR/tegs58jTuW41hGrVxkeYYVzmwrdE0uav8tjyN66YUB7H
        usFrRZQJhHsIT40cgVCXK9DYhXxqCU0R6OUNJFze6hZc5sK3mliSPU33mPFFyJPyuJlKnzZTiSJMJZXh
        VAHFFU0uYJbQFIG17Bbkcfun0E7TXrCxsXGF9fX1PwbEsIfv+8o58pFH57v5PcCDMoPKIWoGzYhnEgRr
        a2sCU4iZCQwy2zkDDaEpAt1yc37NGbb7G+sep1jXOEW7XlG0E7xkxij6bIwiHeAFNbeDUQob2kbJV9dO
        KAIaQlME/CGevibSb51h9ticYctbu8chKnkCmoj9paIUDZeUGeqoyFtnCnALTRFwe7hNkzh2aL9pvWnx
        6uqqYGVlRbC8vPwXmNhuBfJxd6fwkEJTBNbCoKgBZPJ5Fr6jECSzixmMqGFpaUkVANHt7e0fnKMAeXRO
        9S4QmiLQyx/qY3px6UbbUv4qby1/DfA33ApGKJwYVoT4VV5V00p4auRgcqqC6lirJTRFoBf7q+ClCvA3
        dF71uBze1luwv+Cxp1ZgL6CwNIh8aglNEVjLWIC2FfAx/IZ2oq1oL1hcXBQsLCwIsAfLeH7w8/tA54Kl
        N1qAYziLdwBEzeAZbzMJzs/P/0sH+7gH9yIH5kj/Nlw7hG6Ip59hy1/LY/gbSgxRqEUTH6TGGBhQ1EdB
        UhJJUiUfa7zUoCE0RaAL8DV02Dxmb73sKRAe19AjUBok9pcKS0BAUlzN+OlhkQW6cFMBsGAKlWY6dsB+
        7AxmPtKBZfgc7xMeaPw2XP8PCb7AqHeABuf4LrjVENoXirlLUkto2oP7wHEzmzhuZhPHzexBrv+sWVoq
        aerjMAAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="RightAlignPictureBox.EditValue" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAABJ0RVh0VGl0
        bGUAQWxpZ247UmlnaHQ7cux79gAAA21JREFUWEfFlulSU0EQhSMRXkIRQgKIEMMWCGRlERAIoFCiiLtP
        oyjLEymKT0GxFEuxFFAsxfqj7TNz53I7XEh+UGGqvmJmErrP7dN9wUNEd4rrZT5xvcwnrpf5xPUyn8iD
        XvcsCm4Jr2OPuDKnOPAXLi4uJk9PT38dHx//Pjo6cuXw8NCVg4OD2etAvPPz80nkEDnFgT9E8o6hr9Qx
        +OWSgc827SCtSaU/URuT6v9o8YGSfUwveM+8UySej1NttJcggnMUiJziYAkIp15QZW2bosIQ0pQrUhQI
        JS0SFAhq/MG4gxj5a2JUVh21QfWyCjg7O/t2cnIiSrq/vz+7t7c3u7u7K9jZ2fmTyfb2tmBra0v9RAyu
        wPdsArBMA6J5vKx6GiIgINeEbuD3WcAUYouc4mCt+sSwh73FtgC+pdIOf5XH2t+EYlx5HO8BbxUx0D3G
        vKFY12tFqKmbUFXEFDnFwVqBUMpTHx/AtgC+1cXT7Gmmx0xNlPy2x61U9qSVfIoW8lWBZvI9bqbSSk2u
        ApwW3OfyT6B8zvJubm5eYWNj46+T9fV1G5zRM/wwE4gtcoqDnoIpKDVNZ3zOTHxTQsPa2poCe8ThuNPZ
        BKiSm/k1M+z0N94zRnHbX+PxKMWejVK0E7yi1g4wQi2G9hGqaeggTEJWAXj6umi/nmH22Myw9tbpcYT9
        BdrfEpvwJeWGBioJNBgBXpFTHNgCzCrGDuU3pTclNiU1rK6uClZWVuYyWV5ensNniIN+wkOKnOKgF5pQ
        NSBTyL0wAyEQkJkQuCXMBPcQwBWY4ZhZBXiCkT4Pe4ytF2Wz/VXean8N8LelDQxTS+qlIsKv8kgSDCma
        EkNUXZskVBUxRU5xsBb76wlFurFVAoLhrqseV8DbRg37Cx756wXF/joqLqulhxa5CjAWFDJFPD4/MXYo
        tynp0tKSYHFxUbCwsPDPCe7QR2znD46ZdQyn8Q5AUtN4xttcE4L5+XkbfAex0E+c48YKeJE8c4a1v9pj
        5W9Ke6tIDFJTHAwoGmMgLYmmqYrHGi+jnATUhDsdHrO3AfYUCI/ZX6fHPhCSlD5lgvSgRINpyiYAFkxA
        6XVjB5xjZzD9kQksw+fWGGbtAfOHSL0DLIpuCcRU/5iKnOIgF8TcJvYSOZ2Hu8D1Mp+4XuYT18v8QZ7/
        YjWnprIQRqQAAAAASUVORK5CYII=
</value>
  </data>
</root>