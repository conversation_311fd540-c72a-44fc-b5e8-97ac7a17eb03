﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmSupportBSVBSV
    Inherits DevExpress.XtraEditors.XtraForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.PictureBox1 = New System.Windows.Forms.PictureBox()
        Me.BntDiscord = New DevExpress.XtraEditors.SimpleButton()
        Me.BntTelegram = New DevExpress.XtraEditors.SimpleButton()
        Me.BntSignal = New DevExpress.XtraEditors.SimpleButton()
        Me.BntWeb = New DevExpress.XtraEditors.SimpleButton()
        Me.BntShopping = New DevExpress.XtraEditors.SimpleButton()
        Me.BntTGChannel = New DevExpress.XtraEditors.SimpleButton()
        Me.BntYouTube = New DevExpress.XtraEditors.SimpleButton()
        Me.BntEmail = New DevExpress.XtraEditors.SimpleButton()
        Me.SeparatorControl10 = New DevExpress.XtraEditors.SeparatorControl()
        Me.SeparatorControl1 = New DevExpress.XtraEditors.SeparatorControl()
        Me.SeparatorControl2 = New DevExpress.XtraEditors.SeparatorControl()
        Me.SeparatorControl3 = New DevExpress.XtraEditors.SeparatorControl()
        Me.SeparatorControl4 = New DevExpress.XtraEditors.SeparatorControl()
        Me.SeparatorControl5 = New DevExpress.XtraEditors.SeparatorControl()
        Me.SeparatorControl6 = New DevExpress.XtraEditors.SeparatorControl()
        Me.SeparatorControl7 = New DevExpress.XtraEditors.SeparatorControl()
        Me.Label1 = New System.Windows.Forms.Label()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SeparatorControl10, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SeparatorControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SeparatorControl2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SeparatorControl3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SeparatorControl4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SeparatorControl5, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SeparatorControl6, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SeparatorControl7, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'PictureBox1
        '
        Me.PictureBox1.Image = Global.Best_Sender.My.Resources.Resources.BkPanelButton
        Me.PictureBox1.Location = New System.Drawing.Point(8, 5)
        Me.PictureBox1.Name = "PictureBox1"
        Me.PictureBox1.Size = New System.Drawing.Size(955, 587)
        Me.PictureBox1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom
        Me.PictureBox1.TabIndex = 0
        Me.PictureBox1.TabStop = False
        '
        'BntDiscord
        '
        Me.BntDiscord.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntDiscord.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntDiscord.Appearance.Font = New System.Drawing.Font("Comfortaa", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.BntDiscord.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntDiscord.Appearance.Options.UseBackColor = True
        Me.BntDiscord.Appearance.Options.UseBorderColor = True
        Me.BntDiscord.Appearance.Options.UseFont = True
        Me.BntDiscord.Appearance.Options.UseForeColor = True
        Me.BntDiscord.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntDiscord.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntDiscord.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntDiscord.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntDiscord.AppearanceDisabled.Options.UseBackColor = True
        Me.BntDiscord.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntDiscord.AppearanceDisabled.Options.UseFont = True
        Me.BntDiscord.AppearanceDisabled.Options.UseForeColor = True
        Me.BntDiscord.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntDiscord.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntDiscord.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntDiscord.AppearanceHovered.Options.UseBackColor = True
        Me.BntDiscord.AppearanceHovered.Options.UseBorderColor = True
        Me.BntDiscord.AppearanceHovered.Options.UseForeColor = True
        Me.BntDiscord.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntDiscord.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntDiscord.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntDiscord.AppearancePressed.Options.UseBackColor = True
        Me.BntDiscord.AppearancePressed.Options.UseBorderColor = True
        Me.BntDiscord.AppearancePressed.Options.UseForeColor = True
        Me.BntDiscord.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.discord32x32
        Me.BntDiscord.Location = New System.Drawing.Point(136, 83)
        Me.BntDiscord.Name = "BntDiscord"
        Me.BntDiscord.Size = New System.Drawing.Size(190, 52)
        Me.BntDiscord.TabIndex = 339
        Me.BntDiscord.Text = "Discord"
        '
        'BntTelegram
        '
        Me.BntTelegram.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntTelegram.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntTelegram.Appearance.Font = New System.Drawing.Font("Comfortaa", 12.0!, System.Drawing.FontStyle.Bold)
        Me.BntTelegram.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntTelegram.Appearance.Options.UseBackColor = True
        Me.BntTelegram.Appearance.Options.UseBorderColor = True
        Me.BntTelegram.Appearance.Options.UseFont = True
        Me.BntTelegram.Appearance.Options.UseForeColor = True
        Me.BntTelegram.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntTelegram.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntTelegram.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntTelegram.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntTelegram.AppearanceDisabled.Options.UseBackColor = True
        Me.BntTelegram.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntTelegram.AppearanceDisabled.Options.UseFont = True
        Me.BntTelegram.AppearanceDisabled.Options.UseForeColor = True
        Me.BntTelegram.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntTelegram.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntTelegram.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntTelegram.AppearanceHovered.Options.UseBackColor = True
        Me.BntTelegram.AppearanceHovered.Options.UseBorderColor = True
        Me.BntTelegram.AppearanceHovered.Options.UseForeColor = True
        Me.BntTelegram.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntTelegram.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntTelegram.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntTelegram.AppearancePressed.Options.UseBackColor = True
        Me.BntTelegram.AppearancePressed.Options.UseBorderColor = True
        Me.BntTelegram.AppearancePressed.Options.UseForeColor = True
        Me.BntTelegram.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.telegram32x32
        Me.BntTelegram.Location = New System.Drawing.Point(393, 83)
        Me.BntTelegram.Name = "BntTelegram"
        Me.BntTelegram.Size = New System.Drawing.Size(190, 52)
        Me.BntTelegram.TabIndex = 340
        Me.BntTelegram.Text = "Telegram"
        '
        'BntSignal
        '
        Me.BntSignal.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntSignal.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntSignal.Appearance.Font = New System.Drawing.Font("Comfortaa", 12.0!, System.Drawing.FontStyle.Bold)
        Me.BntSignal.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntSignal.Appearance.Options.UseBackColor = True
        Me.BntSignal.Appearance.Options.UseBorderColor = True
        Me.BntSignal.Appearance.Options.UseFont = True
        Me.BntSignal.Appearance.Options.UseForeColor = True
        Me.BntSignal.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntSignal.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntSignal.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntSignal.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntSignal.AppearanceDisabled.Options.UseBackColor = True
        Me.BntSignal.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntSignal.AppearanceDisabled.Options.UseFont = True
        Me.BntSignal.AppearanceDisabled.Options.UseForeColor = True
        Me.BntSignal.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntSignal.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntSignal.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntSignal.AppearanceHovered.Options.UseBackColor = True
        Me.BntSignal.AppearanceHovered.Options.UseBorderColor = True
        Me.BntSignal.AppearanceHovered.Options.UseForeColor = True
        Me.BntSignal.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntSignal.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntSignal.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntSignal.AppearancePressed.Options.UseBackColor = True
        Me.BntSignal.AppearancePressed.Options.UseBorderColor = True
        Me.BntSignal.AppearancePressed.Options.UseForeColor = True
        Me.BntSignal.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.signal_32x32
        Me.BntSignal.Location = New System.Drawing.Point(640, 83)
        Me.BntSignal.Name = "BntSignal"
        Me.BntSignal.Size = New System.Drawing.Size(190, 52)
        Me.BntSignal.TabIndex = 342
        Me.BntSignal.Text = "Signal"
        '
        'BntWeb
        '
        Me.BntWeb.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntWeb.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntWeb.Appearance.Font = New System.Drawing.Font("Comfortaa", 12.0!, System.Drawing.FontStyle.Bold)
        Me.BntWeb.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntWeb.Appearance.Options.UseBackColor = True
        Me.BntWeb.Appearance.Options.UseBorderColor = True
        Me.BntWeb.Appearance.Options.UseFont = True
        Me.BntWeb.Appearance.Options.UseForeColor = True
        Me.BntWeb.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntWeb.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntWeb.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntWeb.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntWeb.AppearanceDisabled.Options.UseBackColor = True
        Me.BntWeb.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntWeb.AppearanceDisabled.Options.UseFont = True
        Me.BntWeb.AppearanceDisabled.Options.UseForeColor = True
        Me.BntWeb.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntWeb.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntWeb.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntWeb.AppearanceHovered.Options.UseBackColor = True
        Me.BntWeb.AppearanceHovered.Options.UseBorderColor = True
        Me.BntWeb.AppearanceHovered.Options.UseForeColor = True
        Me.BntWeb.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntWeb.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntWeb.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntWeb.AppearancePressed.Options.UseBackColor = True
        Me.BntWeb.AppearancePressed.Options.UseBorderColor = True
        Me.BntWeb.AppearancePressed.Options.UseForeColor = True
        Me.BntWeb.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.web23x23
        Me.BntWeb.Location = New System.Drawing.Point(640, 221)
        Me.BntWeb.Name = "BntWeb"
        Me.BntWeb.Size = New System.Drawing.Size(190, 52)
        Me.BntWeb.TabIndex = 345
        Me.BntWeb.Text = "Web Site"
        '
        'BntShopping
        '
        Me.BntShopping.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntShopping.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntShopping.Appearance.Font = New System.Drawing.Font("Comfortaa", 12.0!, System.Drawing.FontStyle.Bold)
        Me.BntShopping.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntShopping.Appearance.Options.UseBackColor = True
        Me.BntShopping.Appearance.Options.UseBorderColor = True
        Me.BntShopping.Appearance.Options.UseFont = True
        Me.BntShopping.Appearance.Options.UseForeColor = True
        Me.BntShopping.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntShopping.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntShopping.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntShopping.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntShopping.AppearanceDisabled.Options.UseBackColor = True
        Me.BntShopping.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntShopping.AppearanceDisabled.Options.UseFont = True
        Me.BntShopping.AppearanceDisabled.Options.UseForeColor = True
        Me.BntShopping.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntShopping.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntShopping.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntShopping.AppearanceHovered.Options.UseBackColor = True
        Me.BntShopping.AppearanceHovered.Options.UseBorderColor = True
        Me.BntShopping.AppearanceHovered.Options.UseForeColor = True
        Me.BntShopping.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntShopping.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntShopping.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntShopping.AppearancePressed.Options.UseBackColor = True
        Me.BntShopping.AppearancePressed.Options.UseBorderColor = True
        Me.BntShopping.AppearancePressed.Options.UseForeColor = True
        Me.BntShopping.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.shopping_32x32
        Me.BntShopping.Location = New System.Drawing.Point(393, 221)
        Me.BntShopping.Name = "BntShopping"
        Me.BntShopping.Size = New System.Drawing.Size(190, 52)
        Me.BntShopping.TabIndex = 344
        Me.BntShopping.Text = "Shopping"
        '
        'BntTGChannel
        '
        Me.BntTGChannel.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntTGChannel.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntTGChannel.Appearance.Font = New System.Drawing.Font("Comfortaa", 12.0!, System.Drawing.FontStyle.Bold)
        Me.BntTGChannel.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntTGChannel.Appearance.Options.UseBackColor = True
        Me.BntTGChannel.Appearance.Options.UseBorderColor = True
        Me.BntTGChannel.Appearance.Options.UseFont = True
        Me.BntTGChannel.Appearance.Options.UseForeColor = True
        Me.BntTGChannel.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntTGChannel.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntTGChannel.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntTGChannel.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntTGChannel.AppearanceDisabled.Options.UseBackColor = True
        Me.BntTGChannel.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntTGChannel.AppearanceDisabled.Options.UseFont = True
        Me.BntTGChannel.AppearanceDisabled.Options.UseForeColor = True
        Me.BntTGChannel.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntTGChannel.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntTGChannel.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntTGChannel.AppearanceHovered.Options.UseBackColor = True
        Me.BntTGChannel.AppearanceHovered.Options.UseBorderColor = True
        Me.BntTGChannel.AppearanceHovered.Options.UseForeColor = True
        Me.BntTGChannel.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntTGChannel.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntTGChannel.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntTGChannel.AppearancePressed.Options.UseBackColor = True
        Me.BntTGChannel.AppearancePressed.Options.UseBorderColor = True
        Me.BntTGChannel.AppearancePressed.Options.UseForeColor = True
        Me.BntTGChannel.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.telegram32x32
        Me.BntTGChannel.Location = New System.Drawing.Point(136, 221)
        Me.BntTGChannel.Name = "BntTGChannel"
        Me.BntTGChannel.Size = New System.Drawing.Size(190, 52)
        Me.BntTGChannel.TabIndex = 343
        Me.BntTGChannel.Text = "TG Channel"
        '
        'BntYouTube
        '
        Me.BntYouTube.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntYouTube.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntYouTube.Appearance.Font = New System.Drawing.Font("Comfortaa", 12.0!, System.Drawing.FontStyle.Bold)
        Me.BntYouTube.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntYouTube.Appearance.Options.UseBackColor = True
        Me.BntYouTube.Appearance.Options.UseBorderColor = True
        Me.BntYouTube.Appearance.Options.UseFont = True
        Me.BntYouTube.Appearance.Options.UseForeColor = True
        Me.BntYouTube.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntYouTube.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntYouTube.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntYouTube.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntYouTube.AppearanceDisabled.Options.UseBackColor = True
        Me.BntYouTube.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntYouTube.AppearanceDisabled.Options.UseFont = True
        Me.BntYouTube.AppearanceDisabled.Options.UseForeColor = True
        Me.BntYouTube.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntYouTube.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntYouTube.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntYouTube.AppearanceHovered.Options.UseBackColor = True
        Me.BntYouTube.AppearanceHovered.Options.UseBorderColor = True
        Me.BntYouTube.AppearanceHovered.Options.UseForeColor = True
        Me.BntYouTube.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntYouTube.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntYouTube.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntYouTube.AppearancePressed.Options.UseBackColor = True
        Me.BntYouTube.AppearancePressed.Options.UseBorderColor = True
        Me.BntYouTube.AppearancePressed.Options.UseForeColor = True
        Me.BntYouTube.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.youtube32x32
        Me.BntYouTube.Location = New System.Drawing.Point(136, 361)
        Me.BntYouTube.Name = "BntYouTube"
        Me.BntYouTube.Size = New System.Drawing.Size(190, 52)
        Me.BntYouTube.TabIndex = 341
        Me.BntYouTube.Text = "YouTube"
        '
        'BntEmail
        '
        Me.BntEmail.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntEmail.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntEmail.Appearance.Font = New System.Drawing.Font("Comfortaa", 12.0!, System.Drawing.FontStyle.Bold)
        Me.BntEmail.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntEmail.Appearance.Options.UseBackColor = True
        Me.BntEmail.Appearance.Options.UseBorderColor = True
        Me.BntEmail.Appearance.Options.UseFont = True
        Me.BntEmail.Appearance.Options.UseForeColor = True
        Me.BntEmail.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntEmail.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntEmail.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntEmail.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntEmail.AppearanceDisabled.Options.UseBackColor = True
        Me.BntEmail.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntEmail.AppearanceDisabled.Options.UseFont = True
        Me.BntEmail.AppearanceDisabled.Options.UseForeColor = True
        Me.BntEmail.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntEmail.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntEmail.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntEmail.AppearanceHovered.Options.UseBackColor = True
        Me.BntEmail.AppearanceHovered.Options.UseBorderColor = True
        Me.BntEmail.AppearanceHovered.Options.UseForeColor = True
        Me.BntEmail.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntEmail.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntEmail.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntEmail.AppearancePressed.Options.UseBackColor = True
        Me.BntEmail.AppearancePressed.Options.UseBorderColor = True
        Me.BntEmail.AppearancePressed.Options.UseForeColor = True
        Me.BntEmail.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Emailimage
        Me.BntEmail.Location = New System.Drawing.Point(393, 365)
        Me.BntEmail.Name = "BntEmail"
        Me.BntEmail.Size = New System.Drawing.Size(190, 52)
        Me.BntEmail.TabIndex = 346
        Me.BntEmail.Text = "Email"
        '
        'SeparatorControl10
        '
        Me.SeparatorControl10.BackColor = System.Drawing.Color.FromArgb(CType(CType(27, Byte), Integer), CType(CType(27, Byte), Integer), CType(CType(26, Byte), Integer))
        Me.SeparatorControl10.LineThickness = 1
        Me.SeparatorControl10.Location = New System.Drawing.Point(136, 127)
        Me.SeparatorControl10.LookAndFeel.SkinName = "DevExpress Style"
        Me.SeparatorControl10.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SeparatorControl10.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.SeparatorControl10.Name = "SeparatorControl10"
        Me.SeparatorControl10.Padding = New System.Windows.Forms.Padding(9, 10, 9, 10)
        Me.SeparatorControl10.Size = New System.Drawing.Size(190, 26)
        Me.SeparatorControl10.TabIndex = 347
        '
        'SeparatorControl1
        '
        Me.SeparatorControl1.BackColor = System.Drawing.Color.FromArgb(CType(CType(27, Byte), Integer), CType(CType(27, Byte), Integer), CType(CType(26, Byte), Integer))
        Me.SeparatorControl1.LineThickness = 1
        Me.SeparatorControl1.Location = New System.Drawing.Point(393, 127)
        Me.SeparatorControl1.LookAndFeel.SkinName = "DevExpress Style"
        Me.SeparatorControl1.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SeparatorControl1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.SeparatorControl1.Name = "SeparatorControl1"
        Me.SeparatorControl1.Padding = New System.Windows.Forms.Padding(9, 10, 9, 10)
        Me.SeparatorControl1.Size = New System.Drawing.Size(190, 26)
        Me.SeparatorControl1.TabIndex = 347
        '
        'SeparatorControl2
        '
        Me.SeparatorControl2.BackColor = System.Drawing.Color.FromArgb(CType(CType(27, Byte), Integer), CType(CType(27, Byte), Integer), CType(CType(26, Byte), Integer))
        Me.SeparatorControl2.LineThickness = 1
        Me.SeparatorControl2.Location = New System.Drawing.Point(640, 127)
        Me.SeparatorControl2.LookAndFeel.SkinName = "DevExpress Style"
        Me.SeparatorControl2.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SeparatorControl2.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.SeparatorControl2.Name = "SeparatorControl2"
        Me.SeparatorControl2.Padding = New System.Windows.Forms.Padding(9, 10, 9, 10)
        Me.SeparatorControl2.Size = New System.Drawing.Size(190, 26)
        Me.SeparatorControl2.TabIndex = 347
        '
        'SeparatorControl3
        '
        Me.SeparatorControl3.BackColor = System.Drawing.Color.FromArgb(CType(CType(27, Byte), Integer), CType(CType(27, Byte), Integer), CType(CType(26, Byte), Integer))
        Me.SeparatorControl3.LineThickness = 1
        Me.SeparatorControl3.Location = New System.Drawing.Point(136, 269)
        Me.SeparatorControl3.LookAndFeel.SkinName = "DevExpress Style"
        Me.SeparatorControl3.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SeparatorControl3.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.SeparatorControl3.Name = "SeparatorControl3"
        Me.SeparatorControl3.Padding = New System.Windows.Forms.Padding(9, 10, 9, 10)
        Me.SeparatorControl3.Size = New System.Drawing.Size(190, 26)
        Me.SeparatorControl3.TabIndex = 347
        '
        'SeparatorControl4
        '
        Me.SeparatorControl4.BackColor = System.Drawing.Color.FromArgb(CType(CType(27, Byte), Integer), CType(CType(27, Byte), Integer), CType(CType(26, Byte), Integer))
        Me.SeparatorControl4.LineThickness = 1
        Me.SeparatorControl4.Location = New System.Drawing.Point(393, 269)
        Me.SeparatorControl4.LookAndFeel.SkinName = "DevExpress Style"
        Me.SeparatorControl4.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SeparatorControl4.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.SeparatorControl4.Name = "SeparatorControl4"
        Me.SeparatorControl4.Padding = New System.Windows.Forms.Padding(9, 10, 9, 10)
        Me.SeparatorControl4.Size = New System.Drawing.Size(190, 26)
        Me.SeparatorControl4.TabIndex = 347
        '
        'SeparatorControl5
        '
        Me.SeparatorControl5.BackColor = System.Drawing.Color.FromArgb(CType(CType(27, Byte), Integer), CType(CType(27, Byte), Integer), CType(CType(26, Byte), Integer))
        Me.SeparatorControl5.LineThickness = 1
        Me.SeparatorControl5.Location = New System.Drawing.Point(640, 269)
        Me.SeparatorControl5.LookAndFeel.SkinName = "DevExpress Style"
        Me.SeparatorControl5.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SeparatorControl5.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.SeparatorControl5.Name = "SeparatorControl5"
        Me.SeparatorControl5.Padding = New System.Windows.Forms.Padding(9, 10, 9, 10)
        Me.SeparatorControl5.Size = New System.Drawing.Size(190, 26)
        Me.SeparatorControl5.TabIndex = 347
        '
        'SeparatorControl6
        '
        Me.SeparatorControl6.BackColor = System.Drawing.Color.FromArgb(CType(CType(27, Byte), Integer), CType(CType(27, Byte), Integer), CType(CType(26, Byte), Integer))
        Me.SeparatorControl6.LineThickness = 1
        Me.SeparatorControl6.Location = New System.Drawing.Point(136, 415)
        Me.SeparatorControl6.LookAndFeel.SkinName = "DevExpress Style"
        Me.SeparatorControl6.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SeparatorControl6.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.SeparatorControl6.Name = "SeparatorControl6"
        Me.SeparatorControl6.Padding = New System.Windows.Forms.Padding(9, 10, 9, 10)
        Me.SeparatorControl6.Size = New System.Drawing.Size(190, 26)
        Me.SeparatorControl6.TabIndex = 347
        '
        'SeparatorControl7
        '
        Me.SeparatorControl7.BackColor = System.Drawing.Color.FromArgb(CType(CType(27, Byte), Integer), CType(CType(27, Byte), Integer), CType(CType(26, Byte), Integer))
        Me.SeparatorControl7.LineThickness = 1
        Me.SeparatorControl7.Location = New System.Drawing.Point(393, 415)
        Me.SeparatorControl7.LookAndFeel.SkinName = "DevExpress Style"
        Me.SeparatorControl7.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SeparatorControl7.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.SeparatorControl7.Name = "SeparatorControl7"
        Me.SeparatorControl7.Padding = New System.Windows.Forms.Padding(9, 10, 9, 10)
        Me.SeparatorControl7.Size = New System.Drawing.Size(190, 26)
        Me.SeparatorControl7.TabIndex = 347
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.BackColor = System.Drawing.Color.FromArgb(CType(CType(27, Byte), Integer), CType(CType(27, Byte), Integer), CType(CType(26, Byte), Integer))
        Me.Label1.Font = New System.Drawing.Font("Comfortaa", 11.25!)
        Me.Label1.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.Label1.Location = New System.Drawing.Point(601, 377)
        Me.Label1.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(229, 24)
        Me.Label1.TabIndex = 348
        Me.Label1.Text = "<EMAIL>"
        '
        'frmSupportBSVBSV
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 18.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(980, 604)
        Me.Controls.Add(Me.BntSignal)
        Me.Controls.Add(Me.BntTelegram)
        Me.Controls.Add(Me.BntDiscord)
        Me.Controls.Add(Me.BntTGChannel)
        Me.Controls.Add(Me.BntShopping)
        Me.Controls.Add(Me.BntWeb)
        Me.Controls.Add(Me.BntEmail)
        Me.Controls.Add(Me.BntYouTube)
        Me.Controls.Add(Me.Label1)
        Me.Controls.Add(Me.SeparatorControl7)
        Me.Controls.Add(Me.SeparatorControl6)
        Me.Controls.Add(Me.SeparatorControl5)
        Me.Controls.Add(Me.SeparatorControl4)
        Me.Controls.Add(Me.SeparatorControl3)
        Me.Controls.Add(Me.SeparatorControl2)
        Me.Controls.Add(Me.SeparatorControl1)
        Me.Controls.Add(Me.SeparatorControl10)
        Me.Controls.Add(Me.PictureBox1)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle
        Me.IconOptions.ShowIcon = False
        Me.LookAndFeel.SkinName = "WXI"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Name = "frmSupportBSVBSV"
        Me.ShowInTaskbar = False
        Me.Text = "Support"
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SeparatorControl10, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SeparatorControl1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SeparatorControl2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SeparatorControl3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SeparatorControl4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SeparatorControl5, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SeparatorControl6, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SeparatorControl7, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub

    Friend WithEvents PictureBox1 As PictureBox
    Friend WithEvents BntDiscord As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents BntTelegram As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents BntSignal As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents BntWeb As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents BntShopping As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents BntTGChannel As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents BntYouTube As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents BntEmail As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SeparatorControl10 As DevExpress.XtraEditors.SeparatorControl
    Friend WithEvents SeparatorControl1 As DevExpress.XtraEditors.SeparatorControl
    Friend WithEvents SeparatorControl2 As DevExpress.XtraEditors.SeparatorControl
    Friend WithEvents SeparatorControl3 As DevExpress.XtraEditors.SeparatorControl
    Friend WithEvents SeparatorControl4 As DevExpress.XtraEditors.SeparatorControl
    Friend WithEvents SeparatorControl5 As DevExpress.XtraEditors.SeparatorControl
    Friend WithEvents SeparatorControl6 As DevExpress.XtraEditors.SeparatorControl
    Friend WithEvents SeparatorControl7 As DevExpress.XtraEditors.SeparatorControl
    Friend WithEvents Label1 As Label
End Class
