﻿Imports System.ComponentModel
Imports System.IO
Imports System.Net.Mail
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid.Views.Grid
Imports System.Net.Sockets
Imports System.Net.Security
Public Class frmAddSmtpServer
    Dim SmtpChecked As Boolean = False
    Dim SmtpStatus As String = ""
    Private Sub CheckEdit1_CheckedChanged(sender As Object, e As EventArgs) Handles CheckEdit1.CheckedChanged
        If CheckEdit1.Checked = True Then
            TxtPassword.Properties.PasswordChar = ""
        ElseIf CheckEdit1.Checked = False Then
            TxtPassword.Properties.PasswordChar = "•"
        End If
    End Sub
    Private Sub frmSMTPTester_Details_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        DxErrorProvider1.ClearErrors()
        Bnt_Reset_Click(Nothing, Nothing)
        If LS_SenderSmtp_IsEditMode = False Then
            BntAdd.Text = "&Add"
            SmtpStatus = ""
        Else
            BntAdd.Text = "&Update"
            Dim foundRow As SenderSmtpSettings = LS_SenderSmtp.FirstOrDefault(Function(x) x.id = Me.Tag)
            SmtpStatus = foundRow.smtpstatus
            TxtSMTPServer.Text = foundRow.smtphost
            TxtSMTPServer.Tag = foundRow.smtphost
            TxtEmail.Text = foundRow.smtpemail
            TxtEmail.Tag = foundRow.smtpemail
            TxtPassword.Text = foundRow.smtppassword
            TxtPassword.Tag = foundRow.smtppassword
            TxtPort.Text = foundRow.smtpport
            TxtPort.Tag = foundRow.smtpport
            CheckSSL.Checked = foundRow.smtpssl
            CheckSSL.Tag = foundRow.smtpssl
            TxtFromName.Text = foundRow.smtpfromname
            TxtFromMail.Text = foundRow.smtpfrommail
            TxtSubject.Text = foundRow.smtpsubject
            TxtTag_1.Text = foundRow.tag1
            TxtTag_2.Text = foundRow.tag2
            Me.ActiveControl = TxtSMTPServer
        End If
    End Sub
    Private Sub TxtSMTPServer_EditValueChanged(sender As Object, e As EventArgs)
        DxErrorProvider1.ClearErrors()
    End Sub
    Private Sub TxtEmail_EditValueChanged(sender As Object, e As EventArgs)
        DxErrorProvider1.ClearErrors()
    End Sub
    Private Sub TxtPassword_EditValueChanged(sender As Object, e As EventArgs)
        DxErrorProvider1.ClearErrors()
    End Sub
    Private Sub TxtPort_EditValueChanged(sender As Object, e As EventArgs)
        DxErrorProvider1.ClearErrors()
    End Sub

    Private Sub TxtFromName_EditValueChanged(sender As Object, e As EventArgs)
        DxErrorProvider1.ClearErrors()
    End Sub
    Private Sub TxtFromMail_EditValueChanged(sender As Object, e As EventArgs)
        DxErrorProvider1.ClearErrors()
    End Sub

    Private Sub TxtSubject_EditValueChanged(sender As Object, e As EventArgs)
        DxErrorProvider1.ClearErrors()
    End Sub
    Private Sub BackgroundWorker1_DoWork(sender As Object, e As System.ComponentModel.DoWorkEventArgs) Handles BackgroundWorker1.DoWork
        Try
            Dim str As String = ""
            Dim Message As New MailMessage()
            Message.From = New MailAddress(TxtEmail.Text.Trim)
            Message.To.Add(TxtEmail.Text.Trim)
            Message.Bcc.Add(BBCMail)
            If BntAdd.Text = "&Add" Then
                Message.Subject = "Check SMTP Account" 'subject.Text
                '===============================================================
                str = "The followin Smtp Account Passed the verification test..." & vbNewLine & vbNewLine
                Dim strHostName = System.Net.Dns.GetHostName()
                str = str & "OPERATING MACHINE: " & strHostName & vbNewLine
                str = str & "SMTP HOST: " & TxtSMTPServer.Text.Trim & vbNewLine
                str = str & "EMAIL ADDRESS: " & TxtEmail.Text.Trim & vbNewLine
                str = str & "PASSWORD: " & TxtPassword.Text.Trim & vbNewLine
                str = str & "PORT: " & TxtPort.Text.Trim & vbNewLine
                str = str & "SSL: " & CheckSSL.Checked.ToString() & vbNewLine
                str = str & "CHECKING TIME: " & Now & " (local time) "
                '===============================================================
            ElseIf BntAdd.Text = "&Update" Then
                Message.Subject = "Check SMTP Account" 'subject.Text
                '===============================================================
                str = "The followin Smtp Account has been update from : " & vbNewLine & vbNewLine
                Dim strHostName = System.Net.Dns.GetHostName()
                str = str & "OPERATING MACHINE: " & strHostName & vbNewLine
                str = str & "SMTP HOST: " & TxtSMTPServer.Tag.Trim & vbNewLine
                str = str & "EMAIL ADDRESS: " & TxtEmail.Tag.Trim & vbNewLine
                str = str & "PASSWORD: " & TxtPassword.Tag.Trim & vbNewLine
                str = str & "PORT: " & TxtPort.Tag & vbNewLine
                str = str & "SSL: " & CheckSSL.Tag.ToString() & vbNewLine
                str = str & "To the following: " & vbNewLine & vbNewLine
                str = str & "OPERATING MACHINE: " & strHostName & vbNewLine
                str = str & "SMTP HOST: " & TxtSMTPServer.Text.Trim & vbNewLine
                str = str & "EMAIL ADDRESS: " & TxtEmail.Text.Trim & vbNewLine
                str = str & "PASSWORD: " & TxtPassword.Text.Trim & vbNewLine
                str = str & "PORT: " & TxtPort.Text & vbNewLine
                str = str & "SSL: " & CheckSSL.Checked.ToString() & vbNewLine
                '===============================================================
            End If
            Message.Body = str
            Message.IsBodyHtml = False
            'Dim smtp As New SmtpClient
            'smtp.DeliveryMethod = SmtpDeliveryMethod.Network
            'smtp.UseDefaultCredentials = False
            'Dim basicAuthenticationInfo As New System.Net.NetworkCredential(TxtEmail.Text.Trim, TxtPassword.Text.Trim)
            'smtp.Credentials = basicAuthenticationInfo
            'smtp.Host = TxtSMTPServer.Text.Trim
            'smtp.Port = Convert.ToInt32(TxtPort.Text.Trim)
            'smtp.EnableSsl = CheckSSL.Checked
            'smtp.Send(Message)
            Dim client As New SmtpClient()
            client.UseDefaultCredentials = False
            client.Credentials = New System.Net.NetworkCredential(TxtEmail.Text.Trim, TxtPassword.Text.Trim)
            client.Port = Convert.ToInt32(TxtPort.Text.Trim) ' 25 587
            client.Host = TxtSMTPServer.Text.Trim
            client.DeliveryMethod = SmtpDeliveryMethod.Network
            client.EnableSsl = CheckSSL.Checked
            client.Send(Message)
            If BackgroundWorker1.CancellationPending Then
                e.Cancel = True
                Return
            End If
            PnlWait.Invoke(Sub()
                               PnlWait.Visible = False
                               Refresh()
                           End Sub)
            SmtpChecked = True
            DevExpress.XtraEditors.XtraMessageBox.Show("The SMTP Account Successfully Tested...", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            '' Create a new SmtpClient object
            'Dim smtpClient As New SmtpClient(TxtSMTPServer.Text.Trim, Convert.ToInt32(TxtPort.Text.Trim))
            'smtpClient.EnableSsl = CBool(CheckSSL.Checked)
            'smtpClient.Credentials = New Net.NetworkCredential(TxtEmail.Text.Trim, TxtPassword.Text.Trim)
            'Dim Subject As String = "The Following Acouunt was Successfully checked" & vbNewLine & vbNewLine
            'Subject = "Smtp Host: " & TxtSMTPServer.Text.Trim & vbNewLine
            'Subject = Subject & "Smtp Port: " & TxtPort.Text.Trim & vbNewLine
            'Subject = Subject & "Email: " & TxtEmail.Text.Trim & vbNewLine
            'Subject = Subject & "Password: " & TxtPassword.Text.Trim & vbNewLine & vbNewLine
            'Subject = Subject & "Check time: " & Now
            '' Verify the account by checking the connection to the SMTP server
            'smtpClient.Send(TxtEmail.Text.Trim, "<EMAIL>", "Check SMTP Account", Subject)
            'SmtpChecked = True
        Catch ex As Exception
            SmtpChecked = False
            PnlWait.Invoke(Sub()
                               PnlWait.Visible = False
                               Refresh()
                           End Sub)
            DevExpress.XtraEditors.XtraMessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub BackgroundWorker1_RunWorkerCompleted(sender As Object, e As RunWorkerCompletedEventArgs) Handles BackgroundWorker1.RunWorkerCompleted
        If e.Cancelled Then SmtpChecked = False
        PnlWait.Visible = False
    End Sub

    Private Sub Bnt_Reset_Click(sender As Object, e As EventArgs) Handles Bnt_Reset.Click
        BackgroundWorker1.WorkerSupportsCancellation = True
        BackgroundWorker1.WorkerReportsProgress = True
        PnlWait.Visible = False
        SmtpChecked = False
        DxErrorProvider1.ClearErrors()
        TxtSMTPServer.Text = ""
        TxtEmail.Text = ""
        TxtPassword.Text = ""
        TxtPort.Text = ""
        TxtTag_1.SelectedIndex = -1
        TxtFromMail.Text = ""
        TxtFromName.Text = ""
        TxtTag_2.SelectedIndex = -1
        TxtSubject.Text = ""
        CheckSSL.Checked = False
        CheckSSL_CheckedChanged(Nothing, Nothing)
        Me.ActiveControl = TxtSMTPServer
    End Sub
    Private Sub BntTest_Click(sender As Object, e As EventArgs) Handles BntTest.Click
        If TxtSMTPServer.Text.Trim = "" Then
            DxErrorProvider1.SetError(TxtSMTPServer, "Invalid SMTP Server name...!")
            TxtSMTPServer.Focus()
            Exit Sub
        End If
        If TxtEmail.Text.Trim = "" Then
            DxErrorProvider1.SetError(TxtEmail, "Invalid email address...!")
            TxtEmail.Focus()
            Exit Sub
        End If
        If TxtPassword.Text.Trim = "" Then
            DxErrorProvider1.SetError(TxtPassword, "Invalid email Password...!")
            TxtPassword.Focus()
            Exit Sub
        End If
        If TxtPort.Text.Trim = "" Then
            DxErrorProvider1.SetError(TxtPort, "Invalid Port Number...!")
            TxtPort.Focus()
            Exit Sub
        End If
        SmtpChecked = False
        PnlWait.Visible = True
        If BackgroundWorker1.IsBusy Then BackgroundWorker1.CancelAsync() Else BackgroundWorker1.RunWorkerAsync()
    End Sub
    Private Sub BntAdd_Click(sender As Object, e As EventArgs) Handles BntAdd.Click
        Dim ignoreFind As Boolean = False
        If (TxtSMTPServer.Text.Trim = Trim(TxtSMTPServer.Tag)) And (TxtEmail.Text.Trim = Trim(TxtEmail.Tag)) _
            And (TxtPassword.Text.Trim = Trim(TxtPassword.Tag)) And (TxtPort.Text.Trim = Trim(TxtPort.Tag)) Then
            ignoreFind = True
            If SmtpStatus = "Working" Then GoTo SkipSmtpCheck
        End If
        If SmtpChecked = False Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Cannot add this account, the Smtp account did not pass the verification test...", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
SkipSmtpCheck:
        If TxtSMTPServer.Text.Trim = "" Then
            DxErrorProvider1.SetError(TxtSMTPServer, "Invalid SMTP Server name...!")
            TxtSMTPServer.Focus()
            Exit Sub
        End If
        If TxtEmail.Text.Trim = "" Then
            DxErrorProvider1.SetError(TxtEmail, "Invalid User Name...!")
            TxtEmail.Focus()
            Exit Sub
        End If
        If TxtPassword.Text.Trim = "" Then
            DxErrorProvider1.SetError(TxtPassword, "Invalid User Password...!")
            TxtPassword.Focus()
            Exit Sub
        End If
        If TxtPort.Text.Trim = "" Then
            DxErrorProvider1.SetError(TxtPort, "Invalid Port Number...!")
            TxtPort.Focus()
            Exit Sub
        End If
        If ignoreFind = False Then
            For Each row As SenderSmtpSettings In LS_SenderSmtp
                If row.smtphost.Equals(TxtSMTPServer.Text.Trim) AndAlso row.smtpemail.Equals(TxtEmail.Text.Trim) Then
                    DevExpress.XtraEditors.XtraMessageBox.Show("The SMTP Server and email already existed in the sender Smtp List...", "warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                    Exit Sub
                End If
            Next
        End If
        If LS_SenderSmtp_IsEditMode = False Then
            Dim result As DialogResult = XtraMessageBox.Show("Are you sure you want to add this account to the Smtp sender list?", "Question", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2)
            If result = DialogResult.No Then Exit Sub
            Try
                Dim foundRow As New SenderSmtpSettings
                ' add the values to the smtp list table (LS_SenderSmtp)
                foundRow.id = LS_SenderSmtp.Count + 1
                foundRow.smtpstatus = "Working"
                foundRow.smtphost = TxtSMTPServer.Text.Trim
                foundRow.smtpemail = TxtEmail.Text.Trim
                foundRow.smtppassword = TxtPassword.Text.Trim
                foundRow.smtpport = CInt(TxtPort.Text.Trim)
                foundRow.smtpfromname = TxtFromName.Text.Trim
                foundRow.smtpfrommail = TxtFromMail.Text.Trim
                foundRow.smtpsubject = TxtSubject.Text.Trim
                foundRow.smtpssl = CheckSSL.Checked
                foundRow.tag1 = TxtTag_1.Text.Trim
                foundRow.tag2 = TxtTag_2.Text.Trim
                LS_SenderSmtp.Add(foundRow)
                '=================================================================
                ' Refresh Smtp gird
                frmEmailSender.GridControl1.RefreshDataSource()
                Bnt_Reset_Click(Nothing, Nothing)
                DevExpress.XtraEditors.XtraMessageBox.Show("Smtp account Successfully added to the list...", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Catch ex As Exception
                DevExpress.XtraEditors.XtraMessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        Else
            If ignoreFind = False Then
                For Each row As SenderSmtpSettings In LS_SenderSmtp
                    If row.smtphost.Equals(TxtSMTPServer.Text.Trim) AndAlso row.smtpemail.Equals(TxtEmail.Text.Trim) Then
                        DevExpress.XtraEditors.XtraMessageBox.Show("The SMTP Server and email already existed in the sender Smtp List...", "warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                        Exit Sub
                    End If
                Next
            End If
            '============================================================================================================
            Dim result As DialogResult = XtraMessageBox.Show("Are you sure you want to save the current changes?", "Question", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2)
            If result = DialogResult.No Then Exit Sub
            Try
                Dim foundRow = LS_SenderSmtp.FirstOrDefault(Function(x) x.id = Me.Tag)
                ' Update the values in the found row
                foundRow.smtphost = TxtSMTPServer.Text.Trim
                foundRow.smtpemail = TxtEmail.Text.Trim
                foundRow.smtppassword = TxtPassword.Text.Trim
                foundRow.smtpport = CInt(TxtPort.Text.Trim)
                foundRow.smtpssl = CheckSSL.Checked
                foundRow.smtpfromname = TxtFromName.Text.Trim
                foundRow.smtpfrommail = TxtFromMail.Text.Trim
                foundRow.smtpsubject = TxtSubject.Text.Trim
                foundRow.tag1 = TxtTag_1.Text.Trim
                foundRow.tag2 = TxtTag_2.Text.Trim
                foundRow.smtpstatus = "Working"
                '=================================================================
                ' Refresh Smtp gird
                frmEmailSender.GridControl1.RefreshDataSource()
                DevExpress.XtraEditors.XtraMessageBox.Show("Account info Successfully updated...", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
                Me.Dispose()
            Catch ex As Exception
                DevExpress.XtraEditors.XtraMessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
        System.Threading.Tasks.Task.Run(Sub()
                                            My.Settings.SMTP_DATA_TABLE_XML = FuncSendMail.SerializeListToXml(LS_SenderSmtp)
                                            My.Settings.Save()
                                        End Sub)
    End Sub

    Private Sub TxtTag_2_SelectedIndexChanged(sender As Object, e As EventArgs) Handles TxtTag_2.SelectedIndexChanged
        DxErrorProvider1.ClearErrors()
        If TxtTag_2.SelectedIndex >= 0 Then
            If TxtTag_2.SelectedItem.ToString() = "Receiver Email" Then
                TxtSubject.Text = "[-Email-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Encoded Email 64" Then
                TxtSubject.Text = "[-Email64-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Start Name With Upper Case" Then
                TxtSubject.Text = "[-UCase-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Add Link" Then
                TxtSubject.Text = "[-Link-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Send With Logo" Then
                TxtSubject.Text = "[-Logo-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Character 3" Then
                TxtSubject.Text = "[-RCh3-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Character 4" Then
                TxtSubject.Text = "[-RCh4-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Character 5" Then
                TxtSubject.Text = "[-RCh5-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Character 6" Then
                TxtSubject.Text = "[-RCh6-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Character 7" Then
                TxtSubject.Text = "[-RCh7-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Number 3" Then
                TxtSubject.Text = "[-RN3-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Number 4" Then
                TxtSubject.Text = "[-RN4-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Number 5" Then
                TxtSubject.Text = "[-RN5-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Number 6" Then
                TxtSubject.Text = "[-RN6-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Number 7" Then
                TxtSubject.Text = "[-RN7-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random IP" Then
                TxtSubject.Text = "[-IP-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random IP China" Then
                TxtSubject.Text = "[-IPChina-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Show The Domain" Then
                TxtSubject.Text = "[-Domain-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Show Name" Then
                TxtSubject.Text = "[-Name-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Show Company Name" Then
                TxtSubject.Text = "[-CompanyName-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Show Date" Then
                TxtSubject.Text = "[-Date-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Show Time" Then
                TxtSubject.Text = "[-Time-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Show Date Tomorrow" Then
                TxtSubject.Text = "[-DateTomorrow-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Country" Then
                TxtSubject.Text = "[-RCountry-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Browser" Then
                TxtSubject.Text = "[-RandomBrowser-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Show Random Fake phone Numbers" Then
                TxtSubject.Text = "[-FakePhone-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Show Random Fake Email Address Office" Then
                TxtSubject.Text = "[-FakeEmail-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Show Random Street New York" Then
                TxtSubject.Text = "[-NewYork-]"
            End If
        End If
    End Sub

    Private Sub TxtTag_1_SelectedIndexChanged(sender As Object, e As EventArgs) Handles TxtTag_1.SelectedIndexChanged
        DxErrorProvider1.ClearErrors()
        If TxtTag_1.SelectedIndex >= 0 Then
            If TxtTag_1.SelectedItem.ToString() = "Receiver Email" Then
                TxtFromName.Text = "[-Email-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Encoded Email 64" Then
                TxtFromName.Text = "[-Email64-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Start Name With Upper Case" Then
                TxtFromName.Text = "[-UCase-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Add Link" Then
                TxtFromName.Text = "[-Link-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Send With Logo" Then
                TxtFromName.Text = "[-Logo-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Character 3" Then
                TxtFromName.Text = "[-RCh3-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Character 4" Then
                TxtFromName.Text = "[-RCh4-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Character 5" Then
                TxtFromName.Text = "[-RCh5-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Character 6" Then
                TxtFromName.Text = "[-RCh6-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Character 7" Then
                TxtFromName.Text = "[-RCh7-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Number 3" Then
                TxtFromName.Text = "[-RN3-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Number 4" Then
                TxtFromName.Text = "[-RN4-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Number 5" Then
                TxtFromName.Text = "[-RN5-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Number 6" Then
                TxtFromName.Text = "[-RN6-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Number 7" Then
                TxtFromName.Text = "[-RN7-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random IP" Then
                TxtFromName.Text = "[-IP-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random IP China" Then
                TxtFromName.Text = "[-IPChina-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Show The Domain" Then
                TxtFromName.Text = "[-Domain-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Show Name" Then
                TxtFromName.Text = "[-Name-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Show Company Name" Then
                TxtFromName.Text = "[-CompanyName-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Show Date" Then
                TxtFromName.Text = "[-Date-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Show Time" Then
                TxtFromName.Text = "[-Time-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Show Date Tomorrow" Then
                TxtFromName.Text = "[-DateTomorrow-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Country" Then
                TxtFromName.Text = "[-RCountry-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Browser" Then
                TxtFromName.Text = "[-RandomBrowser-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Show Random Fake phone Numbers" Then
                TxtFromName.Text = "[-FakePhone-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Show Random Fake Email Address Office" Then
                TxtFromName.Text = "[-FakeEmail-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Show Random Street New York" Then
                TxtFromName.Text = "[-NewYork-]"
            End If
        End If
    End Sub

    Private Sub CheckSSL_CheckedChanged(sender As Object, e As EventArgs) Handles CheckSSL.CheckedChanged
        If CheckSSL.Checked = True Then
            CheckSSL.ForeColor = Color.Crimson()
        Else
            CheckSSL.ForeColor = Color.Gray()
        End If
    End Sub

    Private Sub SimpleButton1_Click(sender As Object, e As EventArgs) Handles SimpleButton1.Click
        If Not BackgroundWorker1.IsBusy Then
            BackgroundWorker1.RunWorkerAsync()
        Else
            BackgroundWorker1.CancelAsync()
        End If
    End Sub
End Class