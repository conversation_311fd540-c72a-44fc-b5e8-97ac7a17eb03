﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Threading.Tasks</name>
  </assembly>
  <members>
    <member name="T:System.AggregateException">
      <summary>응용 프로그램을 실행하는 동안 발생하는 하나 이상의 오류를 나타냅니다.</summary>
    </member>
    <member name="M:System.AggregateException.#ctor">
      <summary>오류를 설명하는 시스템 제공 메시지를 사용하여 <see cref="T:System.AggregateException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.AggregateException.#ctor(System.Collections.Generic.IEnumerable{System.Exception})">
      <summary>이 예외의 원인인 내부 예외에 대한 참조를 사용하여 <see cref="T:System.AggregateException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="innerExceptions">현재 예외의 원인인 예외입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="innerExceptions" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="innerExceptions" />의 요소가 null인 경우.</exception>
    </member>
    <member name="M:System.AggregateException.#ctor(System.Exception[])">
      <summary>이 예외의 원인인 내부 예외에 대한 참조를 사용하여 <see cref="T:System.AggregateException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="innerExceptions">현재 예외의 원인인 예외입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="innerExceptions" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="innerExceptions" />의 요소가 null인 경우.</exception>
    </member>
    <member name="M:System.AggregateException.#ctor(System.String)">
      <summary>오류를 설명하는 지정된 메시지를 사용하여 <see cref="T:System.AggregateException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">예외를 설명하는 메시지입니다.이 생성자의 호출자는 이 문자열이 현재 시스템 문화권에 맞게 지역화되었는지 확인하는 데 필요합니다.</param>
    </member>
    <member name="M:System.AggregateException.#ctor(System.String,System.Collections.Generic.IEnumerable{System.Exception})">
      <summary>지정된 오류 메시지와 이 예외의 원인인 내부 예외에 대한 참조를 사용하여 <see cref="T:System.AggregateException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">예외에 대한 이유를 설명하는 오류 메시지입니다.</param>
      <param name="innerExceptions">현재 예외의 원인인 예외입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="innerExceptions" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="innerExceptions" />의 요소가 null인 경우.</exception>
    </member>
    <member name="M:System.AggregateException.#ctor(System.String,System.Exception)">
      <summary>지정된 오류 메시지와 해당 예외의 원인인 내부 예외에 대한 참조를 사용하여 <see cref="T:System.AggregateException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">예외를 설명하는 메시지입니다.이 생성자의 호출자는 이 문자열이 현재 시스템 문화권에 맞게 지역화되었는지 확인하는 데 필요합니다.</param>
      <param name="innerException">현재 예외의 원인이 되는 예외입니다.<paramref name="innerException" /> 매개 변수가 null이 아니면 현재 예외는 내부 예외를 처리하는 catch 블록에서 발생합니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="innerException" /> 인수가 null인 경우</exception>
    </member>
    <member name="M:System.AggregateException.#ctor(System.String,System.Exception[])">
      <summary>지정된 오류 메시지와 이 예외의 원인인 내부 예외에 대한 참조를 사용하여 <see cref="T:System.AggregateException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">예외에 대한 이유를 설명하는 오류 메시지입니다.</param>
      <param name="innerExceptions">현재 예외의 원인인 예외입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="innerExceptions" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="innerExceptions" />의 요소가 null인 경우.</exception>
    </member>
    <member name="M:System.AggregateException.Flatten">
      <summary>
        <see cref="T:System.AggregateException" /> 인스턴스를 단일 새 인스턴스로 평면화합니다.</summary>
      <returns>평면화된 새 <see cref="T:System.AggregateException" />입니다.</returns>
    </member>
    <member name="M:System.AggregateException.GetBaseException">
      <summary>이 예외의 근본 원인인 <see cref="T:System.AggregateException" />을 반환합니다.</summary>
      <returns>이 예외의 근본 원인인 <see cref="T:System.AggregateException" />을 반환합니다.</returns>
    </member>
    <member name="M:System.AggregateException.Handle(System.Func{System.Exception,System.Boolean})">
      <summary>이 <see cref="T:System.AggregateException" />에 포함된 각 <see cref="T:System.Exception" />의 처리기를 호출합니다.</summary>
      <param name="predicate">각 예외에 대해 실행할 조건자입니다.조건자는 처리할 <see cref="T:System.Exception" />을 인수로 받아들이고, 예외가 처리되었는지 여부를 나타내는 부울을 반환합니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="predicate" /> 인수가 null인 경우</exception>
      <exception cref="T:System.AggregateException">이 <see cref="T:System.AggregateException" />에 의해 포함된 예외가 처리되지 않았습니다.</exception>
    </member>
    <member name="P:System.AggregateException.InnerExceptions">
      <summary>현재 예외를 발생시킨 <see cref="T:System.Exception" /> 인스턴스의 읽기 전용 컬렉션을 가져옵니다.</summary>
      <returns>현재 예외를 발생시킨 <see cref="T:System.Exception" /> 인스턴스의 읽기 전용 컬렉션을 반환합니다.</returns>
    </member>
    <member name="M:System.AggregateException.ToString">
      <summary>현재 <see cref="T:System.AggregateException" />의 문자열 표현을 만들어 반환합니다.</summary>
      <returns>현재 예외에 대한 문자열 표현입니다.</returns>
    </member>
    <member name="T:System.OperationCanceledException">
      <summary>스레드에서 실행 중인 작업을 취소할 때 해당 스레드에서 throw되는 예외입니다.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.OperationCanceledException.#ctor">
      <summary>시스템 제공 오류 메시지를 사용하여 <see cref="T:System.OperationCanceledException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.OperationCanceledException.#ctor(System.String)">
      <summary>지정된 오류 메시지를 사용하여 <see cref="T:System.OperationCanceledException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">오류를 설명하는 <see cref="T:System.String" />입니다.</param>
    </member>
    <member name="M:System.OperationCanceledException.#ctor(System.String,System.Exception)">
      <summary>지정된 오류 메시지와 해당 예외의 원인인 내부 예외에 대한 참조를 사용하여 <see cref="T:System.OperationCanceledException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">예외에 대한 이유를 설명하는 오류 메시지입니다. </param>
      <param name="innerException">현재 예외의 원인이 되는 예외입니다.<paramref name="innerException" /> 매개 변수가 null이 아니면 현재 예외는 내부 예외를 처리하는 catch 블록에서 발생합니다.</param>
    </member>
    <member name="M:System.OperationCanceledException.#ctor(System.String,System.Exception,System.Threading.CancellationToken)">
      <summary>지정된 오류 메시지, 해당 예외의 원인인 내부 예외에 대한 참조 및 취소 토큰을 사용하여 <see cref="T:System.OperationCanceledException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">예외에 대한 이유를 설명하는 오류 메시지입니다. </param>
      <param name="innerException">현재 예외의 원인이 되는 예외입니다.<paramref name="innerException" /> 매개 변수가 null이 아니면 현재 예외는 내부 예외를 처리하는 catch 블록에서 발생합니다.</param>
      <param name="token">취소한 작업과 연결된 취소 토큰입니다.</param>
    </member>
    <member name="M:System.OperationCanceledException.#ctor(System.String,System.Threading.CancellationToken)">
      <summary>지정된 오류 메시지 및 취소 토큰을 사용하여 <see cref="T:System.OperationCanceledException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">예외에 대한 이유를 설명하는 오류 메시지입니다.</param>
      <param name="token">취소한 작업과 연결된 취소 토큰입니다.</param>
    </member>
    <member name="M:System.OperationCanceledException.#ctor(System.Threading.CancellationToken)">
      <summary>취소 토큰을 사용하여 <see cref="T:System.OperationCanceledException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="token">취소한 작업과 연결된 취소 토큰입니다.</param>
    </member>
    <member name="P:System.OperationCanceledException.CancellationToken">
      <summary>취소한 작업과 연결된 취소 토큰을 가져옵니다.</summary>
      <returns>취소한 작업과 연결된 토큰 또는 기본 토큰입니다.</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.AsyncTaskMethodBuilder">
      <summary>작업을 반환하는 비동기 메서드에 대한 작성기를 나타냅니다.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.AwaitOnCompleted``2(``0@,``1@)">
      <summary>지정된 awaiter가 완료되면 다음 작업을 계속하도록 상태 시스템을 예약합니다.</summary>
      <param name="awaiter">Awaiter입니다.</param>
      <param name="stateMachine">상태 시스템입니다.</param>
      <typeparam name="TAwaiter">awaiter의 형식입니다.</typeparam>
      <typeparam name="TStateMachine">상태 컴퓨터의 형식입니다.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.AwaitUnsafeOnCompleted``2(``0@,``1@)">
      <summary>지정된 awaiter가 완료되면 다음 작업을 계속하도록 상태 시스템을 예약합니다.부분적으로 신뢰할 수 있는 코드에서 이 메서드를 호출할 수 있습니다.</summary>
      <param name="awaiter">Awaiter입니다.</param>
      <param name="stateMachine">상태 시스템입니다.</param>
      <typeparam name="TAwaiter">awaiter의 형식입니다.</typeparam>
      <typeparam name="TStateMachine">상태 컴퓨터의 형식입니다.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.Create">
      <summary>
        <see cref="T:System.Runtime.CompilerServices.AsyncTaskMethodBuilder" /> 클래스의 인스턴스를 만듭니다.</summary>
      <returns>작성기의 새 인스턴스입니다.</returns>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetException(System.Exception)">
      <summary>작업이 실패한 것으로 표시하고 지정된 예외를 작업에 바인딩합니다.</summary>
      <param name="exception">작업에 바인딩할 예외입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="exception" />가 null입니다.</exception>
      <exception cref="T:System.InvalidOperationException">작업이 이미 완료된 경우또는빌더가 초기화되지 않은 경우</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetResult">
      <summary>작업이 성공적으로 완료된 것으로 표시합니다.</summary>
      <exception cref="T:System.InvalidOperationException">작업이 이미 완료된 경우또는빌더가 초기화되지 않은 경우</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetStateMachine(System.Runtime.CompilerServices.IAsyncStateMachine)">
      <summary>지정된 상태 컴퓨터와 작성기를 연결합니다.</summary>
      <param name="stateMachine">작성기와 연결할 상태 시스템 인스턴스입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" />가 null입니다.</exception>
      <exception cref="T:System.InvalidOperationException">상태 시스템이 이전에 설정된 경우</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.Start``1(``0@)">
      <summary>연결된 상태 컴퓨터를 사용하여 작성기 실행을 시작합니다.</summary>
      <param name="stateMachine">참조로 전달된 상태 시스템 인스턴스입니다.</param>
      <typeparam name="TStateMachine">상태 컴퓨터의 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" />가 null입니다.</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.Task">
      <summary>이 작성기에 대한 작업을 가져옵니다.</summary>
      <returns>이 작성기에 대한 작업입니다.</returns>
      <exception cref="T:System.InvalidOperationException">빌더가 초기화되지 않은 경우</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1">
      <summary>작업을 반환하고 결과에 대한 매개 변수를 제공하는 비동기 메서드에 대한 작성기를 나타냅니다.</summary>
      <typeparam name="TResult">작업을 완료하는 데 사용할 결과입니다.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AwaitOnCompleted``2(``0@,``1@)">
      <summary>지정된 awaiter가 완료되면 다음 작업을 계속하도록 상태 시스템을 예약합니다.</summary>
      <param name="awaiter">Awaiter입니다.</param>
      <param name="stateMachine">상태 시스템입니다.</param>
      <typeparam name="TAwaiter">awaiter의 형식입니다.</typeparam>
      <typeparam name="TStateMachine">상태 컴퓨터의 형식입니다.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AwaitUnsafeOnCompleted``2(``0@,``1@)">
      <summary>지정된 awaiter가 완료되면 다음 작업을 계속하도록 상태 시스템을 예약합니다.부분적으로 신뢰할 수 있는 코드에서 이 메서드를 호출할 수 있습니다.</summary>
      <param name="awaiter">Awaiter입니다.</param>
      <param name="stateMachine">상태 시스템입니다.</param>
      <typeparam name="TAwaiter">awaiter의 형식입니다.</typeparam>
      <typeparam name="TStateMachine">상태 컴퓨터의 형식입니다.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.Create">
      <summary>
        <see cref="T:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1" /> 클래스의 인스턴스를 만듭니다.</summary>
      <returns>작성기의 새 인스턴스입니다.</returns>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetException(System.Exception)">
      <summary>작업이 실패한 것으로 표시하고 지정된 예외를 작업에 바인딩합니다.</summary>
      <param name="exception">작업에 바인딩할 예외입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="exception" />가 null입니다.</exception>
      <exception cref="T:System.InvalidOperationException">작업이 이미 완료된 경우</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetResult(`0)">
      <summary>작업이 성공적으로 완료된 것으로 표시합니다.</summary>
      <param name="result">작업을 완료하는 데 사용할 결과입니다.</param>
      <exception cref="T:System.InvalidOperationException">작업이 이미 완료된 경우</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetStateMachine(System.Runtime.CompilerServices.IAsyncStateMachine)">
      <summary>지정된 상태 컴퓨터와 작성기를 연결합니다.</summary>
      <param name="stateMachine">작성기와 연결할 상태 시스템 인스턴스입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" />가 null입니다.</exception>
      <exception cref="T:System.InvalidOperationException">상태 시스템이 이전에 설정된 경우</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.Start``1(``0@)">
      <summary>연결된 상태 컴퓨터를 사용하여 작성기 실행을 시작합니다.</summary>
      <param name="stateMachine">참조로 전달된 상태 시스템 인스턴스입니다.</param>
      <typeparam name="TStateMachine">상태 컴퓨터의 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" />가 null입니다.</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.Task">
      <summary>이 작성기에 대한 작업을 가져옵니다.</summary>
      <returns>이 작성기에 대한 작업입니다.</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.AsyncVoidMethodBuilder">
      <summary>값을 반환하지 않는 비동기 메서드에 대한 작성기를 나타냅니다.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitOnCompleted``2(``0@,``1@)">
      <summary>지정된 awaiter가 완료되면 다음 작업을 계속하도록 상태 시스템을 예약합니다.</summary>
      <param name="awaiter">Awaiter입니다.</param>
      <param name="stateMachine">상태 시스템입니다.</param>
      <typeparam name="TAwaiter">awaiter의 형식입니다.</typeparam>
      <typeparam name="TStateMachine">상태 컴퓨터의 형식입니다.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted``2(``0@,``1@)">
      <summary>지정된 awaiter가 완료되면 다음 작업을 계속하도록 상태 시스템을 예약합니다.부분적으로 신뢰할 수 있는 코드에서 이 메서드를 호출할 수 있습니다.</summary>
      <param name="awaiter">Awaiter입니다.</param>
      <param name="stateMachine">상태 시스템입니다.</param>
      <typeparam name="TAwaiter">awaiter의 형식입니다.</typeparam>
      <typeparam name="TStateMachine">상태 컴퓨터의 형식입니다.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Create">
      <summary>
        <see cref="T:System.Runtime.CompilerServices.AsyncVoidMethodBuilder" /> 클래스의 인스턴스를 만듭니다.</summary>
      <returns>작성기의 새 인스턴스입니다.</returns>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.SetException(System.Exception)">
      <summary>예외를 메서드 작성기에 바인딩합니다.</summary>
      <param name="exception">바인딩할 예외입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="exception" />가 null입니다.</exception>
      <exception cref="T:System.InvalidOperationException">빌더가 초기화되지 않은 경우</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.SetResult">
      <summary>메서드 빌더가 성공적으로 완료된 것으로 표시합니다.</summary>
      <exception cref="T:System.InvalidOperationException">빌더가 초기화되지 않은 경우</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.SetStateMachine(System.Runtime.CompilerServices.IAsyncStateMachine)">
      <summary>지정된 상태 컴퓨터와 작성기를 연결합니다.</summary>
      <param name="stateMachine">작성기와 연결할 상태 시스템 인스턴스입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" />가 null입니다.</exception>
      <exception cref="T:System.InvalidOperationException">상태 시스템이 이전에 설정된 경우</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start``1(``0@)">
      <summary>연결된 상태 컴퓨터를 사용하여 작성기 실행을 시작합니다.</summary>
      <param name="stateMachine">참조로 전달된 상태 시스템 인스턴스입니다.</param>
      <typeparam name="TStateMachine">상태 컴퓨터의 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" />가 null입니다.</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable">
      <summary>구성 항목이 작업에서 기다릴 수 있도록 awaitable 개체를 제공합니다.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.GetAwaiter">
      <summary>이 대기 가능 개체에 대해 awaiter를 반환합니다.</summary>
      <returns>Awaiter입니다.</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1">
      <summary>구성 항목이 작업에서 기다릴 수 있도록 awaitable 개체를 제공합니다.</summary>
      <typeparam name="TResult">이 <see cref="T:System.Threading.Tasks.Task`1" />에서 생성되는 결과의 형식입니다.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.GetAwaiter">
      <summary>이 대기 가능 개체에 대해 awaiter를 반환합니다.</summary>
      <returns>Awaiter입니다.</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter">
      <summary>대기 가능 개체(<see cref="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1" />)에 대해 awaiter를 제공합니다.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter.GetResult">
      <summary>완료된 작업에 대한 대기를 종료합니다.</summary>
      <returns>완료된 작업의 결과입니다.</returns>
      <exception cref="T:System.NullReferenceException">awaiter가 제대로 초기화되지 않은 경우</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">작업이 취소되었습니다.</exception>
      <exception cref="T:System.Exception">작업이 Faulted 상태로 완료되었습니다.</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter.IsCompleted">
      <summary>대기 중인 작업이 완료되었는지 여부를 지정하는 값을 가져옵니다.</summary>
      <returns>대기 중인 작업이 완료되었으면 true이고, 그렇지 않으면 false입니다.</returns>
      <exception cref="T:System.NullReferenceException">awaiter가 제대로 초기화되지 않은 경우</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter.OnCompleted(System.Action)">
      <summary>이 awaiter와 연결된 작업에 대한 연속 작업을 예약합니다.</summary>
      <param name="continuation">대기 작업이 완료될 때 호출할 작업입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> 인수가 null인 경우</exception>
      <exception cref="T:System.NullReferenceException">awaiter가 제대로 초기화되지 않은 경우</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter.UnsafeOnCompleted(System.Action)">
      <summary>이 awaiter와 연결된 작업에 대한 연속 작업을 예약합니다. </summary>
      <param name="continuation">대기 작업이 완료될 때 호출할 작업입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> 인수가 null인 경우</exception>
      <exception cref="T:System.NullReferenceException">awaiter가 제대로 초기화되지 않은 경우</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter">
      <summary>대기 가능(<see cref="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable" />) 개체에 대해 awaiter를 제공합니다.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter.GetResult">
      <summary>완료된 작업에 대한 대기를 종료합니다.</summary>
      <exception cref="T:System.NullReferenceException">awaiter가 제대로 초기화되지 않은 경우</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">작업이 취소되었습니다.</exception>
      <exception cref="T:System.Exception">작업이 Faulted 상태로 완료되었습니다.</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter.IsCompleted">
      <summary>대기 중인 작업이 완료되었는지 여부를 지정하는 값을 가져옵니다.</summary>
      <returns>대기 중인 작업이 완료되면 true이고, 그렇지 않으면 false입니다.</returns>
      <exception cref="T:System.NullReferenceException">awaiter가 제대로 초기화되지 않은 경우</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter.OnCompleted(System.Action)">
      <summary>이 awaiter와 연결된 작업에 대한 연속 작업을 예약합니다.</summary>
      <param name="continuation">대기 작업이 완료될 때 호출할 작업입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> 인수가 null인 경우</exception>
      <exception cref="T:System.NullReferenceException">awaiter가 제대로 초기화되지 않은 경우</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter.UnsafeOnCompleted(System.Action)">
      <summary>이 awaiter와 연결된 작업에 대한 연속 작업을 예약합니다. </summary>
      <param name="continuation">대기 작업이 완료될 때 호출할 작업입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> 인수가 null인 경우</exception>
      <exception cref="T:System.NullReferenceException">awaiter가 제대로 초기화되지 않은 경우</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.IAsyncStateMachine">
      <summary>비동기 메서드에 대해 생성된 상태 시스템을 나타냅니다.이 형식은 컴파일러 전용입니다.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.IAsyncStateMachine.MoveNext">
      <summary>상태 시스템을 다음 상태로 이동합니다.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.IAsyncStateMachine.SetStateMachine(System.Runtime.CompilerServices.IAsyncStateMachine)">
      <summary>힙에 할당된 복제본을 사용하여 상태 시스템을 구성합니다.</summary>
      <param name="stateMachine">힙에 할당된 복제본입니다.</param>
    </member>
    <member name="T:System.Runtime.CompilerServices.ICriticalNotifyCompletion">
      <summary>Await 작업이 완료될 때 연속을 예약하는 awaiter를 나타냅니다.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.ICriticalNotifyCompletion.UnsafeOnCompleted(System.Action)">
      <summary>인스턴스가 완료될 때 호출되는 연속 작업을 예약합니다.</summary>
      <param name="continuation">작업이 완료될 때 호출할 작업입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> 인수가 null인 경우(Visual Basic에서는 Nothing)</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.INotifyCompletion">
      <summary>작업이 완료될 때 연속을 예약하는 작업을 나타냅니다.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.INotifyCompletion.OnCompleted(System.Action)">
      <summary>인스턴스가 완료될 때 호출되는 연속 작업을 예약합니다.</summary>
      <param name="continuation">작업이 완료될 때 호출할 작업입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> 인수가 null인 경우(Visual Basic에서는 Nothing)</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.TaskAwaiter">
      <summary>비동기 작업의 완료를 기다리는 개체를 제공합니다. </summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter.GetResult">
      <summary>비동기 작업의 완료에 대한 대기를 종료합니다.</summary>
      <exception cref="T:System.NullReferenceException">
        <see cref="T:System.Runtime.CompilerServices.TaskAwaiter" /> 개체가 제대로 초기화되지 않은 경우</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">작업이 취소되었습니다.</exception>
      <exception cref="T:System.Exception">작업이 <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> 상태로 완료되었습니다.</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.TaskAwaiter.IsCompleted">
      <summary>비동기 작업이 완료되었는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>작업이 완료되었으면 true이고, 그렇지 않으면 false입니다.</returns>
      <exception cref="T:System.NullReferenceException">
        <see cref="T:System.Runtime.CompilerServices.TaskAwaiter" /> 개체가 제대로 초기화되지 않은 경우</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter.OnCompleted(System.Action)">
      <summary>
        <see cref="T:System.Runtime.CompilerServices.TaskAwaiter" /> 개체가 비동기 작업을 완료하기 위해 대기를 중지할 경우 수행할 동작을 설정합니다.</summary>
      <param name="continuation">대기 작업이 완료될 때 수행할 작업입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" />가 null인 경우</exception>
      <exception cref="T:System.NullReferenceException">
        <see cref="T:System.Runtime.CompilerServices.TaskAwaiter" /> 개체가 제대로 초기화되지 않은 경우</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter.UnsafeOnCompleted(System.Action)">
      <summary>이 awaiter와 연결된 비동기 작업에 대한 연속 작업을 예약합니다.</summary>
      <param name="continuation">대기 작업이 완료될 때 호출할 작업입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" />가 null인 경우</exception>
      <exception cref="T:System.InvalidOperationException">awaiter가 제대로 초기화되지 않은 경우</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.TaskAwaiter`1">
      <summary>비동기 작업이 완료되고 결과에 대한 매개 변수를 제공할 때까지 기다리는 개체를 나타냅니다.</summary>
      <typeparam name="TResult">작업의 결과입니다.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter`1.GetResult">
      <summary>비동기 작업의 완료에 대한 대기를 종료합니다.</summary>
      <returns>완료된 작업의 결과입니다.</returns>
      <exception cref="T:System.NullReferenceException">
        <see cref="T:System.Runtime.CompilerServices.TaskAwaiter`1" /> 개체가 제대로 초기화되지 않은 경우</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">작업이 취소되었습니다.</exception>
      <exception cref="T:System.Exception">작업이 <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> 상태로 완료되었습니다.</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.TaskAwaiter`1.IsCompleted">
      <summary>비동기 작업이 완료되었는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>작업이 완료되었으면 true이고, 그렇지 않으면 false입니다.</returns>
      <exception cref="T:System.NullReferenceException">
        <see cref="T:System.Runtime.CompilerServices.TaskAwaiter`1" /> 개체가 제대로 초기화되지 않은 경우</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter`1.OnCompleted(System.Action)">
      <summary>
        <see cref="T:System.Runtime.CompilerServices.TaskAwaiter`1" /> 개체가 비동기 작업을 완료하기 위해 대기를 중지할 경우 수행할 동작을 설정합니다.</summary>
      <param name="continuation">대기 작업이 완료될 때 수행할 작업입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" />가 null인 경우</exception>
      <exception cref="T:System.NullReferenceException">
        <see cref="T:System.Runtime.CompilerServices.TaskAwaiter`1" /> 개체가 제대로 초기화되지 않은 경우</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter`1.UnsafeOnCompleted(System.Action)">
      <summary>이 awaiter와 연결된 비동기 작업에 대한 연속 작업을 예약합니다.</summary>
      <param name="continuation">대기 작업이 완료될 때 호출할 작업입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" />가 null인 경우</exception>
      <exception cref="T:System.InvalidOperationException">awaiter가 제대로 초기화되지 않은 경우</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.YieldAwaitable">
      <summary>비동기적으로 대상 환경으로 전환할 때 대기에 대한 컨텍스트를 제공합니다.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.YieldAwaitable.GetAwaiter">
      <summary>클래스의 이 인스턴스에 대한 <see cref="T:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter" /> 개체를 검색합니다.</summary>
      <returns>비동기 작업의 완료를 모니터링하는 데 사용되는 개체입니다.</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter">
      <summary>대상 환경으로 전환하는 awaiter를 제공합니다.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter.GetResult">
      <summary>대기 작업을 종료합니다.</summary>
    </member>
    <member name="P:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter.IsCompleted">
      <summary>양보가 필요하지 않은지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>항상 false이며, 이는 <see cref="T:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter" />에 항상 yield가 필요함을 나타냅니다.</returns>
    </member>
    <member name="M:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter.OnCompleted(System.Action)">
      <summary>호출할 연속을 설정합니다.</summary>
      <param name="continuation">비동기적으로 호출하는 작업입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" />가 null입니다.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter.UnsafeOnCompleted(System.Action)">
      <summary>
        <paramref name="continuation" />을 현재 컨텍스트로 다시 게시합니다.</summary>
      <param name="continuation">비동기적으로 호출하는 작업입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> 인수가 null인 경우</exception>
    </member>
    <member name="T:System.Threading.CancellationToken">
      <summary>작업을 취소해야 한다는 알림을 전파합니다.</summary>
    </member>
    <member name="M:System.Threading.CancellationToken.#ctor(System.Boolean)">
      <summary>
        <see cref="T:System.Threading.CancellationToken" />을(를) 초기화합니다.</summary>
      <param name="canceled">토큰의 취소된 상태입니다.</param>
    </member>
    <member name="P:System.Threading.CancellationToken.CanBeCanceled">
      <summary>이 토큰이 취소된 상태로 있을 수 있는지 여부를 가져옵니다.</summary>
      <returns>이 토큰이 취소된 상태로 있을 수 있으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="M:System.Threading.CancellationToken.Equals(System.Object)">
      <summary>현재 <see cref="T:System.Threading.CancellationToken" /> 인스턴스와 지정한 <see cref="T:System.Object" />가 같은지 여부를 확인합니다.</summary>
      <returns>
        <paramref name="other" />가 <see cref="T:System.Threading.CancellationToken" />이고 두 인스턴스가 같으면 true이고, 그렇지 않으면 false입니다.둘 다 동일한 <see cref="T:System.Threading.CancellationTokenSource" />에 연결되어 있거나 둘 다 public CancellationToken 생성자에서 생성되고 <see cref="P:System.Threading.CancellationToken.IsCancellationRequested" /> 값이 같으면 두 토큰은 같습니다.</returns>
      <param name="other">이 인스턴스와 비교할 다른 개체입니다.</param>
      <exception cref="T:System.ObjectDisposedException">An associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.Equals(System.Threading.CancellationToken)">
      <summary>현재 <see cref="T:System.Threading.CancellationToken" /> 인스턴스와 지정한 토큰이 같은지 여부를 확인합니다.</summary>
      <returns>두 인스턴스가 같으면 true이고, 그렇지 않으면 false입니다.둘 다 동일한 <see cref="T:System.Threading.CancellationTokenSource" />에 연결되어 있거나 둘 다 public CancellationToken 생성자에서 생성되고 <see cref="P:System.Threading.CancellationToken.IsCancellationRequested" /> 값이 같으면 두 토큰은 같습니다.</returns>
      <param name="other">이 인스턴스와 비교할 다른 <see cref="T:System.Threading.CancellationToken" />입니다.</param>
    </member>
    <member name="M:System.Threading.CancellationToken.GetHashCode">
      <summary>
        <see cref="T:System.Threading.CancellationToken" />의 해시 함수 역할을 수행합니다.</summary>
      <returns>현재 <see cref="T:System.Threading.CancellationToken" /> 인스턴스에 대한 해시 코드입니다.</returns>
    </member>
    <member name="P:System.Threading.CancellationToken.IsCancellationRequested">
      <summary>이 토큰의 취소가 요청되었는지 여부를 가져옵니다.</summary>
      <returns>이 토큰의 취소가 요청되었으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Threading.CancellationToken.None">
      <summary>빈 <see cref="T:System.Threading.CancellationToken" /> 값을 반환합니다.</summary>
      <returns>빈 취소 토큰입니다. </returns>
    </member>
    <member name="M:System.Threading.CancellationToken.op_Equality(System.Threading.CancellationToken,System.Threading.CancellationToken)">
      <summary>두 개의 <see cref="T:System.Threading.CancellationToken" /> 인스턴스가 같은지 여부를 확인합니다.</summary>
      <returns>두 인스턴스가 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="left">첫 번째 인스턴스입니다.</param>
      <param name="right">두 번째 인스턴스입니다.</param>
      <exception cref="T:System.ObjectDisposedException">An associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.op_Inequality(System.Threading.CancellationToken,System.Threading.CancellationToken)">
      <summary>두 <see cref="T:System.Threading.CancellationToken" /> 인스턴스가 서로 다른지 여부를 확인합니다.</summary>
      <returns>인스턴스가 서로 다르면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="left">첫 번째 인스턴스입니다.</param>
      <param name="right">두 번째 인스턴스입니다.</param>
      <exception cref="T:System.ObjectDisposedException">An associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.Register(System.Action)">
      <summary>이 <see cref="T:System.Threading.CancellationToken" />이 취소될 때 호출할 대리자를 등록합니다.</summary>
      <returns>콜백 등록을 취소하는 데 사용할 수 있는 <see cref="T:System.Threading.CancellationTokenRegistration" /> 인스턴스입니다.</returns>
      <param name="callback">
        <see cref="T:System.Threading.CancellationToken" />이 취소될 때 실행할 대리자입니다.</param>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="callback" /> is null.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.Register(System.Action,System.Boolean)">
      <summary>이 <see cref="T:System.Threading.CancellationToken" />이 취소될 때 호출할 대리자를 등록합니다.</summary>
      <returns>콜백 등록을 취소하는 데 사용할 수 있는 <see cref="T:System.Threading.CancellationTokenRegistration" /> 인스턴스입니다.</returns>
      <param name="callback">
        <see cref="T:System.Threading.CancellationToken" />이 취소될 때 실행할 대리자입니다.</param>
      <param name="useSynchronizationContext">현재 <see cref="T:System.Threading.SynchronizationContext" />를 캡처하여 <paramref name="callback" />을 호출할 때 사용할지 여부를 나타내는 부울 값입니다.</param>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="callback" /> is null.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.Register(System.Action{System.Object},System.Object)">
      <summary>이 <see cref="T:System.Threading.CancellationToken" />이 취소될 때 호출할 대리자를 등록합니다.</summary>
      <returns>콜백 등록을 취소하는 데 사용할 수 있는 <see cref="T:System.Threading.CancellationTokenRegistration" /> 인스턴스입니다.</returns>
      <param name="callback">
        <see cref="T:System.Threading.CancellationToken" />이 취소될 때 실행할 대리자입니다.</param>
      <param name="state">대리자가 호출될 때 <paramref name="callback" />에 전달할 상태입니다.null일 수 있습니다.</param>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="callback" /> is null.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.Register(System.Action{System.Object},System.Object,System.Boolean)">
      <summary>이 <see cref="T:System.Threading.CancellationToken" />이 취소될 때 호출할 대리자를 등록합니다.</summary>
      <returns>콜백 등록을 취소하는 데 사용할 수 있는 <see cref="T:System.Threading.CancellationTokenRegistration" /> 인스턴스입니다.</returns>
      <param name="callback">
        <see cref="T:System.Threading.CancellationToken" />이 취소될 때 실행할 대리자입니다.</param>
      <param name="state">대리자가 호출될 때 <paramref name="callback" />에 전달할 상태입니다.null일 수 있습니다.</param>
      <param name="useSynchronizationContext">현재 <see cref="T:System.Threading.SynchronizationContext" />를 캡처하여 <paramref name="callback" />을 호출할 때 사용할지 여부를 나타내는 부울 값입니다.</param>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="callback" /> is null.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.ThrowIfCancellationRequested">
      <summary>이 토큰의 취소가 요청된 경우 <see cref="T:System.OperationCanceledException" />이 발생합니다.</summary>
      <exception cref="T:System.OperationCanceledException">The token has had cancellation requested.</exception>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
    </member>
    <member name="P:System.Threading.CancellationToken.WaitHandle">
      <summary>토큰이 취소될 때 신호를 받는 <see cref="T:System.Threading.WaitHandle" />을 가져옵니다.</summary>
      <returns>토큰이 취소될 때 신호를 받는 <see cref="T:System.Threading.WaitHandle" />입니다.</returns>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
    </member>
    <member name="T:System.Threading.CancellationTokenRegistration">
      <summary>
        <see cref="T:System.Threading.CancellationToken" />에 등록된 콜백 대리자를 나타냅니다. </summary>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.Dispose">
      <summary>
        <see cref="T:System.Threading.CancellationTokenRegistration" /> 클래스의 현재 인스턴스에서 사용하는 모든 리소스를 해제합니다.</summary>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.Equals(System.Object)">
      <summary>현재 <see cref="T:System.Threading.CancellationTokenRegistration" /> 인스턴스와 지정한 <see cref="T:System.Threading.CancellationTokenRegistration" />이 같은지 여부를 확인합니다.</summary>
      <returns>이 인스턴스와 <paramref name="obj" />가 같으면 true이고,그렇지 않으면 false입니다.두 <see cref="T:System.Threading.CancellationTokenRegistration" /> 인스턴스 모두 <see cref="T:System.Threading.CancellationToken" />의 같은 Register 메서드에 대한 단일 호출의 출력을 참조할 경우 두 인스턴스는 같습니다.</returns>
      <param name="obj">이 인스턴스와 비교할 다른 개체입니다.</param>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.Equals(System.Threading.CancellationTokenRegistration)">
      <summary>현재 <see cref="T:System.Threading.CancellationTokenRegistration" /> 인스턴스와 지정한 <see cref="T:System.Threading.CancellationTokenRegistration" />이 같은지 여부를 확인합니다.</summary>
      <returns>이 인스턴스와 <paramref name="other" />가 같으면 true이고,그렇지 않으면 false입니다. 두 <see cref="T:System.Threading.CancellationTokenRegistration" /> 인스턴스 모두 <see cref="T:System.Threading.CancellationToken" />의 같은 Register 메서드에 대한 단일 호출의 출력을 참조할 경우 두 인스턴스는 같습니다.</returns>
      <param name="other">이 인스턴스와 비교할 다른 <see cref="T:System.Threading.CancellationTokenRegistration" />입니다.</param>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.GetHashCode">
      <summary>
        <see cref="T:System.Threading.CancellationTokenRegistration" />에 대한 해시 함수 역할을 합니다.</summary>
      <returns>현재 <see cref="T:System.Threading.CancellationTokenRegistration" /> 인스턴스에 대한 해시 코드입니다.</returns>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.op_Equality(System.Threading.CancellationTokenRegistration,System.Threading.CancellationTokenRegistration)">
      <summary>두 <see cref="T:System.Threading.CancellationTokenRegistration" /> 인스턴스가 같은지 여부를 확인합니다.</summary>
      <returns>두 인스턴스가 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="left">첫 번째 인스턴스입니다.</param>
      <param name="right">두 번째 인스턴스입니다.</param>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.op_Inequality(System.Threading.CancellationTokenRegistration,System.Threading.CancellationTokenRegistration)">
      <summary>두 <see cref="T:System.Threading.CancellationTokenRegistration" /> 인스턴스가 서로 다른지 여부를 확인합니다.</summary>
      <returns>인스턴스가 서로 다르면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="left">첫 번째 인스턴스입니다.</param>
      <param name="right">두 번째 인스턴스입니다.</param>
    </member>
    <member name="T:System.Threading.CancellationTokenSource">
      <summary>취소되도록 <see cref="T:System.Threading.CancellationToken" />에 신호를 보냅니다.</summary>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.#ctor">
      <summary>
        <see cref="T:System.Threading.CancellationTokenSource" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.#ctor(System.Int32)">
      <summary>지정한 지연(밀리초) 후에 취소되는 <see cref="T:System.Threading.CancellationTokenSource" />의 새 인스턴스를 초기화합니다.</summary>
      <param name="millisecondsDelay">이 <see cref="T:System.Threading.CancellationTokenSource" />이(가) 취소될 때까지 대기하는 시간(밀리초) 간격입니다. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsDelay" /> is less than -1. </exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.#ctor(System.TimeSpan)">
      <summary>지정한 시간 범위 후에 취소되는 <see cref="T:System.Threading.CancellationTokenSource" />의 새 인스턴스를 초기화합니다.</summary>
      <param name="delay">이 <see cref="T:System.Threading.CancellationTokenSource" />이(가) 취소될 때까지 대기하는 시간 간격입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="delay" />.<see cref="P:System.TimeSpan.TotalMilliseconds" /> is less than -1 or greater than <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.Cancel">
      <summary>취소 요청을 전달합니다.</summary>
      <exception cref="T:System.ObjectDisposedException">This <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.AggregateException">An aggregate exception containing all the exceptions thrown by the registered callbacks on the associated <see cref="T:System.Threading.CancellationToken" />.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.Cancel(System.Boolean)">
      <summary>취소 요청을 전달하고 나머지 콜백과 취소 가능한 작업을 처리해야 하는지를 지정합니다.</summary>
      <param name="throwOnFirstException">예외를 즉시 전파해야 하는 경우 true이고, 그렇지 않으면 false입니다.</param>
      <exception cref="T:System.ObjectDisposedException">This <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.AggregateException">An aggregate exception containing all the exceptions thrown by the registered callbacks on the associated <see cref="T:System.Threading.CancellationToken" />.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.CancelAfter(System.Int32)">
      <summary>지정한 시간(밀리초) 후에 이 <see cref="T:System.Threading.CancellationTokenSource" />를 취소하는 작업을 예약합니다.</summary>
      <param name="millisecondsDelay">이 <see cref="T:System.Threading.CancellationTokenSource" />를 취소하기 전에 대기할 시간 범위입니다.</param>
      <exception cref="T:System.ObjectDisposedException">The exception thrown when this <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The exception thrown when <paramref name="millisecondsDelay" /> is less than -1.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.CancelAfter(System.TimeSpan)">
      <summary>지정한 시간 범위 후에 이 <see cref="T:System.Threading.CancellationTokenSource" />를 취소하는 작업을 예약합니다.</summary>
      <param name="delay">이 <see cref="T:System.Threading.CancellationTokenSource" />를 취소하기 전에 대기할 시간 범위입니다.</param>
      <exception cref="T:System.ObjectDisposedException">The exception thrown when this <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The exception that is thrown when <paramref name="delay" /> is less than -1 or greater than Int32.MaxValue.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.CreateLinkedTokenSource(System.Threading.CancellationToken,System.Threading.CancellationToken)">
      <summary>취소된 상태인 원본 토큰이 있는 경우 취소된 상태가 되는 <see cref="T:System.Threading.CancellationTokenSource" />를 만듭니다.</summary>
      <returns>원본 토큰에 연결된 <see cref="T:System.Threading.CancellationTokenSource" />입니다.</returns>
      <param name="token1">관찰할 첫 번째 취소 토큰입니다.</param>
      <param name="token2">관찰할 두 번째 취소 토큰입니다.</param>
      <exception cref="T:System.ObjectDisposedException">A <see cref="T:System.Threading.CancellationTokenSource" /> associated with one of the source tokens has been disposed.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.CreateLinkedTokenSource(System.Threading.CancellationToken[])">
      <summary>지정한 배열에 취소된 상태인 원본 토큰이 있는 경우 취소된 상태가 되는 <see cref="T:System.Threading.CancellationTokenSource" />를 만듭니다.</summary>
      <returns>원본 토큰에 연결된 <see cref="T:System.Threading.CancellationTokenSource" />입니다.</returns>
      <param name="tokens">관찰할 취소 토큰 인스턴스를 포함하는 배열입니다.</param>
      <exception cref="T:System.ObjectDisposedException">A <see cref="T:System.Threading.CancellationTokenSource" /> associated with one of the source tokens has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tokens" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tokens" /> is empty.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.Dispose">
      <summary>
        <see cref="T:System.Threading.CancellationTokenSource" /> 클래스의 현재 인스턴스에서 사용하는 모든 리소스를 해제합니다.</summary>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.Dispose(System.Boolean)">
      <summary>
        <see cref="T:System.Threading.CancellationTokenSource" /> 클래스에 사용되는 관리되지 않는 리소스를 해제하고, 필요에 따라 관리되는 리소스를 해제합니다.</summary>
      <param name="disposing">관리되는 리소스와 관리되지 않는 리소스를 모두 해제하려면 true로 설정하고, 관리되지 않는 리소스만 해제하려면 false로 설정합니다.</param>
    </member>
    <member name="P:System.Threading.CancellationTokenSource.IsCancellationRequested">
      <summary>이 <see cref="T:System.Threading.CancellationTokenSource" />의 취소가 요청되었는지 여부를 가져옵니다.</summary>
      <returns>이 <see cref="T:System.Threading.CancellationTokenSource" />의 취소가 요청되었으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Threading.CancellationTokenSource.Token">
      <summary>이 <see cref="T:System.Threading.CancellationToken" />와 연결된 <see cref="T:System.Threading.CancellationTokenSource" />를 가져옵니다.</summary>
      <returns>이 <see cref="T:System.Threading.CancellationToken" />와 연결된 <see cref="T:System.Threading.CancellationTokenSource" />입니다.</returns>
      <exception cref="T:System.ObjectDisposedException">The token source has been disposed.</exception>
    </member>
    <member name="T:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair">
      <summary>동시 작업을 동시에 실행하고 전용 작업은 실행하지 않으면서 작업을 실행하도록 조정하는 작업 스케줄러를 제공합니다.</summary>
    </member>
    <member name="M:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.#ctor">
      <summary>
        <see cref="T:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.#ctor(System.Threading.Tasks.TaskScheduler)">
      <summary>지정된 스케줄러를 대상으로 하는 <see cref="T:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="taskScheduler">이 쌍을 실행해야 하는 대상 스케줄러입니다.</param>
    </member>
    <member name="M:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.#ctor(System.Threading.Tasks.TaskScheduler,System.Int32)">
      <summary>최대 동시성 수준을 사용하여 지정된 스케줄러를 대상으로 하는 <see cref="T:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="taskScheduler">이 쌍을 실행해야 하는 대상 스케줄러입니다.</param>
      <param name="maxConcurrencyLevel">동시에 실행할 최대 작업 수입니다.</param>
    </member>
    <member name="M:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.#ctor(System.Threading.Tasks.TaskScheduler,System.Int32,System.Int32)">
      <summary>최대 동시성 수준과 예약된 최대 작업 수(단위로 처리될 수도 있음)와 함께 지정한 스케줄러를 대상으로 하는 <see cref="T:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair" /> 클래스의 새 인스턴스를 초기화하고 보호 수준에 서명합니다.</summary>
      <param name="taskScheduler">이 쌍을 실행해야 하는 대상 스케줄러입니다.</param>
      <param name="maxConcurrencyLevel">동시에 실행할 최대 작업 수입니다.</param>
      <param name="maxItemsPerTask">쌍으로 사용되는 예약된 각 내부 작업을 처리하는 최대 작업 수입니다.</param>
    </member>
    <member name="M:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.Complete">
      <summary>스케줄러 쌍에게 추가 작업을 수락할 수 없음을 알립니다.</summary>
    </member>
    <member name="P:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.Completion">
      <summary>스케줄러가 처리를 완료하면 완료될 <see cref="T:System.Threading.Tasks.Task" />를 가져옵니다.</summary>
      <returns>스케줄러 처리를 마칠 때 완료되는 비동기 작업입니다.</returns>
    </member>
    <member name="P:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.ConcurrentScheduler">
      <summary>이 쌍에서 다른 작업과 동시에 실행될 수 있는 이 쌍에 대한 작업을 예약하는 데 사용할 수 있는 <see cref="T:System.Threading.Tasks.TaskScheduler" />를 가져옵니다.</summary>
      <returns>동시에 작업을 예약하는 데 사용할 수 있는 개체입니다.</returns>
    </member>
    <member name="P:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.ExclusiveScheduler">
      <summary>이 쌍에서 다른 작업과 관련하여 단독으로 실행되어야 하는 이 쌍에 대한 작업을 예약하는 데 사용할 수 있는 <see cref="T:System.Threading.Tasks.TaskScheduler" />를 가져옵니다.</summary>
      <returns>다른 작업과 동시에 실행되지 않는 작업을 예약하는 데 사용할 수 있는 개체입니다.</returns>
    </member>
    <member name="T:System.Threading.Tasks.Task">
      <summary>비동기 작업을 나타냅니다.이 형식에 대한 .NET Framework 소스 코드를 찾아보려면 참조 원본을 참조하세요.</summary>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action)">
      <summary>지정된 작업을 사용하여 새 <see cref="T:System.Threading.Tasks.Task" />를 초기화합니다.</summary>
      <param name="action">작업에서 실행할 코드를 나타내는 대리자입니다.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action,System.Threading.CancellationToken)">
      <summary>지정된 작업을 사용하여 새 <see cref="T:System.Threading.Tasks.Task" /> 및 <see cref="T:System.Threading.CancellationToken" />을 초기화합니다.</summary>
      <param name="action">작업에서 실행할 코드를 나타내는 대리자입니다.</param>
      <param name="cancellationToken">작업이 관찰할 <see cref="T:System.Threading.CancellationToken" />입니다.</param>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions)">
      <summary>지정된 작업 및 만들기 옵션을 사용하여 새 <see cref="T:System.Threading.Tasks.Task" />를 초기화합니다.</summary>
      <param name="action">작업에서 실행할 코드를 나타내는 대리자입니다.</param>
      <param name="cancellationToken">작업이 관찰할 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />입니다.</param>
      <param name="creationOptions">작업의 동작을 사용자 지정하는 데 사용할 <see cref="T:System.Threading.Tasks.TaskCreationOptions" />입니다.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action,System.Threading.Tasks.TaskCreationOptions)">
      <summary>지정된 작업 및 만들기 옵션을 사용하여 새 <see cref="T:System.Threading.Tasks.Task" />를 초기화합니다.</summary>
      <param name="action">작업에서 실행할 코드를 나타내는 대리자입니다.</param>
      <param name="creationOptions">작업의 동작을 사용자 지정하는 데 사용할 <see cref="T:System.Threading.Tasks.TaskCreationOptions" />입니다. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action{System.Object},System.Object)">
      <summary>지정된 작업 및 상태를 사용하여 새 <see cref="T:System.Threading.Tasks.Task" />를 초기화합니다.</summary>
      <param name="action">작업에서 실행할 코드를 나타내는 대리자입니다.</param>
      <param name="state">동작에서 사용할 데이터를 나타내는 개체입니다.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action{System.Object},System.Object,System.Threading.CancellationToken)">
      <summary>지정된 작업, 상태 및 옵션을 사용하여 새 <see cref="T:System.Threading.Tasks.Task" />를 초기화합니다.</summary>
      <param name="action">작업에서 실행할 코드를 나타내는 대리자입니다.</param>
      <param name="state">동작에서 사용할 데이터를 나타내는 개체입니다.</param>
      <param name="cancellationToken">새 작업이 관찰할 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />입니다.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action{System.Object},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions)">
      <summary>지정된 작업, 상태 및 옵션을 사용하여 새 <see cref="T:System.Threading.Tasks.Task" />를 초기화합니다.</summary>
      <param name="action">작업에서 실행할 코드를 나타내는 대리자입니다.</param>
      <param name="state">동작에서 사용할 데이터를 나타내는 개체입니다.</param>
      <param name="cancellationToken">새 작업이 관찰할 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />입니다.</param>
      <param name="creationOptions">작업의 동작을 사용자 지정하는 데 사용할 <see cref="T:System.Threading.Tasks.TaskCreationOptions" />입니다.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action{System.Object},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>지정된 작업, 상태 및 옵션을 사용하여 새 <see cref="T:System.Threading.Tasks.Task" />를 초기화합니다.</summary>
      <param name="action">작업에서 실행할 코드를 나타내는 대리자입니다.</param>
      <param name="state">동작에서 사용할 데이터를 나타내는 개체입니다.</param>
      <param name="creationOptions">작업의 동작을 사용자 지정하는 데 사용할 <see cref="T:System.Threading.Tasks.TaskCreationOptions" />입니다.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
    </member>
    <member name="P:System.Threading.Tasks.Task.AsyncState">
      <summary>
        <see cref="T:System.Threading.Tasks.Task" />를 만들 때 제공된 상태 개체를 가져오거나, 제공된 개체가 없는 경우 null을 가져옵니다.</summary>
      <returns>작업을 만들 때 작업에 전달된 상태 데이터를 나타내는 <see cref="T:System.Object" />입니다.</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.CompletedTask">
      <summary>이미 성공적으로 완료된 작업을 가져옵니다. </summary>
      <returns>작업을 완료했습니다. </returns>
    </member>
    <member name="M:System.Threading.Tasks.Task.ConfigureAwait(System.Boolean)">
      <summary>이 <see cref="T:System.Threading.Tasks.Task" />를 기다리는 데 사용되는 awaiter를 구성합니다.</summary>
      <returns>이 작업을 기다리는 데 사용되는 개체입니다.</returns>
      <param name="continueOnCapturedContext">캡처된 원래 컨텍스트로 연속 마샬링하려면 true이고, 그렇지 않으면 false입니다.</param>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task})">
      <summary>대상 <see cref="T:System.Threading.Tasks.Task" />가 완료될 때 비동기적으로 실행되는 연속 작업을 만듭니다.</summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task" />입니다.</returns>
      <param name="continuationAction">
        <see cref="T:System.Threading.Tasks.Task" />이 완료될 때 실행할 동작입니다.실행될 때 이 대리자에는 완료된 작업이 인수로 전달됩니다.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task},System.Threading.CancellationToken)">
      <summary>대상 <see cref="T:System.Threading.Tasks.Task" />이(가) 완료될 때 취소 토큰을 받고 비동기적으로 실행되는 연속 작업을 만듭니다.</summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task" />입니다.</returns>
      <param name="continuationAction">
        <see cref="T:System.Threading.Tasks.Task" />이 완료될 때 실행할 동작입니다.실행될 때 이 대리자에는 완료된 작업이 인수로 전달됩니다.</param>
      <param name="cancellationToken">새 연속 작업에 할당할 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />입니다.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created the token has already been disposed. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>지정된 <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />에 따라 대상 작업이 완료되면 실행되는 연속 작업을 만듭니다.연속 작업이 취소 토큰을 받고 지정된 스케줄러를 사용합니다.</summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task" />입니다.</returns>
      <param name="continuationAction">지정된 <paramref name="continuationOptions" />에 따라 실행되는 작업입니다.실행될 때 이 대리자에는 완료된 작업이 인수로 전달됩니다.</param>
      <param name="cancellationToken">새 연속 작업에 할당할 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />입니다.</param>
      <param name="continuationOptions">연속 작업이 예약되는 시기와 연속 작업이 동작하는 방식에 대한 옵션입니다.여기에는 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />와 같은 조건과 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" /> 등의 실행 옵션이 포함됩니다.</param>
      <param name="scheduler">연속 작업과 연결하고 연속 작업의 실행에 사용할 <see cref="T:System.Threading.Tasks.TaskScheduler" />입니다.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created the token has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>지정된 <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />에 따라 대상 작업이 완료되면 실행되는 연속 작업을 만듭니다.</summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task" />입니다.</returns>
      <param name="continuationAction">지정된 <paramref name="continuationOptions" />에 따라 실행되는 작업입니다.실행될 때 이 대리자에는 완료된 작업이 인수로 전달됩니다.</param>
      <param name="continuationOptions">연속 작업이 예약되는 시기와 연속 작업이 동작하는 방식에 대한 옵션입니다.여기에는 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />와 같은 조건과 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" /> 등의 실행 옵션이 포함됩니다.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task},System.Threading.Tasks.TaskScheduler)">
      <summary>대상 <see cref="T:System.Threading.Tasks.Task" />가 완료될 때 비동기적으로 실행되는 연속 작업을 만듭니다.연속 작업은 지정된 스케줄러를 사용합니다.</summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task" />입니다.</returns>
      <param name="continuationAction">
        <see cref="T:System.Threading.Tasks.Task" />이 완료될 때 실행할 동작입니다.실행될 때 이 대리자에는 완료된 작업이 인수로 전달됩니다.</param>
      <param name="scheduler">연속 작업과 연결하고 연속 작업의 실행에 사용할 <see cref="T:System.Threading.Tasks.TaskScheduler" />입니다.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null. -or-The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task,System.Object},System.Object)">
      <summary>호출자 제공 상태 정보를 받으며 대상 <see cref="T:System.Threading.Tasks.Task" />이(가) 완료되면 실행되는 연속 작업을 만듭니다. </summary>
      <returns>새 연속 작업입니다. </returns>
      <param name="continuationAction">작업이 완료될 때 실행할 동작입니다.실행되는 경우 대리자에게 완료된 작업과 호출자 제공 상태 개체가 인수로 전달됩니다.</param>
      <param name="state">연속 동작에서 사용할 데이터를 나타내는 개체입니다. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task,System.Object},System.Object,System.Threading.CancellationToken)">
      <summary>대상 <see cref="T:System.Threading.Tasks.Task" />이(가) 완료될 때 호출자 제공 상태 정보 및 취소 토큰을 받고 비동기적으로 실행되는 연속 작업을 만듭니다.</summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task" />입니다.</returns>
      <param name="continuationAction">
        <see cref="T:System.Threading.Tasks.Task" />이 완료될 때 실행할 동작입니다.실행되는 경우 대리자에게 완료된 작업과 호출자 제공 상태 개체가 인수로 전달됩니다.</param>
      <param name="state">연속 동작에서 사용할 데이터를 나타내는 개체입니다.</param>
      <param name="cancellationToken">새 연속 작업에 할당할 <see cref="T:System.Threading.CancellationToken" />입니다.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task,System.Object},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>대상 <see cref="T:System.Threading.Tasks.Task" />이(가) 완료될 때 호출자 제공 상태 정보 및 취소 토큰을 받고 실행되는 연속 작업을 만듭니다.연속 작업은 지정된 조건의 집합에 따라 실행되며 지정된 스케줄러를 사용합니다.</summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task" />입니다.</returns>
      <param name="continuationAction">
        <see cref="T:System.Threading.Tasks.Task" />이 완료될 때 실행할 동작입니다.실행되는 경우 대리자에게 완료된 작업과 호출자 제공 상태 개체가 인수로 전달됩니다.</param>
      <param name="state">연속 동작에서 사용할 데이터를 나타내는 개체입니다.</param>
      <param name="cancellationToken">새 연속 작업에 할당할 <see cref="T:System.Threading.CancellationToken" />입니다.</param>
      <param name="continuationOptions">연속 작업이 예약되는 시기와 연속 작업이 동작하는 방식에 대한 옵션입니다.여기에는 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />와 같은 조건과 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" /> 등의 실행 옵션이 포함됩니다.</param>
      <param name="scheduler">연속 작업과 연결하고 연속 작업의 실행에 사용할 <see cref="T:System.Threading.Tasks.TaskScheduler" />입니다.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task,System.Object},System.Object,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>호출자 제공 상태 정보를 받으며 대상 <see cref="T:System.Threading.Tasks.Task" />이(가) 완료되면 실행되는 연속 작업을 만듭니다.연속 작업은 지정된 조건의 집합에 따라 실행됩니다.</summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task" />입니다.</returns>
      <param name="continuationAction">
        <see cref="T:System.Threading.Tasks.Task" />이 완료될 때 실행할 동작입니다.실행되는 경우 대리자에게 완료된 작업과 호출자 제공 상태 개체가 인수로 전달됩니다.</param>
      <param name="state">연속 동작에서 사용할 데이터를 나타내는 개체입니다.</param>
      <param name="continuationOptions">연속 작업이 예약되는 시기와 연속 작업이 동작하는 방식에 대한 옵션입니다.여기에는 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />와 같은 조건과 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" /> 등의 실행 옵션이 포함됩니다.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task,System.Object},System.Object,System.Threading.Tasks.TaskScheduler)">
      <summary>호출자 제공 상태 정보를 받으며 대상 <see cref="T:System.Threading.Tasks.Task" />이(가) 완료되면 비동기적으로 실행되는 연속 작업을 만듭니다.연속 작업은 지정된 스케줄러를 사용합니다.</summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task" />입니다.</returns>
      <param name="continuationAction">
        <see cref="T:System.Threading.Tasks.Task" />이 완료될 때 실행할 동작입니다.실행되는 경우 대리자에게 완료된 작업과 호출자 제공 상태 개체가 인수로 전달됩니다.</param>
      <param name="state">연속 동작에서 사용할 데이터를 나타내는 개체입니다.</param>
      <param name="scheduler">연속 작업과 연결하고 연속 작업의 실행에 사용할 <see cref="T:System.Threading.Tasks.TaskScheduler" />입니다.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,``0})">
      <summary>대상 <see cref="T:System.Threading.Tasks.Task`1" />이(가) 완료되고 값을 가져오면 비동기적으로 실행되는 연속 작업을 만듭니다. </summary>
      <returns>새 연속 작업입니다. </returns>
      <param name="continuationFunction">
        <see cref="T:System.Threading.Tasks.Task`1" />이(가) 완료되면 실행되는 함수입니다.실행될 때 이 대리자에는 완료된 작업이 인수로 전달됩니다.</param>
      <typeparam name="TResult"> 연속 작업에서 생성되는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,``0},System.Threading.CancellationToken)">
      <summary>대상 <see cref="T:System.Threading.Tasks.Task" />이(가) 완료되고 값을 가져오면 비동기적으로 실행되는 연속 작업을 만듭니다.연속 작업이 취소 토큰을 받습니다.</summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</returns>
      <param name="continuationFunction">
        <see cref="T:System.Threading.Tasks.Task" />이 완료될 때 실행할 함수입니다.실행될 때 이 대리자에는 완료된 작업이 인수로 전달됩니다.</param>
      <param name="cancellationToken">새 연속 작업에 할당할 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />입니다.</param>
      <typeparam name="TResult"> 연속 작업에서 생성되는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created the token has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,``0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>지정된 연속 작업 옵션에 따라 실행되고 값을 가져오는 연속 작업을 만듭니다.연속 작업에 취소 토큰이 전달되고 지정된 스케줄러를 사용합니다.</summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</returns>
      <param name="continuationFunction">지정된 <paramref name="continuationOptions." />에 따라 실행할 함수. 실행될 때 이 대리자에는 완료된 작업이 인수로 전달됩니다.</param>
      <param name="cancellationToken">새 연속 작업에 할당할 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />입니다.</param>
      <param name="continuationOptions">연속 작업이 예약되는 시기와 연속 작업이 동작하는 방식에 대한 옵션입니다.여기에는 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />와 같은 조건과 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" /> 등의 실행 옵션이 포함됩니다.</param>
      <param name="scheduler">연속 작업과 연결하고 연속 작업의 실행에 사용할 <see cref="T:System.Threading.Tasks.TaskScheduler" />입니다.</param>
      <typeparam name="TResult"> 연속 작업에서 생성되는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created the token has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,``0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>지정된 연속 작업 옵션에 따라 실행되고 값을 가져오는 연속 작업을 만듭니다. </summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</returns>
      <param name="continuationFunction">
        <paramref name="continuationOptions" />에 지정된 조건에 따라 실행할 함수입니다.실행될 때 이 대리자에는 완료된 작업이 인수로 전달됩니다.</param>
      <param name="continuationOptions">연속 작업이 예약되는 시기와 연속 작업이 동작하는 방식에 대한 옵션입니다.여기에는 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />와 같은 조건과 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" /> 등의 실행 옵션이 포함됩니다.</param>
      <typeparam name="TResult"> 연속 작업에서 생성되는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,``0},System.Threading.Tasks.TaskScheduler)">
      <summary>대상 <see cref="T:System.Threading.Tasks.Task" />이(가) 완료되고 값을 가져오면 비동기적으로 실행되는 연속 작업을 만듭니다.연속 작업은 지정된 스케줄러를 사용합니다.</summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</returns>
      <param name="continuationFunction">
        <see cref="T:System.Threading.Tasks.Task" />이 완료될 때 실행할 함수입니다.실행될 때 이 대리자에는 완료된 작업이 인수로 전달됩니다.</param>
      <param name="scheduler">연속 작업과 연결하고 연속 작업의 실행에 사용할 <see cref="T:System.Threading.Tasks.TaskScheduler" />입니다.</param>
      <typeparam name="TResult"> 연속 작업에서 생성되는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,System.Object,``0},System.Object)">
      <summary>호출자 제공 상태 정보를 받으며 대상 <see cref="T:System.Threading.Tasks.Task" />이(가) 완료되고 값을 가져오면 비동기적으로 실행되는 연속 작업을 만듭니다. </summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</returns>
      <param name="continuationFunction">
        <see cref="T:System.Threading.Tasks.Task" />이 완료될 때 실행할 함수입니다.실행되는 경우 대리자에게 완료된 작업과 호출자 제공 상태 개체가 인수로 전달됩니다.</param>
      <param name="state">연속 함수에서 사용할 데이터를 나타내는 개체입니다.</param>
      <typeparam name="TResult">연속 작업에서 생성되는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,System.Object,``0},System.Object,System.Threading.CancellationToken)">
      <summary>대상 <see cref="T:System.Threading.Tasks.Task" />이(가) 완료되고 값을 가져오면 비동기적으로 실행되는 연속 작업을 만듭니다.연속 작업은 호출자 제공한 상태 정보 및 취소 토큰을 받습니다.</summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</returns>
      <param name="continuationFunction">
        <see cref="T:System.Threading.Tasks.Task" />이 완료될 때 실행할 함수입니다.실행되는 경우 대리자에게 완료된 작업과 호출자 제공 상태 개체가 인수로 전달됩니다.</param>
      <param name="state">연속 함수에서 사용할 데이터를 나타내는 개체입니다.</param>
      <param name="cancellationToken">새 연속 작업에 할당할 <see cref="T:System.Threading.CancellationToken" />입니다.</param>
      <typeparam name="TResult">연속 작업에서 생성되는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,System.Object,``0},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>대상 <see cref="T:System.Threading.Tasks.Task" />이(가) 완료되고 값을 가져오면 지정된 작업 연속 옵션에 따라 실행되는 연속 작업을 만듭니다.연속 작업은 호출자 제공 상태 정보 및 취소 토큰을 받고 지정된 스케줄러를 사용합니다.</summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</returns>
      <param name="continuationFunction">
        <see cref="T:System.Threading.Tasks.Task" />이 완료될 때 실행할 함수입니다.실행되는 경우 대리자에게 완료된 작업과 호출자 제공 상태 개체가 인수로 전달됩니다.</param>
      <param name="state">연속 함수에서 사용할 데이터를 나타내는 개체입니다.</param>
      <param name="cancellationToken">새 연속 작업에 할당할 <see cref="T:System.Threading.CancellationToken" />입니다.</param>
      <param name="continuationOptions">연속 작업이 예약되는 시기와 연속 작업이 동작하는 방식에 대한 옵션입니다.여기에는 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />와 같은 조건과 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" /> 등의 실행 옵션이 포함됩니다.</param>
      <param name="scheduler">연속 작업과 연결하고 연속 작업의 실행에 사용할 <see cref="T:System.Threading.Tasks.TaskScheduler" />입니다.</param>
      <typeparam name="TResult">연속 작업에서 생성되는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,System.Object,``0},System.Object,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>대상 <see cref="T:System.Threading.Tasks.Task" />이(가) 완료되면 지정된 작업 연속 옵션에 따라 실행되는 연속 작업을 만듭니다.연속 작업은 호출자 제공 상태 정보를 받습니다.</summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</returns>
      <param name="continuationFunction">
        <see cref="T:System.Threading.Tasks.Task" />이 완료될 때 실행할 함수입니다.실행되는 경우 대리자에게 완료된 작업과 호출자 제공 상태 개체가 인수로 전달됩니다.</param>
      <param name="state">연속 함수에서 사용할 데이터를 나타내는 개체입니다.</param>
      <param name="continuationOptions">연속 작업이 예약되는 시기와 연속 작업이 동작하는 방식에 대한 옵션입니다.여기에는 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />와 같은 조건과 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" /> 등의 실행 옵션이 포함됩니다.</param>
      <typeparam name="TResult">연속 작업에서 생성되는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,System.Object,``0},System.Object,System.Threading.Tasks.TaskScheduler)">
      <summary>대상 <see cref="T:System.Threading.Tasks.Task" />가 완료될 때 비동기적으로 실행되는 연속 작업을 만듭니다.연속 작업은 호출자 제공 상태 정보를 받고 지정된 스케줄러를 사용합니다.</summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</returns>
      <param name="continuationFunction">
        <see cref="T:System.Threading.Tasks.Task" />이 완료될 때 실행할 함수입니다.실행되는 경우 대리자에게 완료된 작업과 호출자 제공 상태 개체가 인수로 전달됩니다.</param>
      <param name="state">연속 함수에서 사용할 데이터를 나타내는 개체입니다.</param>
      <param name="scheduler">연속 작업과 연결하고 연속 작업의 실행에 사용할 <see cref="T:System.Threading.Tasks.TaskScheduler" />입니다.</param>
      <typeparam name="TResult">연속 작업에서 생성되는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="P:System.Threading.Tasks.Task.CreationOptions">
      <summary>이 작업을 만드는 데 사용된 <see cref="T:System.Threading.Tasks.TaskCreationOptions" />를 가져옵니다.</summary>
      <returns>이 작업을 만드는 데 사용된 <see cref="T:System.Threading.Tasks.TaskCreationOptions" />입니다.</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.CurrentId">
      <summary>현재 실행 중인 <see cref="T:System.Threading.Tasks.Task" />의 고유 ID를 반환합니다.</summary>
      <returns>시스템에서 현재 실행 중인 작업에 할당한 정수입니다.</returns>
    </member>
    <member name="M:System.Threading.Tasks.Task.Delay(System.Int32)">
      <summary>시간 지연 후 완료되는 작업을 만듭니다. </summary>
      <returns>시간 지연을 나타내는 작업입니다. </returns>
      <param name="millisecondsDelay">반환된 작업을 완료하기 전에 대기하는 시간(밀리초)입니다. -1은 무기한 대기를 나타냅니다. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="millisecondsDelay" /> argument is less than -1.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Delay(System.Int32,System.Threading.CancellationToken)">
      <summary>시간 지연 후 완료되는 취소 가능 작업을 만듭니다. </summary>
      <returns>시간 지연을 나타내는 작업입니다. </returns>
      <param name="millisecondsDelay">반환된 작업을 완료하기 전에 대기하는 시간(밀리초)입니다. -1은 무기한 대기를 나타냅니다. </param>
      <param name="cancellationToken">반환된 작업을 완료하기 전에 확인되는 취소 토큰입니다. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="millisecondsDelay" /> argument is less than -1. </exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled. </exception>
      <exception cref="T:System.ObjectDisposedException">The provided <paramref name="cancellationToken" /> has already been disposed. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Delay(System.TimeSpan)">
      <summary>지정된 시간 제한 간격 후 완료되는 작업을 만듭니다. </summary>
      <returns>시간 지연을 나타내는 작업입니다. </returns>
      <param name="delay">반환된 작업이 완료되기 전에 대기하는 시간 범위입니다. TimeSpan.FromMilliseconds(-1)는 무기한 대기를 나타냅니다. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="delay" /> represents a negative time interval other than TimeSpan.FromMillseconds(-1). -or-The <paramref name="delay" /> argument's <see cref="P:System.TimeSpan.TotalMilliseconds" /> property is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Delay(System.TimeSpan,System.Threading.CancellationToken)">
      <summary>지정된 시간 제한 간격 후 완료되는 취소 가능 작업을 만듭니다. </summary>
      <returns>시간 지연을 나타내는 작업입니다. </returns>
      <param name="delay">반환된 작업이 완료되기 전에 대기하는 시간 범위입니다. TimeSpan.FromMilliseconds(-1)는 무기한 대기를 나타냅니다. </param>
      <param name="cancellationToken">반환된 작업을 완료하기 전에 확인되는 취소 토큰입니다. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="delay" /> represents a negative time interval other than TimeSpan.FromMillseconds(-1). -or-The <paramref name="delay" /> argument's <see cref="P:System.TimeSpan.TotalMilliseconds" /> property is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <paramref name="cancellationToken" /> has already been disposed. </exception>
    </member>
    <member name="P:System.Threading.Tasks.Task.Exception">
      <summary>
        <see cref="T:System.AggregateException" />가 중간에 종료되도록 한 <see cref="T:System.Threading.Tasks.Task" />을 가져옵니다.<see cref="T:System.Threading.Tasks.Task" />가 완료되었거나 예외를 아직 throw하지 않은 경우 null을 반환합니다.</summary>
      <returns>
        <see cref="T:System.AggregateException" />가 중간에 종료되도록 한 <see cref="T:System.Threading.Tasks.Task" />입니다.</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.Factory">
      <summary>
        <see cref="T:System.Threading.Tasks.Task" /> 및 <see cref="T:System.Threading.Tasks.Task`1" /> 인스턴스를 만들고 구성하는 팩터리 메서드에 대한 액세스를 제공합니다.</summary>
      <returns>다양한 <see cref="T:System.Threading.Tasks.Task" /> 및 <see cref="T:System.Threading.Tasks.Task`1" /> 개체를 만들 수 있는 팩터리 개체입니다. </returns>
    </member>
    <member name="M:System.Threading.Tasks.Task.FromCanceled(System.Threading.CancellationToken)">
      <summary>지정된 취소 토큰을 사용하여 취소로 인해 완료된 <see cref="T:System.Threading.Tasks.Task" />를 만듭니다.</summary>
      <returns>취소된 작업입니다. </returns>
      <param name="cancellationToken">작업을 완료하는 데 사용할 취소 토큰입니다. </param>
    </member>
    <member name="M:System.Threading.Tasks.Task.FromCanceled``1(System.Threading.CancellationToken)">
      <summary>지정된 취소 토큰을 사용하여 취소로 인해 완료된 <see cref="T:System.Threading.Tasks.Task`1" />를 만듭니다.</summary>
      <returns>취소된 작업입니다. </returns>
      <param name="cancellationToken">작업을 완료하는 데 사용할 취소 토큰입니다. </param>
      <typeparam name="TResult">작업에서 반환되는 결과의 형식입니다. </typeparam>
    </member>
    <member name="M:System.Threading.Tasks.Task.FromException``1(System.Exception)">
      <summary>지정된 예외를 사용하여 완료된 <see cref="T:System.Threading.Tasks.Task`1" />을 만듭니다. </summary>
      <returns>오류가 발생한 작업입니다. </returns>
      <param name="exception">작업을 완료하는 데 사용할 예외입니다. </param>
      <typeparam name="TResult">작업에서 반환되는 결과의 형식입니다. </typeparam>
    </member>
    <member name="M:System.Threading.Tasks.Task.FromException(System.Exception)">
      <summary>지정된 예외를 사용하여 완료된 <see cref="T:System.Threading.Tasks.Task" />를 만듭니다. </summary>
      <returns>오류가 발생한 작업입니다. </returns>
      <param name="exception">작업을 완료하는 데 사용할 예외입니다. </param>
    </member>
    <member name="M:System.Threading.Tasks.Task.FromResult``1(``0)">
      <summary>지정된 결과로 완료되는 <see cref="T:System.Threading.Tasks.Task`1" />을 만듭니다.</summary>
      <returns>작업을 완료했습니다.</returns>
      <param name="result">완료된 작업에 저장할 결과입니다. </param>
      <typeparam name="TResult">작업에서 반환되는 결과의 형식입니다. </typeparam>
    </member>
    <member name="M:System.Threading.Tasks.Task.GetAwaiter">
      <summary>이 <see cref="T:System.Threading.Tasks.Task" />를 기다리는 데 사용되는 awaiter를 가져옵니다.</summary>
      <returns>awaiter 인스턴스입니다.</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.Id">
      <summary>이 <see cref="T:System.Threading.Tasks.Task" /> 인스턴스의 고유 ID를 가져옵니다.</summary>
      <returns>시스템에서 이 작업 인스턴스에 할당한 정수입니다. </returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.IsCanceled">
      <summary>이 <see cref="T:System.Threading.Tasks.Task" /> 인스턴스가 취소되어 실행을 완료했는지를 가져옵니다.</summary>
      <returns>이 작업이 취소되어 완료되었으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.IsCompleted">
      <summary>이 <see cref="T:System.Threading.Tasks.Task" />가 완료되었는지를 가져옵니다.</summary>
      <returns>작업이 완료되었으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.IsFaulted">
      <summary>처리되지 않은 예외로 인해 <see cref="T:System.Threading.Tasks.Task" />가 완료되었는지를 가져옵니다.</summary>
      <returns>이 작업이 처리되지 않은 예외를 throw했으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run(System.Action)">
      <summary>지정한 작업을 스레드 풀에서 실행하도록 큐에 대기시키고 해당 작업에 대한 작업 핸들을 반환합니다.</summary>
      <returns>스레드 풀에서 실행하도록 큐에 대기된 작업(work)을 나타내는 작업(task)입니다.</returns>
      <param name="action">비동기적으로 실행할 작업입니다.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> parameter was null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run(System.Action,System.Threading.CancellationToken)">
      <summary>지정한 작업을 스레드 풀에서 실행하도록 큐에 대기시키고 해당 작업에 대한 작업 핸들을 반환합니다.</summary>
      <returns>스레드 풀에서 실행하도록 큐에 대기된 작업(work)을 나타내는 작업(task)입니다.</returns>
      <param name="action">비동기적으로 실행할 작업입니다.</param>
      <param name="cancellationToken">작업을 취소하는 데 사용해야 하는 취소 토큰입니다.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> parameter was null.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with <paramref name="cancellationToken" /> was disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run``1(System.Func{System.Threading.Tasks.Task{``0}})">
      <summary>지정한 작업을 ThreadPool에서 실행하도록 큐에 대기시키고 Task(TResult)에서 반환된 <paramref name="function" />에 대한 프록시를 반환합니다.</summary>
      <returns>Task(TResult)에서 반환하는 Task(TResult)에 대한 프록시를 나타내는 <paramref name="function" />입니다.</returns>
      <param name="function">비동기적으로 실행할 작업입니다.</param>
      <typeparam name="TResult">프록시 작업에서 반환되는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter was null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run``1(System.Func{System.Threading.Tasks.Task{``0}},System.Threading.CancellationToken)">
      <summary>지정한 작업을 ThreadPool에서 실행하도록 큐에 대기시키고 Task(TResult)에서 반환된 <paramref name="function" />에 대한 프록시를 반환합니다.</summary>
      <returns>Task(TResult)에서 반환하는 Task(TResult)에 대한 프록시를 나타내는 <paramref name="function" />입니다.</returns>
      <param name="function">비동기적으로 실행할 작업입니다.</param>
      <param name="cancellationToken">작업을 취소하는 데 사용해야 하는 취소 토큰입니다.</param>
      <typeparam name="TResult">프록시 작업에서 반환되는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter was null.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with <paramref name="cancellationToken" /> was disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run(System.Func{System.Threading.Tasks.Task})">
      <summary>지정한 작업을 ThreadPool에서 실행하도록 큐에 대기시키고 <paramref name="function" />에서 반환된 작업에 대한 프록시를 반환합니다.</summary>
      <returns>
        <paramref name="function" />에서 반환하는 작업에 대한 프록시를 나타내는 작업입니다.</returns>
      <param name="function">비동기적으로 실행할 작업입니다.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter was null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run(System.Func{System.Threading.Tasks.Task},System.Threading.CancellationToken)">
      <summary>지정한 작업을 ThreadPool에서 실행하도록 큐에 대기시키고 <paramref name="function" />에서 반환된 작업에 대한 프록시를 반환합니다.</summary>
      <returns>
        <paramref name="function" />에서 반환하는 작업에 대한 프록시를 나타내는 작업입니다.</returns>
      <param name="function">비동기적으로 실행할 작업입니다. </param>
      <param name="cancellationToken">작업을 취소하는 데 사용해야 하는 취소 토큰입니다. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter was null.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with <paramref name="cancellationToken" /> was disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run``1(System.Func{``0})">
      <summary>지정한 작업을 스레드 풀에서 실행하도록 큐에 대기시키고 작업을 나타내는 <see cref="T:System.Threading.Tasks.Task`1" /> 개체를 반환합니다. </summary>
      <returns>스레드 풀에서 실행하도록 큐에 대기된 작업을 나타내는 작업 개체입니다. </returns>
      <param name="function">비동기적으로 실행할 작업입니다. </param>
      <typeparam name="TResult">작업의 반환 유형입니다. </typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter is null. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run``1(System.Func{``0},System.Threading.CancellationToken)">
      <summary>지정한 작업을 스레드 풀에서 실행하도록 큐에 대기시키고 해당 작업에 대한 Task(TResult) 핸들을 반환합니다.</summary>
      <returns>ThreadPool에서 실행하도록 큐에 대기된 작업을 나타내는 Task(TResult)입니다.</returns>
      <param name="function">비동기적으로 실행할 작업입니다.</param>
      <param name="cancellationToken">작업을 취소하는 데 사용해야 하는 취소 토큰입니다.</param>
      <typeparam name="TResult">작업의 결과 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter was null.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with <paramref name="cancellationToken" /> was disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.RunSynchronously">
      <summary>현재 <see cref="T:System.Threading.Tasks.Task" />에서 <see cref="T:System.Threading.Tasks.TaskScheduler" />를 동기적으로 실행합니다.</summary>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> instance has been disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Threading.Tasks.Task" /> is not in a valid state to be started.It may have already been started, executed, or canceled, or it may have been created in a manner that doesn't support direct scheduling.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.RunSynchronously(System.Threading.Tasks.TaskScheduler)">
      <summary>제공된 <see cref="T:System.Threading.Tasks.Task" />에서 <see cref="T:System.Threading.Tasks.TaskScheduler" />를 동기적으로 실행합니다.</summary>
      <param name="scheduler">이 작업을 인라인으로 실행하려고 하는 스케줄러입니다.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> instance has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Threading.Tasks.Task" /> is not in a valid state to be started.It may have already been started, executed, or canceled, or it may have been created in a manner that doesn't support direct scheduling.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Start">
      <summary>
        <see cref="T:System.Threading.Tasks.Task" />를 시작하고 현재 <see cref="T:System.Threading.Tasks.TaskScheduler" />에 실행을 예약합니다.</summary>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> instance has been disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Threading.Tasks.Task" /> is not in a valid state to be started.It may have already been started, executed, or canceled, or it may have been created in a manner that doesn't support direct scheduling.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Start(System.Threading.Tasks.TaskScheduler)">
      <summary>
        <see cref="T:System.Threading.Tasks.Task" />를 시작하고 지정된 <see cref="T:System.Threading.Tasks.TaskScheduler" />에 실행을 예약합니다.</summary>
      <param name="scheduler">이 작업을 연결하고 실행할 <see cref="T:System.Threading.Tasks.TaskScheduler" />입니다.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> instance has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Threading.Tasks.Task" /> is not in a valid state to be started.It may have already been started, executed, or canceled, or it may have been created in a manner that doesn't support direct scheduling.</exception>
    </member>
    <member name="P:System.Threading.Tasks.Task.Status">
      <summary>이 작업의 <see cref="T:System.Threading.Tasks.TaskStatus" />를 가져옵니다.</summary>
      <returns>이 작업 인스턴스의 현재 <see cref="T:System.Threading.Tasks.TaskStatus" />입니다.</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.System#IAsyncResult#AsyncWaitHandle">
      <summary>작업이 완료되기를 기다리는 데 사용할 수 있는 <see cref="T:System.Threading.WaitHandle" />을 가져옵니다.</summary>
      <returns>작업이 완료되기를 기다리는 데 사용할 수 있는 <see cref="T:System.Threading.WaitHandle" />입니다.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
    </member>
    <member name="P:System.Threading.Tasks.Task.System#IAsyncResult#CompletedSynchronously">
      <summary>작업이 동기적으로 완료되었는지를 나타내는 표시를 가져옵니다.</summary>
      <returns>작업이 동기적으로 끝났으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="M:System.Threading.Tasks.Task.Wait">
      <summary>
        <see cref="T:System.Threading.Tasks.Task" />의 실행이 완료되기를 기다립니다.</summary>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.AggregateException">The task was canceled.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object.-or-An exception was thrown during the execution of the task.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Wait(System.Int32)">
      <summary>
        <see cref="T:System.Threading.Tasks.Task" />가 지정된 시간(밀리초) 내에 실행을 완료할 때까지 기다립니다.</summary>
      <returns>true의 실행이 할당된 시간 안에 완료되었으면 <see cref="T:System.Threading.Tasks.Task" />이고, 그렇지 않으면 false입니다.</returns>
      <param name="millisecondsTimeout">대기할 시간(밀리초)이거나, 무기한 대기할 경우 <see cref="F:System.Threading.Timeout.Infinite" />(-1)입니다.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.AggregateException">The task was canceled.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object.-or-An exception was thrown during the execution of the task.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Wait(System.Int32,System.Threading.CancellationToken)">
      <summary>
        <see cref="T:System.Threading.Tasks.Task" />의 실행이 완료되기를 기다립니다.작업이 완료되기 전에 취소 토큰이 취소되었거나 시간 제한 간격이 경과되었으면 대기가 종료됩니다.</summary>
      <returns>true의 실행이 할당된 시간 안에 완료되었으면 <see cref="T:System.Threading.Tasks.Task" />이고, 그렇지 않으면 false입니다.</returns>
      <param name="millisecondsTimeout">대기할 시간(밀리초)이거나, 무기한 대기할 경우 <see cref="F:System.Threading.Timeout.Infinite" />(-1)입니다. </param>
      <param name="cancellationToken">작업이 완료되기를 기다리는 동안 관찰할 취소 토큰입니다. </param>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.AggregateException">The task was canceled.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object.-or-An exception was thrown during the execution of the task.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Wait(System.Threading.CancellationToken)">
      <summary>
        <see cref="T:System.Threading.Tasks.Task" />의 실행이 완료되기를 기다립니다.작업이 완료되기 전에 취소 토큰이 취소되면 대기가 종료됩니다.</summary>
      <param name="cancellationToken">작업이 완료되기를 기다리는 동안 관찰할 취소 토큰입니다. </param>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The task has been disposed.</exception>
      <exception cref="T:System.AggregateException">The task was canceled.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object.-or-An exception was thrown during the execution of the task.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Wait(System.TimeSpan)">
      <summary>
        <see cref="T:System.Threading.Tasks.Task" />가 지정된 시간 간격 내에 실행을 완료할 때까지 기다립니다.</summary>
      <returns>true의 실행이 할당된 시간 안에 완료되었으면 <see cref="T:System.Threading.Tasks.Task" />이고, 그렇지 않으면 false입니다.</returns>
      <param name="timeout">대기할 시간(밀리초)을 나타내는 <see cref="T:System.TimeSpan" />이거나, 무한 대기하도록 -1밀리초를 나타내는 <see cref="T:System.TimeSpan" />입니다.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> is a negative number other than -1 milliseconds, which represents an infinite time-out. -or-<paramref name="timeout" /> is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
      <exception cref="T:System.AggregateException">The task was canceled.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object.-or-An exception was thrown during the execution of the task.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAll(System.Threading.Tasks.Task[])">
      <summary>제공된 모든 <see cref="T:System.Threading.Tasks.Task" /> 개체의 실행이 완료되기를 기다립니다.</summary>
      <param name="tasks">대기할 <see cref="T:System.Threading.Tasks.Task" /> 인스턴스의 배열입니다.</param>
      <exception cref="T:System.ObjectDisposedException">One or more of the <see cref="T:System.Threading.Tasks.Task" /> objects in <paramref name="tasks" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.-or-The <paramref name="tasks" /> argument contains a null element.</exception>
      <exception cref="T:System.AggregateException">At least one of the <see cref="T:System.Threading.Tasks.Task" /> instances was canceled.If a task was canceled, the <see cref="T:System.AggregateException" /> exception contains an <see cref="T:System.OperationCanceledException" /> exception in its <see cref="P:System.AggregateException.InnerExceptions" /> collection.-or-An exception was thrown during the execution of at least one of the <see cref="T:System.Threading.Tasks.Task" /> instances. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAll(System.Threading.Tasks.Task[],System.Int32)">
      <summary>모든 제공된 <see cref="T:System.Threading.Tasks.Task" /> 개체가 지정된 시간(밀리초) 내에 실행을 완료할 때까지 기다립니다.</summary>
      <returns>모든 true 인스턴스의 실행이 할당된 시간 안에 완료되었으면 <see cref="T:System.Threading.Tasks.Task" />이고, 그렇지 않으면 false입니다.</returns>
      <param name="tasks">대기할 <see cref="T:System.Threading.Tasks.Task" /> 인스턴스의 배열입니다.</param>
      <param name="millisecondsTimeout">대기할 시간(밀리초)이거나, 무기한 대기할 경우 <see cref="F:System.Threading.Timeout.Infinite" />(-1)입니다.</param>
      <exception cref="T:System.ObjectDisposedException">One or more of the <see cref="T:System.Threading.Tasks.Task" /> objects in <paramref name="tasks" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">At least one of the <see cref="T:System.Threading.Tasks.Task" /> instances was canceled.If a task was canceled, the <see cref="T:System.AggregateException" /> contains an <see cref="T:System.OperationCanceledException" /> in its <see cref="P:System.AggregateException.InnerExceptions" /> collection.-or-An exception was thrown during the execution of at least one of the <see cref="T:System.Threading.Tasks.Task" /> instances. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAll(System.Threading.Tasks.Task[],System.Int32,System.Threading.CancellationToken)">
      <summary>제공된 모든 <see cref="T:System.Threading.Tasks.Task" /> 개체가 지정된 시간(밀리초) 내에 실행을 완료하기를 기다리거나 대기가 취소될 때까지 기다립니다.</summary>
      <returns>모든 true 인스턴스의 실행이 할당된 시간 안에 완료되었으면 <see cref="T:System.Threading.Tasks.Task" />이고, 그렇지 않으면 false입니다.</returns>
      <param name="tasks">대기할 <see cref="T:System.Threading.Tasks.Task" /> 인스턴스의 배열입니다.</param>
      <param name="millisecondsTimeout">대기할 시간(밀리초)이거나, 무기한 대기할 경우 <see cref="F:System.Threading.Timeout.Infinite" />(-1)입니다.</param>
      <param name="cancellationToken">작업이 완료되기를 기다리는 동안 확인할 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />입니다.</param>
      <exception cref="T:System.ObjectDisposedException">One or more of the <see cref="T:System.Threading.Tasks.Task" /> objects in <paramref name="tasks" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">At least one of the <see cref="T:System.Threading.Tasks.Task" /> instances was canceled.If a task was canceled, the <see cref="T:System.AggregateException" /> contains an <see cref="T:System.OperationCanceledException" /> in its <see cref="P:System.AggregateException.InnerExceptions" /> collection.-or-An exception was thrown during the execution of at least one of the <see cref="T:System.Threading.Tasks.Task" /> instances. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAll(System.Threading.Tasks.Task[],System.Threading.CancellationToken)">
      <summary>대기가 취소되지 않는 경우 제공된 모든 <see cref="T:System.Threading.Tasks.Task" /> 개체가 실행을 완료하기를 기다립니다. </summary>
      <param name="tasks">대기할 <see cref="T:System.Threading.Tasks.Task" /> 인스턴스의 배열입니다.</param>
      <param name="cancellationToken">작업이 완료되기를 기다리는 동안 확인할 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />입니다.</param>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">At least one of the <see cref="T:System.Threading.Tasks.Task" /> instances was canceled.If a task was canceled, the <see cref="T:System.AggregateException" /> contains an <see cref="T:System.OperationCanceledException" /> in its <see cref="P:System.AggregateException.InnerExceptions" /> collection.-or-An exception was thrown during the execution of at least one of the <see cref="T:System.Threading.Tasks.Task" /> instances. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
      <exception cref="T:System.ObjectDisposedException">One or more of the <see cref="T:System.Threading.Tasks.Task" /> objects in <paramref name="tasks" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAll(System.Threading.Tasks.Task[],System.TimeSpan)">
      <summary>모든 제공된 취소 가능한 <see cref="T:System.Threading.Tasks.Task" /> 개체가 지정된 시간 간격 내에 실행을 완료할 때까지 기다립니다.</summary>
      <returns>모든 true 인스턴스의 실행이 할당된 시간 안에 완료되었으면 <see cref="T:System.Threading.Tasks.Task" />이고, 그렇지 않으면 false입니다.</returns>
      <param name="tasks">대기할 <see cref="T:System.Threading.Tasks.Task" /> 인스턴스의 배열입니다.</param>
      <param name="timeout">대기할 시간(밀리초)을 나타내는 <see cref="T:System.TimeSpan" />이거나, 무한 대기하도록 -1밀리초를 나타내는 <see cref="T:System.TimeSpan" />입니다.</param>
      <exception cref="T:System.ObjectDisposedException">One or more of the <see cref="T:System.Threading.Tasks.Task" /> objects in <paramref name="tasks" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null. </exception>
      <exception cref="T:System.AggregateException">At least one of the <see cref="T:System.Threading.Tasks.Task" /> instances was canceled.If a task was canceled, the <see cref="T:System.AggregateException" /> contains an <see cref="T:System.OperationCanceledException" /> in its <see cref="P:System.AggregateException.InnerExceptions" /> collection.-or-An exception was thrown during the execution of at least one of the <see cref="T:System.Threading.Tasks.Task" /> instances. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> is a negative number other than -1 milliseconds, which represents an infinite time-out. -or-<paramref name="timeout" /> is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAny(System.Threading.Tasks.Task[])">
      <summary>제공된 <see cref="T:System.Threading.Tasks.Task" /> 개체 중 임의 개체의 실행이 완료되기를 기다립니다.</summary>
      <returns>
        <paramref name="tasks" /> 배열 인수에서 완료된 작업의 인덱스입니다.</returns>
      <param name="tasks">대기할 <see cref="T:System.Threading.Tasks.Task" /> 인스턴스의 배열입니다.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAny(System.Threading.Tasks.Task[],System.Int32)">
      <summary>모든 제공된 <see cref="T:System.Threading.Tasks.Task" /> 개체가 지정된 시간(밀리초) 내에 실행을 완료할 때까지 기다립니다.</summary>
      <returns>
        <paramref name="tasks" /> 배열 인수의 완료된 작업 인덱스이거나, 제한 시간이 초과되었으면 -1입니다.</returns>
      <param name="tasks">대기할 <see cref="T:System.Threading.Tasks.Task" /> 인스턴스의 배열입니다.</param>
      <param name="millisecondsTimeout">대기할 시간(밀리초)이거나, 무기한 대기할 경우 <see cref="F:System.Threading.Timeout.Infinite" />(-1)입니다.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAny(System.Threading.Tasks.Task[],System.Int32,System.Threading.CancellationToken)">
      <summary>모든 제공된 <see cref="T:System.Threading.Tasks.Task" /> 개체가 지정된 시간(밀리초) 내에 실행을 완료하기를 기다리거나 취소 토큰이 취소될 때까지 기다립니다.</summary>
      <returns>
        <paramref name="tasks" /> 배열 인수의 완료된 작업 인덱스이거나, 제한 시간이 초과되었으면 -1입니다.</returns>
      <param name="tasks">대기할 <see cref="T:System.Threading.Tasks.Task" /> 인스턴스의 배열입니다. </param>
      <param name="millisecondsTimeout">대기할 시간(밀리초)이거나, 무기한 대기할 경우 <see cref="F:System.Threading.Timeout.Infinite" />(-1)입니다. </param>
      <param name="cancellationToken">작업이 완료되기를 기다리는 동안 확인할 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />입니다. </param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAny(System.Threading.Tasks.Task[],System.Threading.CancellationToken)">
      <summary>대기가 취소되지 않는 경우 제공된 모든 <see cref="T:System.Threading.Tasks.Task" /> 개체가 실행을 완료하기를 기다립니다.</summary>
      <returns>
        <paramref name="tasks" /> 배열 인수에서 완료된 작업의 인덱스입니다.</returns>
      <param name="tasks">대기할 <see cref="T:System.Threading.Tasks.Task" /> 인스턴스의 배열입니다. </param>
      <param name="cancellationToken">작업이 완료되기를 기다리는 동안 확인할 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />입니다. </param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAny(System.Threading.Tasks.Task[],System.TimeSpan)">
      <summary>모든 제공된 <see cref="T:System.Threading.Tasks.Task" /> 개체가 지정된 시간 간격 내에 실행을 완료할 때까지 기다립니다.</summary>
      <returns>
        <paramref name="tasks" /> 배열 인수의 완료된 작업 인덱스이거나, 제한 시간이 초과되었으면 -1입니다.</returns>
      <param name="tasks">대기할 <see cref="T:System.Threading.Tasks.Task" /> 인스턴스의 배열입니다.</param>
      <param name="timeout">대기할 시간(밀리초)을 나타내는 <see cref="T:System.TimeSpan" />이거나, 무한 대기하도록 -1밀리초를 나타내는 <see cref="T:System.TimeSpan" />입니다.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> is a negative number other than -1 milliseconds, which represents an infinite time-out. -or-<paramref name="timeout" /> is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAll``1(System.Collections.Generic.IEnumerable{System.Threading.Tasks.Task{``0}})">
      <summary>열거 가능한 컬렉션의 모든 <see cref="T:System.Threading.Tasks.Task`1" /> 개체가 완료되면 완료될 작업을 만듭니다. </summary>
      <returns>제공된 모든 작업의 완료를 나타내는 작업입니다. </returns>
      <param name="tasks">완료를 기다리는 작업입니다. </param>
      <typeparam name="TResult">완료된 작업의 형식입니다. </typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> collection contained a null task. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAll(System.Collections.Generic.IEnumerable{System.Threading.Tasks.Task})">
      <summary>열거 가능한 컬렉션의 모든 <see cref="T:System.Threading.Tasks.Task" /> 개체가 완료되면 완료될 작업을 만듭니다.</summary>
      <returns>제공된 모든 작업의 완료를 나타내는 작업입니다. </returns>
      <param name="tasks">완료를 기다리는 작업입니다.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> collection contained a null task.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAll(System.Threading.Tasks.Task[])">
      <summary>배열의 모든 <see cref="T:System.Threading.Tasks.Task" /> 개체가 완료되면 완료될 작업을 만듭니다. </summary>
      <returns>제공된 모든 작업의 완료를 나타내는 작업입니다.</returns>
      <param name="tasks">완료를 기다리는 작업입니다.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAll``1(System.Threading.Tasks.Task{``0}[])">
      <summary>배열의 모든 <see cref="T:System.Threading.Tasks.Task`1" /> 개체가 완료되면 완료될 작업을 만듭니다. </summary>
      <returns>제공된 모든 작업의 완료를 나타내는 작업입니다.</returns>
      <param name="tasks">완료를 기다리는 작업입니다.</param>
      <typeparam name="TResult">완료된 작업의 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAny``1(System.Collections.Generic.IEnumerable{System.Threading.Tasks.Task{``0}})">
      <summary>제공된 작업을 모두 완료했을 때 완료할 작업을 만듭니다.</summary>
      <returns>제공된 한 작업의 완료를 나타내는 작업입니다.반환 작업의 결과는 완료된 작업입니다.</returns>
      <param name="tasks">완료를 기다리는 작업입니다.</param>
      <typeparam name="TResult">완료된 작업의 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task, or was empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAny(System.Collections.Generic.IEnumerable{System.Threading.Tasks.Task})">
      <summary>제공된 작업을 모두 완료했을 때 완료할 작업을 만듭니다.</summary>
      <returns>제공된 한 작업의 완료를 나타내는 작업입니다.반환 작업의 결과는 완료된 작업입니다.</returns>
      <param name="tasks">완료를 기다리는 작업입니다.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task, or was empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAny(System.Threading.Tasks.Task[])">
      <summary>제공된 작업을 모두 완료했을 때 완료할 작업을 만듭니다.</summary>
      <returns>제공된 한 작업의 완료를 나타내는 작업입니다.반환 작업의 결과는 완료된 작업입니다.</returns>
      <param name="tasks">완료를 기다리는 작업입니다.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task, or was empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAny``1(System.Threading.Tasks.Task{``0}[])">
      <summary>제공된 작업을 모두 완료했을 때 완료할 작업을 만듭니다.</summary>
      <returns>제공된 한 작업의 완료를 나타내는 작업입니다.반환 작업의 결과는 완료된 작업입니다.</returns>
      <param name="tasks">완료를 기다리는 작업입니다.</param>
      <typeparam name="TResult">완료된 작업의 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task, or was empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Yield">
      <summary>대기할 때 현재 컨텍스트로 비동기적으로 전환되는 awaitable 작업을 만듭니다.</summary>
      <returns>대기할 때 대기 시의 현재 컨텍스트로 비동기적으로 전환될 컨텍스트입니다.현재 <see cref="T:System.Threading.SynchronizationContext" />가 null이 아닌 경우 현재 컨텍스트로 처리됩니다.그렇지 않으면 현재 실행 중인 작업과 관련된 작업 스케줄러를 현재 컨텍스트로 처리됩니다.</returns>
    </member>
    <member name="T:System.Threading.Tasks.Task`1">
      <summary>값을 반환할 수 있는 비동기 작업을 나타냅니다.</summary>
      <typeparam name="TResult">이 <see cref="T:System.Threading.Tasks.Task`1" />에서 생성되는 결과의 형식입니다. </typeparam>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{`0})">
      <summary>지정된 함수를 사용하여 새 <see cref="T:System.Threading.Tasks.Task`1" />을 초기화합니다.</summary>
      <param name="function">작업에서 실행할 코드를 나타내는 대리자입니다.함수가 완료되면 작업의 <see cref="P:System.Threading.Tasks.Task`1.Result" /> 속성이 함수의 결과 값을 반환하도록 설정됩니다.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{`0},System.Threading.CancellationToken)">
      <summary>지정된 함수를 사용하여 새 <see cref="T:System.Threading.Tasks.Task`1" />을 초기화합니다.</summary>
      <param name="function">작업에서 실행할 코드를 나타내는 대리자입니다.함수가 완료되면 작업의 <see cref="P:System.Threading.Tasks.Task`1.Result" /> 속성이 함수의 결과 값을 반환하도록 설정됩니다.</param>
      <param name="cancellationToken">이 작업에 할당할 <see cref="T:System.Threading.CancellationToken" />입니다.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions)">
      <summary>지정된 함수 및 만들기 옵션을 사용하여 새 <see cref="T:System.Threading.Tasks.Task`1" />을 초기화합니다.</summary>
      <param name="function">작업에서 실행할 코드를 나타내는 대리자입니다.함수가 완료되면 작업의 <see cref="P:System.Threading.Tasks.Task`1.Result" /> 속성이 함수의 결과 값을 반환하도록 설정됩니다.</param>
      <param name="cancellationToken">새 작업에 할당할 <see cref="T:System.Threading.CancellationToken" />입니다.</param>
      <param name="creationOptions">작업의 동작을 사용자 지정하는 데 사용할 <see cref="T:System.Threading.Tasks.TaskCreationOptions" />입니다.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{`0},System.Threading.Tasks.TaskCreationOptions)">
      <summary>지정된 함수 및 만들기 옵션을 사용하여 새 <see cref="T:System.Threading.Tasks.Task`1" />을 초기화합니다.</summary>
      <param name="function">작업에서 실행할 코드를 나타내는 대리자입니다.함수가 완료되면 작업의 <see cref="P:System.Threading.Tasks.Task`1.Result" /> 속성이 함수의 결과 값을 반환하도록 설정됩니다.</param>
      <param name="creationOptions">작업의 동작을 사용자 지정하는 데 사용할 <see cref="T:System.Threading.Tasks.TaskCreationOptions" />입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{System.Object,`0},System.Object)">
      <summary>지정된 함수 및 상태를 사용하여 새 <see cref="T:System.Threading.Tasks.Task`1" />을 초기화합니다.</summary>
      <param name="function">작업에서 실행할 코드를 나타내는 대리자입니다.함수가 완료되면 작업의 <see cref="P:System.Threading.Tasks.Task`1.Result" /> 속성이 함수의 결과 값을 반환하도록 설정됩니다.</param>
      <param name="state">동작에서 사용할 데이터를 나타내는 개체입니다.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{System.Object,`0},System.Object,System.Threading.CancellationToken)">
      <summary>지정된 작업, 상태 및 옵션을 사용하여 새 <see cref="T:System.Threading.Tasks.Task`1" />를 초기화합니다.</summary>
      <param name="function">작업에서 실행할 코드를 나타내는 대리자입니다.함수가 완료되면 작업의 <see cref="P:System.Threading.Tasks.Task`1.Result" /> 속성이 함수의 결과 값을 반환하도록 설정됩니다.</param>
      <param name="state">함수에서 사용할 데이터를 나타내는 개체입니다.</param>
      <param name="cancellationToken">새 작업에 할당할 <see cref="T:System.Threading.CancellationToken" />입니다.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{System.Object,`0},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions)">
      <summary>지정된 작업, 상태 및 옵션을 사용하여 새 <see cref="T:System.Threading.Tasks.Task`1" />를 초기화합니다.</summary>
      <param name="function">작업에서 실행할 코드를 나타내는 대리자입니다.함수가 완료되면 작업의 <see cref="P:System.Threading.Tasks.Task`1.Result" /> 속성이 함수의 결과 값을 반환하도록 설정됩니다.</param>
      <param name="state">함수에서 사용할 데이터를 나타내는 개체입니다.</param>
      <param name="cancellationToken">새 작업에 할당할 <see cref="T:System.Threading.CancellationToken" />입니다.</param>
      <param name="creationOptions">작업의 동작을 사용자 지정하는 데 사용할 <see cref="T:System.Threading.Tasks.TaskCreationOptions" />입니다.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{System.Object,`0},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>지정된 작업, 상태 및 옵션을 사용하여 새 <see cref="T:System.Threading.Tasks.Task`1" />를 초기화합니다.</summary>
      <param name="function">작업에서 실행할 코드를 나타내는 대리자입니다.함수가 완료되면 작업의 <see cref="P:System.Threading.Tasks.Task`1.Result" /> 속성이 함수의 결과 값을 반환하도록 설정됩니다.</param>
      <param name="state">함수에서 사용할 데이터를 나타내는 개체입니다.</param>
      <param name="creationOptions">작업의 동작을 사용자 지정하는 데 사용할 <see cref="T:System.Threading.Tasks.TaskCreationOptions" />입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ConfigureAwait(System.Boolean)">
      <summary>이 <see cref="T:System.Threading.Tasks.Task`1" />를 기다리는 데 사용되는 awaiter를 구성합니다.</summary>
      <returns>이 작업을 기다리는 데 사용되는 개체입니다.</returns>
      <param name="continueOnCapturedContext">캡처된 원래 컨텍스트로 연속 마샬링하려면 true이고, 그렇지 않으면 false입니다.</param>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}})">
      <summary>대상 작업이 완료될 때 비동기적으로 실행되는 연속 작업을 만듭니다. </summary>
      <returns>새 연속 작업입니다. </returns>
      <param name="continuationAction">
        <see cref="T:System.Threading.Tasks.Task`1" /> 선행 작업이 완료될 때 실행할 동작입니다.실행될 때 이 대리자에는 완료된 작업이 인수로 전달됩니다.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}},System.Threading.CancellationToken)">
      <summary>대상 <see cref="T:System.Threading.Tasks.Task`1" />이 완료될 때 비동기적으로 실행되는 취소 가능 연속 작업을 만듭니다.</summary>
      <returns>새 연속 작업입니다. </returns>
      <param name="continuationAction">
        <see cref="T:System.Threading.Tasks.Task`1" />이 완료될 때 실행할 동작입니다.실행될 때 이 대리자에는 완료된 작업이 인수로 전달됩니다.</param>
      <param name="cancellationToken">새 연속 작업에 전달되는 취소 토큰입니다. </param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has been disposed. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>
        <paramref name="continuationOptions" />에 지정된 조건에 따라 실행되는 연속 작업을 만듭니다.</summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task" />입니다.</returns>
      <param name="continuationAction">
        <paramref name="continuationOptions" />에 지정된 조건에 따라 실행할 동작입니다.실행될 때 이 대리자에는 완료된 작업이 인수로 전달됩니다.</param>
      <param name="cancellationToken">새 연속 작업에 할당할 <see cref="T:System.Threading.CancellationToken" />입니다.</param>
      <param name="continuationOptions">연속 작업이 예약되는 시기와 연속 작업이 동작하는 방식에 대한 옵션입니다.여기에는 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />와 같은 조건과 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" /> 등의 실행 옵션이 포함됩니다.</param>
      <param name="scheduler">연속 작업과 연결하고 연속 작업의 실행에 사용할 <see cref="T:System.Threading.Tasks.TaskScheduler" />입니다.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>
        <paramref name="continuationOptions" />에 지정된 조건에 따라 실행되는 연속 작업을 만듭니다.</summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task" />입니다.</returns>
      <param name="continuationAction">
        <paramref name="continuationOptions" />에 지정된 조건에 따른 작업입니다.실행될 때 이 대리자에는 완료된 작업이 인수로 전달됩니다.</param>
      <param name="continuationOptions">연속 작업이 예약되는 시기와 연속 작업이 동작하는 방식에 대한 옵션입니다.여기에는 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />와 같은 조건과 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" /> 등의 실행 옵션이 포함됩니다.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}},System.Threading.Tasks.TaskScheduler)">
      <summary>대상 <see cref="T:System.Threading.Tasks.Task`1" />가 완료될 때 비동기적으로 실행되는 연속 작업을 만듭니다.</summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task" />입니다.</returns>
      <param name="continuationAction">
        <see cref="T:System.Threading.Tasks.Task`1" />이 완료될 때 실행할 동작입니다.실행될 때 이 대리자에는 완료된 작업이 인수로 전달됩니다.</param>
      <param name="scheduler">연속 작업과 연결하고 연속 작업의 실행에 사용할 <see cref="T:System.Threading.Tasks.TaskScheduler" />입니다.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0},System.Object},System.Object)">
      <summary>상태 정보가 전달되며 대상 <see cref="T:System.Threading.Tasks.Task`1" />이(가) 완료되면 실행되는 연속 작업을 만듭니다. </summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task" />입니다.</returns>
      <param name="continuationAction">
        <see cref="T:System.Threading.Tasks.Task`1" />이 완료될 때 실행할 동작입니다.실행되는 경우 대리자에게 완료된 작업과 호출자 제공 상태 개체가 인수로 전달됩니다.</param>
      <param name="state">연속 동작에서 사용할 데이터를 나타내는 개체입니다. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0},System.Object},System.Object,System.Threading.CancellationToken)">
      <summary>대상 <see cref="T:System.Threading.Tasks.Task`1" />이 완료될 때 실행되는 연속 작업을 만듭니다.</summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task" />입니다.</returns>
      <param name="continuationAction">
        <see cref="T:System.Threading.Tasks.Task`1" />이 완료될 때 실행할 동작입니다.실행되는 경우 대리자에게 완료된 작업과 호출자 제공 상태 개체가 인수로 전달됩니다.</param>
      <param name="state">연속 동작에서 사용할 데이터를 나타내는 개체입니다.</param>
      <param name="cancellationToken">새 연속 작업에 할당할 <see cref="T:System.Threading.CancellationToken" />입니다.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0},System.Object},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>대상 <see cref="T:System.Threading.Tasks.Task`1" />이 완료될 때 실행되는 연속 작업을 만듭니다.</summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task" />입니다.</returns>
      <param name="continuationAction">
        <see cref="T:System.Threading.Tasks.Task`1" />이 완료될 때 실행할 동작입니다.실행되는 경우 대리자에게 완료된 작업과 호출자 제공 상태 개체가 인수로 전달됩니다.</param>
      <param name="state">연속 동작에서 사용할 데이터를 나타내는 개체입니다.</param>
      <param name="cancellationToken">새 연속 작업에 할당할 <see cref="T:System.Threading.CancellationToken" />입니다.</param>
      <param name="continuationOptions">연속 작업이 예약되는 시기와 연속 작업이 동작하는 방식에 대한 옵션입니다.여기에는 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />와 같은 조건과 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" /> 등의 실행 옵션이 포함됩니다.</param>
      <param name="scheduler">연속 작업과 연결하고 연속 작업의 실행에 사용할 <see cref="T:System.Threading.Tasks.TaskScheduler" />입니다.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0},System.Object},System.Object,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>대상 <see cref="T:System.Threading.Tasks.Task`1" />이 완료될 때 실행되는 연속 작업을 만듭니다.</summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task" />입니다.</returns>
      <param name="continuationAction">
        <see cref="T:System.Threading.Tasks.Task`1" />이 완료될 때 실행할 동작입니다.실행되는 경우 대리자에게 완료된 작업과 호출자 제공 상태 개체가 인수로 전달됩니다.</param>
      <param name="state">연속 동작에서 사용할 데이터를 나타내는 개체입니다.</param>
      <param name="continuationOptions">연속 작업이 예약되는 시기와 연속 작업이 동작하는 방식에 대한 옵션입니다.여기에는 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />와 같은 조건과 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" /> 등의 실행 옵션이 포함됩니다.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0},System.Object},System.Object,System.Threading.Tasks.TaskScheduler)">
      <summary>대상 <see cref="T:System.Threading.Tasks.Task`1" />이 완료될 때 실행되는 연속 작업을 만듭니다.</summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task" />입니다.</returns>
      <param name="continuationAction">
        <see cref="T:System.Threading.Tasks.Task`1" />이 완료될 때 실행할 동작입니다.실행되는 경우 대리자에게 완료된 작업과 호출자 제공 상태 개체가 인수로 전달됩니다.</param>
      <param name="state">연속 동작에서 사용할 데이터를 나타내는 개체입니다.</param>
      <param name="scheduler">연속 작업과 연결하고 연속 작업의 실행에 사용할 <see cref="T:System.Threading.Tasks.TaskScheduler" />입니다.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},``0})">
      <summary>대상 <see cref="T:System.Threading.Tasks.Task`1" />가 완료될 때 비동기적으로 실행되는 연속 작업을 만듭니다.</summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</returns>
      <param name="continuationFunction">
        <see cref="T:System.Threading.Tasks.Task`1" />이 완료될 때 실행할 함수입니다.실행될 때 이 대리자에는 완료된 작업이 인수로 전달됩니다.</param>
      <typeparam name="TNewResult"> 연속 작업에서 생성되는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},``0},System.Threading.CancellationToken)">
      <summary>대상 <see cref="T:System.Threading.Tasks.Task`1" />가 완료될 때 비동기적으로 실행되는 연속 작업을 만듭니다.</summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</returns>
      <param name="continuationFunction">
        <see cref="T:System.Threading.Tasks.Task`1" />이 완료될 때 실행할 함수입니다.실행될 때 이 대리자에는 완료된 작업이 인수로 전달됩니다.</param>
      <param name="cancellationToken">새 작업에 할당할 <see cref="T:System.Threading.CancellationToken" />입니다.</param>
      <typeparam name="TNewResult"> 연속 작업에서 생성되는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},``0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>
        <paramref name="continuationOptions" />에 지정된 조건에 따라 실행되는 연속 작업을 만듭니다.</summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</returns>
      <param name="continuationFunction">
        <paramref name="continuationOptions" />에 지정된 조건에 따라 실행할 함수입니다.실행될 때 이 대리자에는 완료된 작업이 인수로 전달됩니다.</param>
      <param name="cancellationToken">새 작업에 할당할 <see cref="T:System.Threading.CancellationToken" />입니다.</param>
      <param name="continuationOptions">연속 작업이 예약되는 시기와 연속 작업이 동작하는 방식에 대한 옵션입니다.여기에는 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />와 같은 조건과 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" /> 등의 실행 옵션이 포함됩니다.</param>
      <param name="scheduler">연속 작업과 연결하고 연속 작업의 실행에 사용할 <see cref="T:System.Threading.Tasks.TaskScheduler" />입니다.</param>
      <typeparam name="TNewResult"> 연속 작업에서 생성되는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},``0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>
        <paramref name="continuationOptions" />에 지정된 조건에 따라 실행되는 연속 작업을 만듭니다.</summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</returns>
      <param name="continuationFunction">
        <paramref name="continuationOptions" />에 지정된 조건에 따라 실행할 함수입니다.실행될 때 이 대리자에는 완료된 작업이 인수로 전달됩니다.</param>
      <param name="continuationOptions">연속 작업이 예약되는 시기와 연속 작업이 동작하는 방식에 대한 옵션입니다.여기에는 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />와 같은 조건과 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" /> 등의 실행 옵션이 포함됩니다.</param>
      <typeparam name="TNewResult"> 연속 작업에서 생성되는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},``0},System.Threading.Tasks.TaskScheduler)">
      <summary>대상 <see cref="T:System.Threading.Tasks.Task`1" />가 완료될 때 비동기적으로 실행되는 연속 작업을 만듭니다.</summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</returns>
      <param name="continuationFunction">
        <see cref="T:System.Threading.Tasks.Task`1" />이 완료될 때 실행할 함수입니다.실행될 때 이 대리자에는 완료된 작업이 인수로 전달됩니다.</param>
      <param name="scheduler">연속 작업과 연결하고 연속 작업의 실행에 사용할 <see cref="T:System.Threading.Tasks.TaskScheduler" />입니다.</param>
      <typeparam name="TNewResult"> 연속 작업에서 생성되는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},System.Object,``0},System.Object)">
      <summary>대상 <see cref="T:System.Threading.Tasks.Task`1" />이 완료될 때 실행되는 연속 작업을 만듭니다.</summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</returns>
      <param name="continuationFunction">
        <see cref="T:System.Threading.Tasks.Task`1" />이 완료될 때 실행할 함수입니다.실행되는 경우 대리자에게 완료된 작업과 호출자 제공 상태 개체가 인수로 전달됩니다.</param>
      <param name="state">연속 함수에서 사용할 데이터를 나타내는 개체입니다.</param>
      <typeparam name="TNewResult">연속 작업에서 생성되는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},System.Object,``0},System.Object,System.Threading.CancellationToken)">
      <summary>대상 <see cref="T:System.Threading.Tasks.Task`1" />이 완료될 때 실행되는 연속 작업을 만듭니다.</summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</returns>
      <param name="continuationFunction">
        <see cref="T:System.Threading.Tasks.Task`1" />이 완료될 때 실행할 함수입니다.실행되는 경우 대리자에게 완료된 작업과 호출자 제공 상태 개체가 인수로 전달됩니다.</param>
      <param name="state">연속 함수에서 사용할 데이터를 나타내는 개체입니다.</param>
      <param name="cancellationToken">새 작업에 할당할 <see cref="T:System.Threading.CancellationToken" />입니다.</param>
      <typeparam name="TNewResult">연속 작업에서 생성되는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},System.Object,``0},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>대상 <see cref="T:System.Threading.Tasks.Task`1" />이 완료될 때 실행되는 연속 작업을 만듭니다.</summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</returns>
      <param name="continuationFunction">
        <see cref="T:System.Threading.Tasks.Task`1" />이 완료될 때 실행할 함수입니다.실행되는 경우 대리자에게 완료된 작업과 호출자 제공 상태 개체가 인수로 전달됩니다.</param>
      <param name="state">연속 함수에서 사용할 데이터를 나타내는 개체입니다.</param>
      <param name="cancellationToken">새 작업에 할당할 <see cref="T:System.Threading.CancellationToken" />입니다.</param>
      <param name="continuationOptions">연속 작업이 예약되는 시기와 연속 작업이 동작하는 방식에 대한 옵션입니다.여기에는 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />와 같은 조건과 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" /> 등의 실행 옵션이 포함됩니다.</param>
      <param name="scheduler">연속 작업과 연결하고 연속 작업의 실행에 사용할 <see cref="T:System.Threading.Tasks.TaskScheduler" />입니다.</param>
      <typeparam name="TNewResult">연속 작업에서 생성되는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The  <paramref name="continuationOptions" />  argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},System.Object,``0},System.Object,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>대상 <see cref="T:System.Threading.Tasks.Task`1" />이 완료될 때 실행되는 연속 작업을 만듭니다.</summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</returns>
      <param name="continuationFunction">
        <see cref="T:System.Threading.Tasks.Task`1" />이 완료될 때 실행할 함수입니다.실행되는 경우 대리자에게 완료된 작업과 호출자 제공 상태 개체가 인수로 전달됩니다.</param>
      <param name="state">연속 함수에서 사용할 데이터를 나타내는 개체입니다.</param>
      <param name="continuationOptions">연속 작업이 예약되는 시기와 연속 작업이 동작하는 방식에 대한 옵션입니다.여기에는 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />와 같은 조건과 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" /> 등의 실행 옵션이 포함됩니다.</param>
      <typeparam name="TNewResult">연속 작업에서 생성되는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},System.Object,``0},System.Object,System.Threading.Tasks.TaskScheduler)">
      <summary>대상 <see cref="T:System.Threading.Tasks.Task`1" />이 완료될 때 실행되는 연속 작업을 만듭니다.</summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</returns>
      <param name="continuationFunction">
        <see cref="T:System.Threading.Tasks.Task`1" />이 완료될 때 실행할 함수입니다.실행되는 경우 대리자에게 완료된 작업과 호출자 제공 상태 개체가 인수로 전달됩니다.</param>
      <param name="state">연속 함수에서 사용할 데이터를 나타내는 개체입니다.</param>
      <param name="scheduler">연속 작업과 연결하고 연속 작업의 실행에 사용할 <see cref="T:System.Threading.Tasks.TaskScheduler" />입니다.</param>
      <typeparam name="TNewResult">연속 작업에서 생성되는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="P:System.Threading.Tasks.Task`1.Factory">
      <summary>
        <see cref="T:System.Threading.Tasks.Task`1" /> 인스턴스를 만들고 구성하는 팩터리 메서드에 대한 액세스를 제공합니다.</summary>
      <returns>다양한 <see cref="T:System.Threading.Tasks.Task`1" /> 개체를 만들 수 있는 팩터리 개체입니다.</returns>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.GetAwaiter">
      <summary>이 <see cref="T:System.Threading.Tasks.Task`1" />를 기다리는 데 사용되는 awaiter를 가져옵니다.</summary>
      <returns>awaiter 인스턴스입니다.</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task`1.Result">
      <summary>이 <see cref="T:System.Threading.Tasks.Task`1" />의 결과 값을 가져옵니다.</summary>
      <returns>작업의 형식 매개 변수와 동일한 형식인 이 <see cref="T:System.Threading.Tasks.Task`1" />의 결과 값입니다.</returns>
      <exception cref="T:System.AggregateException">The task was canceled.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object.-or-An exception was thrown during the execution of the task.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions.</exception>
    </member>
    <member name="T:System.Threading.Tasks.TaskCanceledException">
      <summary>작업 취소를 알리기 위해 사용되는 예외를 나타냅니다.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskCanceledException.#ctor">
      <summary>오류를 설명하는 시스템 제공 메시지를 사용하여 <see cref="T:System.Threading.Tasks.TaskCanceledException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskCanceledException.#ctor(System.String)">
      <summary>오류를 설명하는 지정된 메시지를 사용하여 <see cref="T:System.Threading.Tasks.TaskCanceledException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">예외를 설명하는 메시지입니다.이 생성자의 호출자는 이 문자열이 현재 시스템 문화권에 맞게 지역화되었는지 확인하는 데 필요합니다.</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskCanceledException.#ctor(System.String,System.Exception)">
      <summary>지정된 오류 메시지와 해당 예외의 근본 원인인 내부 예외에 대한 참조를 사용하여 <see cref="T:System.Threading.Tasks.TaskCanceledException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">예외를 설명하는 메시지입니다.이 생성자의 호출자는 이 문자열이 현재 시스템 문화권에 맞게 지역화되었는지 확인하는 데 필요합니다.</param>
      <param name="innerException">현재 예외의 원인이 되는 예외입니다.<paramref name="innerException" /> 매개 변수가 null이 아니면 현재 예외는 내부 예외를 처리하는 catch 블록에서 발생합니다.</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskCanceledException.#ctor(System.Threading.Tasks.Task)">
      <summary>취소된 <see cref="T:System.Threading.Tasks.Task" />에 대한 참조를 사용하여 <see cref="T:System.Threading.Tasks.TaskCanceledException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="task">취소된 작업입니다.</param>
    </member>
    <member name="P:System.Threading.Tasks.TaskCanceledException.Task">
      <summary>이 예외와 연결된 작업을 가져옵니다.</summary>
      <returns>이 예외와 연결된 <see cref="T:System.Threading.Tasks.Task" />에 대한 참조입니다.</returns>
    </member>
    <member name="T:System.Threading.Tasks.TaskCompletionSource`1">
      <summary>대리자에 바인딩되지 않은 <see cref="T:System.Threading.Tasks.Task`1" />의 생산자 측면을 나타내고 <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" /> 속성을 통해 소비자 측면에 대한 액세스를 제공합니다.</summary>
      <typeparam name="TResult">이 <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" />에 연결된 결과 값의 형식입니다.</typeparam>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.#ctor">
      <summary>
        <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" />을 만듭니다.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.#ctor(System.Object)">
      <summary>지정된 상태를 사용하여 <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" />을 만듭니다.</summary>
      <param name="state">내부 <see cref="T:System.Threading.Tasks.Task`1" />의 AsyncState로 사용할 상태입니다.</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.#ctor(System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>지정된 상태 및 옵션을 사용하여 <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" />을 만듭니다.</summary>
      <param name="state">내부 <see cref="T:System.Threading.Tasks.Task`1" />의 AsyncState로 사용할 상태입니다.</param>
      <param name="creationOptions">내부 <see cref="T:System.Threading.Tasks.Task`1" />를 만들 때 사용할 옵션입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" />는 <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" />에 사용하기에 유효하지 않은 옵션을 나타냅니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.#ctor(System.Threading.Tasks.TaskCreationOptions)">
      <summary>지정된 옵션을 사용하여 <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" />을 만듭니다.</summary>
      <param name="creationOptions">내부 <see cref="T:System.Threading.Tasks.Task`1" />를 만들 때 사용할 옵션입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" />는 <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" />에 사용하기에 유효하지 않은 옵션을 나타냅니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.SetCanceled">
      <summary>내부 <see cref="T:System.Threading.Tasks.Task`1" />를 <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" /> 상태로 전환합니다.</summary>
      <exception cref="T:System.InvalidOperationException">기본 <see cref="T:System.Threading.Tasks.Task`1" />는 이미 <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />, <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> 또는 <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" /> 등 세 가지 최종 상태 중 하나에 있거나 기본 <see cref="T:System.Threading.Tasks.Task`1" />가 이미 삭제되었습니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.SetException(System.Collections.Generic.IEnumerable{System.Exception})">
      <summary>내부 <see cref="T:System.Threading.Tasks.Task`1" />를 <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> 상태로 전환합니다.</summary>
      <param name="exceptions">이 <see cref="T:System.Threading.Tasks.Task`1" />에 바인딩할 예외 컬렉션입니다.</param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" />가 삭제되었습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="exceptions" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="exceptions" />에 null 요소가 하나 이상 있습니다.</exception>
      <exception cref="T:System.InvalidOperationException">기본 <see cref="T:System.Threading.Tasks.Task`1" />는 이미 <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />, <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> 또는 <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />의 세 가지 최종 상태 중 하나에 있습니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.SetException(System.Exception)">
      <summary>내부 <see cref="T:System.Threading.Tasks.Task`1" />를 <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> 상태로 전환합니다.</summary>
      <param name="exception">이 <see cref="T:System.Threading.Tasks.Task`1" />에 바인딩할 예외입니다.</param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" />가 삭제되었습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="exception" /> 인수가 null인 경우</exception>
      <exception cref="T:System.InvalidOperationException">기본 <see cref="T:System.Threading.Tasks.Task`1" />는 이미 <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />, <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> 또는 <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />의 세 가지 최종 상태 중 하나에 있습니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.SetResult(`0)">
      <summary>내부 <see cref="T:System.Threading.Tasks.Task`1" />를 <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" /> 상태로 전환합니다.</summary>
      <param name="result">이 <see cref="T:System.Threading.Tasks.Task`1" />에 바인딩할 결과 값입니다.</param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" />가 삭제되었습니다.</exception>
      <exception cref="T:System.InvalidOperationException">기본 <see cref="T:System.Threading.Tasks.Task`1" />는 이미 <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />, <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> 또는 <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />의 세 가지 최종 상태 중 하나에 있습니다.</exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskCompletionSource`1.Task">
      <summary>이 <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" />에서 만든 <see cref="T:System.Threading.Tasks.Task`1" />를 가져옵니다.</summary>
      <returns>이 <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" />에서 만든 <see cref="T:System.Threading.Tasks.Task`1" />를 반환합니다.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.TrySetCanceled">
      <summary>내부 <see cref="T:System.Threading.Tasks.Task`1" />를 <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" /> 상태로 전환하려고 시도합니다.</summary>
      <returns>작업이 수행되었으면 true이고, 작업에 실패했거나 개체가 이미 삭제되었으면 false입니다.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.TrySetCanceled(System.Threading.CancellationToken)">
      <summary>내부 전환 하려고 시도 <see cref="T:System.Threading.Tasks.Task`1" /> 에 <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" /> 상태 이며 취소 작업에 저장 될 취소 토큰을 사용할 수 있습니다. </summary>
      <returns>작업에 성공하면 true이고, 그렇지 않으면 false입니다. </returns>
      <param name="cancellationToken">취소 토큰입니다. </param>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.TrySetException(System.Collections.Generic.IEnumerable{System.Exception})">
      <summary>내부 <see cref="T:System.Threading.Tasks.Task`1" />를 <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> 상태로 전환하려고 시도합니다.</summary>
      <returns>작업이 성공했으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="exceptions">이 <see cref="T:System.Threading.Tasks.Task`1" />에 바인딩할 예외 컬렉션입니다.</param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" />가 삭제되었습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="exceptions" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="exceptions" />에 null 요소가 하나 이상 있습니다.또는<paramref name="exceptions" /> 컬렉션이 비어 있습니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.TrySetException(System.Exception)">
      <summary>내부 <see cref="T:System.Threading.Tasks.Task`1" />를 <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> 상태로 전환하려고 시도합니다.</summary>
      <returns>작업이 성공했으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="exception">이 <see cref="T:System.Threading.Tasks.Task`1" />에 바인딩할 예외입니다.</param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" />가 삭제되었습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="exception" /> 인수가 null인 경우</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.TrySetResult(`0)">
      <summary>내부 <see cref="T:System.Threading.Tasks.Task`1" />를 <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" /> 상태로 전환하려고 시도합니다.</summary>
      <returns>작업이 성공했으면 true이고, 그렇지 않으면 false입니다. </returns>
      <param name="result">이 <see cref="T:System.Threading.Tasks.Task`1" />에 바인딩할 결과 값입니다.</param>
    </member>
    <member name="T:System.Threading.Tasks.TaskContinuationOptions">
      <summary>
        <see cref="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)" /> 또는 <see cref="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}},System.Threading.Tasks.TaskContinuationOptions)" /> 메서드를 사용하여 만드는 작업의 동작을 지정합니다.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.AttachedToParent">
      <summary>연속 작업이 자식 작업인 경우 작업 계층 구조의 부모에 연결되도록 지정합니다.연속 작업은 선행 작업도 자식 작업인 경우에만 자식 작업이 됩니다.기본적으로 자식 작업(즉, 외부 작업이 만든 내부 작업)은 부모와 독립적으로 실행됩니다.<see cref="F:System.Threading.Tasks.TaskContinuationOptions.AttachedToParent" /> 옵션을 사용할 수 있어 부모와 자식 작업은 동기화됩니다.부모 작업이 <see cref="F:System.Threading.Tasks.TaskCreationOptions.DenyChildAttach" /> 옵션을 사용하여 구성된 경우 자식 작업의 <see cref="F:System.Threading.Tasks.TaskCreationOptions.AttachedToParent" /> 옵션은 영향을 받지 않으며, 자식 작업은 분리된 자식 작업으로 실행됩니다. 자세한 내용은 연결된 자식 작업과 분리된 자식 작업을 참조하세요. </summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.DenyChildAttach">
      <summary>
        <see cref="F:System.Threading.Tasks.TaskCreationOptions.AttachedToParent" /> 옵션으로 만들어지고 연결된 자식 작업으로 실행하려는 모든 자식 작업(이 연속 작업으로 만들어진 모든 중첩 내부 작업)은 부모 작업에 연결할 수 없고 대신 분리된 자식 작업으로 실행되도록 지정합니다.자세한 내용은 연결된 자식 작업 및 분리된 자식 작업을 참조하십시오.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously">
      <summary>연속 작업이 동기적으로 실행되도록 지정합니다.이 옵션을 지정하면 연속 작업이 같은 스레드에서 실행되어 선행 작업이 최종 상태로 전환됩니다.연속 작업이 만들어질 때 선행 작업이 이미 완료된 경우 연속 작업은 해당 작업을 만드는 스레드에서 실행됩니다.선행 작업의 <see cref="T:System.Threading.CancellationTokenSource" />가 finally 블록 (Visual Basic의 Finally)에서 삭제된 경우 이 옵션이 지정되면 연속 작업이 해당 finally 블록에서 계속 실행됩니다.매우 단기로 실행되는 연속 작업만 동기적으로 실행됩니다.작업은 동기적으로 실행되므로, 호출 스레드가 작업이 완료되기를 기다리도록 하기 위해 <see cref="M:System.Threading.Tasks.Task.Wait" />와 같은 메서드를 호출할 필요가 없습니다. </summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.HideScheduler">
      <summary>
        <see cref="M:System.Threading.Tasks.Task.Run(System.Action)" /> 또는 <see cref="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task})" />와 같은 메서드를 호출함으로써 연속 작업에 의해 만들어진 작업이 이 연속 작업이 실행되는 스케줄러가 아닌 기본 스케줄러(<see cref="P:System.Threading.Tasks.TaskScheduler.Default" />)를 현재 스케줄러로 인식하도록 지정합니다.  </summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.LazyCancellation">
      <summary>연속 취소의 경우 선행이 완료되기 전에 연속이 완료되는 것을 방지합니다.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.LongRunning">
      <summary>연속 작업이 장기 실행되는 성긴 작업이 되도록 지정합니다.초과 구독을 보장할 수 있는 <see cref="T:System.Threading.Tasks.TaskScheduler" />에 대한 힌트를 제공합니다.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.None">
      <summary>연속 작업 옵션이 지정되지 않은 경우, 연속 작업을 실행할 때 기본 동작을 사용하도록 지정합니다.선행 작업의 최종 <see cref="P:System.Threading.Tasks.Task.Status" /> 속성 값에 관계없이 선행 작업이 완료되면 연속 작업이 비동기적으로 실행됩니다.연속 작업이 자식 작업인 경우 해당 작업은 분리된 상태의 중첩된 작업으로 만들어집니다.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.NotOnCanceled">
      <summary>선행 작업이 취소된 경우 연속 작업이 예약되지 않도록 지정합니다.완료 시 선행 작업의 <see cref="P:System.Threading.Tasks.Task.Status" /> 속성이 <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />이면 작업이 취소됩니다.이 옵션은 다중 작업 연속에 유효하지 않습니다.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.NotOnFaulted">
      <summary>선행 작업이 처리되지 않은 예외를 throw한 경우 연속 작업이 예약되지 않도록 지정합니다.완료 시 선행 작업의 <see cref="P:System.Threading.Tasks.Task.Status" /> 속성이 <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" />이면 처리되지 않은 예외가 발생합니다.이 옵션은 다중 작업 연속에 유효하지 않습니다.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.NotOnRanToCompletion">
      <summary>선행 작업의 실행이 완료된 경우 연속 작업이 예약되지 않도록 지정합니다.완료 시 선행 작업의 <see cref="P:System.Threading.Tasks.Task.Status" /> 속성이 <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />이면 작업이 끝까지 실행됩니다.이 옵션은 다중 작업 연속에 유효하지 않습니다.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled">
      <summary>선행 작업이 취소된 경우에만 연속 작업이 예약되도록 지정합니다.완료 시 선행 작업의 <see cref="P:System.Threading.Tasks.Task.Status" /> 속성이 <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />이면 작업이 취소됩니다.이 옵션은 다중 작업 연속에 유효하지 않습니다.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnFaulted">
      <summary>선행 작업이 처리되지 않은 예외를 throw한 경우에만 연속 작업이 예약되도록 지정합니다.완료 시 선행 작업의 <see cref="P:System.Threading.Tasks.Task.Status" /> 속성이 <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" />이면 처리되지 않은 예외가 발생합니다.<see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnFaulted" /> 옵션은 선행 작업의 <see cref="P:System.Threading.Tasks.Task.Exception" /> 속성이 null이(가) 아니도록 보장합니다.이 속성을 사용하여 예외를 catch하고 해당 작업에 오류를 발생시킨 예외를 확인할 수 있습니다.<see cref="P:System.Threading.Tasks.Task.Exception" /> 속성에 액세스하지 않는 경우에는 예외가 처리되지 않습니다.또한 취소되었거나 오류가 발생한 작업의 <see cref="P:System.Threading.Tasks.Task`1.Result" /> 속성에 액세스하려고 하면 새 예외가 발생합니다.이 옵션은 다중 작업 연속에 유효하지 않습니다. </summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnRanToCompletion">
      <summary>선행 작업의 실행이 완료된 경우에만 연속 작업이 예약되도록 지정합니다.완료 시 선행 작업의 <see cref="P:System.Threading.Tasks.Task.Status" /> 속성이 <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />이면 작업이 끝까지 실행됩니다.이 옵션은 다중 작업 연속에 유효하지 않습니다.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.PreferFairness">
      <summary>더 일찍 예약된 작업을 먼저 실행하고 나중에 예약된 작업을 나중에 실행할 수 있도록 예약된 순서대로 작업을 예약하도록 <see cref="T:System.Threading.Tasks.TaskScheduler" />에 알려줍니다. </summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.RunContinuationsAsynchronously">
      <summary>연속 작업이 비동기 방식으로 실행되도록 지정합니다.이 옵션은 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />보다 우선합니다.</summary>
    </member>
    <member name="T:System.Threading.Tasks.TaskCreationOptions">
      <summary>작업을 만들고 실행하기 위한 선택적 동작을 제어하는 플래그를 지정합니다. </summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.AttachedToParent">
      <summary>작업이 작업 계층 구조의 부모에 연결되도록 지정합니다.기본적으로 자식 작업(즉, 외부 작업이 만든 내부 작업)은 부모와 독립적으로 실행됩니다.<see cref="F:System.Threading.Tasks.TaskContinuationOptions.AttachedToParent" /> 옵션을 사용할 수 있어 부모와 자식 작업은 동기화됩니다.부모 작업이 <see cref="F:System.Threading.Tasks.TaskCreationOptions.DenyChildAttach" /> 옵션을 사용하여 구성된 경우 자식 작업의 <see cref="F:System.Threading.Tasks.TaskCreationOptions.AttachedToParent" /> 옵션은 영향을 받지 않으며, 자식 작업은 분리된 자식 작업으로 실행됩니다. 자세한 내용은 연결된 자식 작업과 분리된 자식 작업을 참조하세요. </summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.DenyChildAttach">
      <summary>연결된 자식 작업으로 실행하려는 자식 작업(즉 <see cref="F:System.Threading.Tasks.TaskCreationOptions.AttachedToParent" /> 옵션으로 만듦)은 부모 작업에 연결할 수 없고 대신 분리된 자식 작업으로 실행되도록 지정합니다.자세한 내용은 연결된 자식 작업 및 분리된 자식 작업을 참조하세요.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.HideScheduler">
      <summary>주변 스케줄러가 만든 작업에서 현재 스케줄러로 나타나지 않도록 합니다.이는 만들어진 작업에서 수행되는 StartNew 또는 ContinueWith와 같은 작업이 <see cref="P:System.Threading.Tasks.TaskScheduler.Default" />를 현재 스케줄러로 표시함을 의미합니다.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.LongRunning">
      <summary>세분화된 시스템보다 적고 폭 넓은 구성 요소가 개입하는 장기 실행되고 정교하지 않은 작업이 되도록 지정합니다.초과 구독을 보장할 수 있는 <see cref="T:System.Threading.Tasks.TaskScheduler" />에 대한 힌트를 제공합니다.초과 구독을 사용하면 사용 가능한 하드웨어 수보다 많은 스레드를 만들 수 있습니다.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.None">
      <summary>기본 동작이 사용되도록 지정합니다.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.PreferFairness">
      <summary>가능한 한 공정한 방식, 즉 일찍 예약된 작업은 일찍 실행되고 나중에 예약된 작업은 나중에 실행될 수 있는 방식으로 작업을 예약하는 <see cref="T:System.Threading.Tasks.TaskScheduler" />에 대한 힌트입니다.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.RunContinuationsAsynchronously">
      <summary>비동기 방식으로 실행될 현재 작업에 추가된 연속을 적용합니다. </summary>
    </member>
    <member name="T:System.Threading.Tasks.TaskExtensions">
      <summary>특정 유형의 <see cref="T:System.Threading.Tasks.Task" /> 인스턴스로 작업하기 위한 정적(Visual Basic의 경우 Shared) 메서드 집합을 제공합니다.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskExtensions.Unwrap``1(System.Threading.Tasks.Task{System.Threading.Tasks.Task{``0}})">
      <summary>Task&lt;Task&lt;T&gt;&gt;(C#) 또는 Task (Of Task(Of T))(Visual Basic)의 비동기 작업을 나타내는 프록시 <see cref="T:System.Threading.Tasks.Task" />를 만듭니다.</summary>
      <returns>제공된 Task&lt;Task&lt;T&gt;&gt;(C#) 또는 Task (Of Task(Of T))(Visual Basic)의 비동기 작업을 나타내는 <see cref="T:System.Threading.Tasks.Task" />입니다.</returns>
      <param name="task">래핑을 해제할 Task&lt;Task&lt;T&gt;&gt;(C#) 또는 Task (Of Task(Of T))(Visual Basic)입니다.</param>
      <typeparam name="TResult">작업 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="task" /> 인수가 null인 경우 throw되는 예외입니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskExtensions.Unwrap(System.Threading.Tasks.Task{System.Threading.Tasks.Task})">
      <summary>
        <see cref="M:System.Threading.Tasks.TaskScheduler.TryExecuteTaskInline(System.Threading.Tasks.Task,System.Boolean)" />의 비동기 작업을 나타내는 프록시 <see cref="T:System.Threading.Tasks.Task" />를 만듭니다.</summary>
      <returns>제공된 System.Threading.Tasks.Task(Of Task)의 비동기 작업을 나타내는 작업입니다.</returns>
      <param name="task">래핑을 해제할 Task&lt;Task&gt;(C#) 또는 Task (Of Task)(Visual Basic)입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="task" /> 인수가 null인 경우 throw되는 예외입니다.</exception>
    </member>
    <member name="T:System.Threading.Tasks.TaskFactory">
      <summary>
        <see cref="T:System.Threading.Tasks.Task" /> 개체를 만들고 예약하도록 지원합니다. </summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.#ctor">
      <summary>기본 구성을 사용하여 <see cref="T:System.Threading.Tasks.TaskFactory" /> 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.#ctor(System.Threading.CancellationToken)">
      <summary>지정된 구성을 사용하여 <see cref="T:System.Threading.Tasks.TaskFactory" /> 인스턴스를 초기화합니다.</summary>
      <param name="cancellationToken">팩터리 메서드를 호출할 때 다른 CancellationToken을 명시적으로 지정하지 않는 경우 이 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />에서 만든 작업에 할당할 <see cref="T:System.Threading.Tasks.TaskFactory" />입니다.</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.#ctor(System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>지정된 구성을 사용하여 <see cref="T:System.Threading.Tasks.TaskFactory" /> 인스턴스를 초기화합니다.</summary>
      <param name="cancellationToken">팩터리 메서드를 호출할 때 다른 CancellationToken을 명시적으로 지정하지 않는 경우 이 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />에서 만든 작업에 할당할 기본 <see cref="T:System.Threading.Tasks.TaskFactory" />입니다.</param>
      <param name="creationOptions">이 TaskFactory를 사용하여 작업을 만들 때 사용할 기본 <see cref="T:System.Threading.Tasks.TaskCreationOptions" />입니다.</param>
      <param name="continuationOptions">이 TaskFactory를 사용하여 연속 작업을 만들 때 사용할 기본 <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />입니다.</param>
      <param name="scheduler">이 TaskFactory를 사용하여 만든 작업을 예약하는 데 사용할 기본 <see cref="T:System.Threading.Tasks.TaskScheduler" />입니다.null 값은 TaskScheduler.Current를 사용함을 나타냅니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 인수에서 잘못된 <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> 값을 지정합니다.자세한 내용은 설명 부분을 참조 하십시오. <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />합니다.또는<paramref name="continuationOptions" /> 인수에서 잘못된 값을 지정합니다.  </exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.#ctor(System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>지정된 구성을 사용하여 <see cref="T:System.Threading.Tasks.TaskFactory" /> 인스턴스를 초기화합니다.</summary>
      <param name="creationOptions">이 TaskFactory를 사용하여 작업을 만들 때 사용할 기본 <see cref="T:System.Threading.Tasks.TaskCreationOptions" />입니다.</param>
      <param name="continuationOptions">이 TaskFactory를 사용하여 연속 작업을 만들 때 사용할 기본 <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 인수에서 잘못된 <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> 값을 지정합니다.자세한 내용은 설명 부분을 참조 하십시오. <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />합니다.또는<paramref name="continuationOptions" /> 인수에서 잘못된 값을 지정합니다.  </exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.#ctor(System.Threading.Tasks.TaskScheduler)">
      <summary>지정된 구성을 사용하여 <see cref="T:System.Threading.Tasks.TaskFactory" /> 인스턴스를 초기화합니다.</summary>
      <param name="scheduler">이 TaskFactory를 사용하여 만든 작업을 예약하는 데 사용할 <see cref="T:System.Threading.Tasks.TaskScheduler" />입니다.null 값은 현재 TaskScheduler를 사용함을 나타냅니다.</param>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory.CancellationToken">
      <summary>이 작업 팩터리에 대한 기본 취소 토큰을 가져옵니다.</summary>
      <returns>이 작업 팩터리에 대한 기본 작업 취소 토큰입니다.</returns>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory.ContinuationOptions">
      <summary>이 작업 팩터리에 대한 기본 작업 연속 옵션을 가져옵니다.</summary>
      <returns>이 작업 팩터리에 대한 기본 작업 연속 옵션입니다.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task[]})">
      <summary>지정된 일련의 작업을 완료하면 시작되는 연속 작업을 만듭니다. </summary>
      <returns>새 연속 작업입니다.</returns>
      <param name="tasks">계속할 작업의 배열입니다.</param>
      <param name="continuationAction">
        <paramref name="tasks" /> 배열의 모든 작업이 완료되었을 때 실행할 동작 대리자입니다.</param>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 배열에 있는 요소 하나가 삭제되었습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 배열은 null입니다.또는<paramref name="continuationAction" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 배열에 null 값이 있거나 비어 있습니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task[]},System.Threading.CancellationToken)">
      <summary>지정된 일련의 작업을 완료하면 시작되는 연속 작업을 만듭니다.</summary>
      <returns>새 연속 작업입니다.</returns>
      <param name="tasks">계속할 작업의 배열입니다.</param>
      <param name="continuationAction">
        <paramref name="tasks" /> 배열의 모든 작업이 완료되었을 때 실행할 동작 대리자입니다.</param>
      <param name="cancellationToken">새 연속 작업에 할당할 취소 토큰입니다.</param>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 배열에 있는 요소 하나가 삭제되었습니다.또는<see cref="T:System.Threading.CancellationTokenSource" />을 만든 <paramref name="cancellationToken" />가 이미 삭제되었습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 배열은 null입니다.또는<paramref name="continuationAction" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 배열에 null 값이 있거나 비어 있습니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task[]},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>지정된 일련의 작업을 완료하면 시작되는 연속 작업을 만듭니다.</summary>
      <returns>새 연속 작업입니다.</returns>
      <param name="tasks">계속할 작업의 배열입니다.</param>
      <param name="continuationAction">
        <paramref name="tasks" /> 배열의 모든 작업이 완료되었을 때 실행할 동작 대리자입니다.</param>
      <param name="cancellationToken">새 연속 작업에 할당할 취소 토큰입니다.</param>
      <param name="continuationOptions">새 연속 작업 동작을 제어하는 열거형 값의 비트 조합입니다.</param>
      <param name="scheduler">새 연속 작업을 예약하는 데 사용할 개체입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 배열은 null입니다.또는<paramref name="continuationAction" /> 인수가 null인 경우또는<paramref name="scheduler" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 배열에 null 값이 있거나 비어 있습니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task[]},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>지정된 일련의 작업을 완료하면 시작되는 연속 작업을 만듭니다.</summary>
      <returns>새 연속 작업입니다.</returns>
      <param name="tasks">계속할 작업의 배열입니다.</param>
      <param name="continuationAction">
        <paramref name="tasks" /> 배열의 모든 작업이 완료되었을 때 실행할 동작 대리자입니다.</param>
      <param name="continuationOptions">새 연속 작업 동작을 제어하는 열거형 값의 비트 조합입니다.NotOn* 및 OnlyOn* 멤버는 지원되지 않습니다.</param>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 배열에 있는 요소 하나가 삭제되었습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 배열은 null입니다.또는<paramref name="continuationAction" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 인수에서 잘못된 값을 지정합니다. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 배열에 null 값이 있거나 비어 있습니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],``0})">
      <summary>지정된 일련의 작업을 완료하면 시작되는 연속 작업을 만듭니다.</summary>
      <returns>새 연속 작업입니다.</returns>
      <param name="tasks">계속할 작업의 배열입니다.</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 배열의 모든 작업이 완료되었을 때 비동기로 실행할 함수 대리자입니다.</param>
      <typeparam name="TResult">
        <paramref name="continuationFunction" /> 대리자가 반환하고 만들어진 작업과 연결되는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 배열에 있는 요소 하나가 삭제되었습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 배열은 null입니다.또는<paramref name="continuationFunction" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 배열에 null 값이 있거나 비어 있습니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],``0},System.Threading.CancellationToken)">
      <summary>지정된 일련의 작업을 완료하면 시작되는 연속 작업을 만듭니다.</summary>
      <returns>새 연속 작업입니다.</returns>
      <param name="tasks">계속할 작업의 배열입니다.</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 배열의 모든 작업이 완료되었을 때 비동기로 실행할 함수 대리자입니다.</param>
      <param name="cancellationToken">새 연속 작업에 할당할 취소 토큰입니다.</param>
      <typeparam name="TResult">
        <paramref name="continuationFunction" /> 대리자가 반환하고 만들어진 작업과 연결되는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 배열에 있는 요소 하나가 삭제되었습니다.또는<see cref="T:System.Threading.CancellationTokenSource" />을 만든 <paramref name="cancellationToken" />가 이미 삭제되었습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 배열은 null입니다.또는<paramref name="continuationFunction" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 배열에 null 값이 있거나 비어 있습니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],``0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>지정된 일련의 작업을 완료하면 시작되는 연속 작업을 만듭니다.</summary>
      <returns>새 연속 작업입니다.</returns>
      <param name="tasks">계속할 작업의 배열입니다.</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 배열의 모든 작업이 완료되었을 때 비동기로 실행할 함수 대리자입니다.</param>
      <param name="cancellationToken">새 연속 작업에 할당할 취소 토큰입니다.</param>
      <param name="continuationOptions">새 연속 작업 동작을 제어하는 열거형 값의 비트 조합입니다.NotOn* 및 OnlyOn* 멤버는 지원되지 않습니다.</param>
      <param name="scheduler">새 연속 작업을 예약하는 데 사용할 개체입니다.</param>
      <typeparam name="TResult">
        <paramref name="continuationFunction" /> 대리자가 반환하고 만들어진 작업과 연결되는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 배열은 null입니다.또는<paramref name="continuationFunction" /> 인수가 null인 경우또는<paramref name="scheduler" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 배열에 null 값이 있거나 비어 있습니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],``0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>지정된 일련의 작업을 완료하면 시작되는 연속 작업을 만듭니다.</summary>
      <returns>새 연속 작업입니다.</returns>
      <param name="tasks">계속할 작업의 배열입니다.</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 배열의 모든 작업이 완료되었을 때 비동기로 실행할 함수 대리자입니다.</param>
      <param name="continuationOptions">새 연속 작업 동작을 제어하는 열거형 값의 비트 조합입니다.NotOn* 및 OnlyOn* 멤버는 지원되지 않습니다.</param>
      <typeparam name="TResult">
        <paramref name="continuationFunction" /> 대리자가 반환하고 만들어진 작업과 연결되는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 배열에 있는 요소 하나가 삭제되었습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 배열은 null입니다.또는<paramref name="continuationFunction" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 인수에서 잘못된 값을 지정합니다. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 배열에 null 값이 있거나 비어 있습니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}[]})">
      <summary>지정된 일련의 작업을 완료하면 시작되는 연속 작업을 만듭니다.</summary>
      <returns>새 연속 작업입니다.</returns>
      <param name="tasks">계속할 작업의 배열입니다.</param>
      <param name="continuationAction">
        <paramref name="tasks" /> 배열의 모든 작업이 완료되었을 때 실행할 동작 대리자입니다.</param>
      <typeparam name="TAntecedentResult">선행 <paramref name="tasks" />의 결과 형식입니다.</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 배열에 있는 요소 하나가 삭제되었습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 배열은 null입니다.또는<paramref name="continuationAction" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 배열에 null 값이 있거나 비어 있습니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}[]},System.Threading.CancellationToken)">
      <summary>지정된 일련의 작업을 완료하면 시작되는 연속 작업을 만듭니다.</summary>
      <returns>새 연속 작업입니다.</returns>
      <param name="tasks">계속할 작업의 배열입니다.</param>
      <param name="continuationAction">
        <paramref name="tasks" /> 배열의 모든 작업이 완료되었을 때 실행할 동작 대리자입니다.</param>
      <param name="cancellationToken">새 연속 작업에 할당할 취소 토큰입니다.</param>
      <typeparam name="TAntecedentResult">선행 <paramref name="tasks" />의 결과 형식입니다.</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 배열에 있는 요소 하나가 삭제되었습니다.또는<see cref="T:System.Threading.CancellationTokenSource" />을 만든 <paramref name="cancellationToken" />가 이미 삭제되었습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 배열은 null입니다.또는<paramref name="continuationAction" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 배열에 null 값이 있거나 비어 있습니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}[]},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>지정된 일련의 작업을 완료하면 시작되는 연속 작업을 만듭니다.</summary>
      <returns>새 연속 작업입니다.</returns>
      <param name="tasks">계속할 작업의 배열입니다.</param>
      <param name="continuationAction">
        <paramref name="tasks" /> 배열의 모든 작업이 완료되었을 때 실행할 동작 대리자입니다.</param>
      <param name="cancellationToken">새 연속 작업에 할당할 취소 토큰입니다.</param>
      <param name="continuationOptions">새 연속 작업 동작을 제어하는 열거형 값의 비트 조합입니다.NotOn* 및 OnlyOn* 멤버는 지원되지 않습니다.</param>
      <param name="scheduler">새 연속 작업을 예약하는 데 사용할 개체입니다.</param>
      <typeparam name="TAntecedentResult">선행 <paramref name="tasks" />의 결과 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 배열은 null입니다.또는<paramref name="continuationAction" /> 인수가 null인 경우또는<paramref name="scheduler" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 배열에 null 값이 있거나 비어 있습니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}[]},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>지정된 일련의 작업을 완료하면 시작되는 연속 작업을 만듭니다.</summary>
      <returns>새 연속 작업입니다.</returns>
      <param name="tasks">계속할 작업의 배열입니다.</param>
      <param name="continuationAction">
        <paramref name="tasks" /> 배열의 모든 작업이 완료되었을 때 실행할 동작 대리자입니다.</param>
      <param name="continuationOptions">새 연속 작업 동작을 제어하는 열거형 값의 비트 조합입니다.NotOn* 및 OnlyOn* 멤버는 지원되지 않습니다.</param>
      <typeparam name="TAntecedentResult">선행 <paramref name="tasks" />의 결과 형식입니다.</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 배열에 있는 요소 하나가 삭제되었습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 배열은 null입니다.또는<paramref name="continuationAction" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 인수에서 잘못된 값을 지정합니다. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 배열에 null 값이 있거나 비어 있습니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],``1})">
      <summary>지정된 일련의 작업을 완료하면 시작되는 연속 작업을 만듭니다.</summary>
      <returns>새 연속 작업입니다.</returns>
      <param name="tasks">계속할 작업의 배열입니다.</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 배열의 모든 작업이 완료되었을 때 비동기로 실행할 함수 대리자입니다.</param>
      <typeparam name="TAntecedentResult">선행 <paramref name="tasks" />의 결과 형식입니다.</typeparam>
      <typeparam name="TResult">
        <paramref name="continuationFunction" /> 대리자가 반환하고 만들어진 작업과 연결되는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 배열에 있는 요소 하나가 삭제되었습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 배열은 null입니다.또는<paramref name="continuationFunction" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 배열에 null 값이 있거나 비어 있습니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],``1},System.Threading.CancellationToken)">
      <summary>지정된 일련의 작업을 완료하면 시작되는 연속 작업을 만듭니다.</summary>
      <returns>새 연속 작업입니다.</returns>
      <param name="tasks">계속할 작업의 배열입니다.</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 배열의 모든 작업이 완료되었을 때 비동기로 실행할 함수 대리자입니다.</param>
      <param name="cancellationToken">새 연속 작업에 할당할 취소 토큰입니다.</param>
      <typeparam name="TAntecedentResult">선행 <paramref name="tasks" />의 결과 형식입니다.</typeparam>
      <typeparam name="TResult">
        <paramref name="continuationFunction" /> 대리자가 반환하고 만들어진 작업과 연결되는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 배열에 있는 요소 하나가 삭제되었습니다.또는<see cref="T:System.Threading.CancellationTokenSource" /> 만든<paramref name=" cancellationToken" /> 이미 삭제 되었습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 배열은 null입니다.또는<paramref name="continuationFunction" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 배열에 null 값이 있거나 비어 있습니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],``1},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>지정된 일련의 작업을 완료하면 시작되는 연속 작업을 만듭니다.</summary>
      <returns>새 연속 작업입니다.</returns>
      <param name="tasks">계속할 작업의 배열입니다.</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 배열의 모든 작업이 완료되었을 때 비동기로 실행할 함수 대리자입니다.</param>
      <param name="cancellationToken">새 연속 작업에 할당할 취소 토큰입니다.</param>
      <param name="continuationOptions">새 연속 작업 동작을 제어하는 열거형 값의 비트 조합입니다.NotOn* 및 OnlyOn* 멤버는 지원되지 않습니다.</param>
      <param name="scheduler">새 연속 작업을 예약하는 데 사용할 개체입니다.</param>
      <typeparam name="TAntecedentResult">선행 <paramref name="tasks" />의 결과 형식입니다.</typeparam>
      <typeparam name="TResult">
        <paramref name="continuationFunction" /> 대리자가 반환하고 만들어진 작업과 연결되는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 배열은 null입니다.또는<paramref name="continuationFunction" /> 인수가 null인 경우또는<paramref name="scheduler" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 배열에 null 값이 있거나 비어 있습니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 인수에서 잘못된 값을 지정합니다. </exception>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 배열에 있는 요소 하나가 삭제되었습니다.또는<see cref="T:System.Threading.CancellationTokenSource" />을 만든 <paramref name="cancellationToken" />가 이미 삭제되었습니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],``1},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>지정된 일련의 작업을 완료하면 시작되는 연속 작업을 만듭니다.</summary>
      <returns>새 연속 작업입니다.</returns>
      <param name="tasks">계속할 작업의 배열입니다.</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 배열의 모든 작업이 완료되었을 때 비동기로 실행할 함수 대리자입니다.</param>
      <param name="continuationOptions">새 연속 작업 동작을 제어하는 열거형 값의 비트 조합입니다.NotOn* 및 OnlyOn* 멤버는 지원되지 않습니다.</param>
      <typeparam name="TAntecedentResult">선행 <paramref name="tasks" />의 결과 형식입니다.</typeparam>
      <typeparam name="TResult">
        <paramref name="continuationFunction" /> 대리자가 반환하고 만들어진 작업과 연결되는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 배열에 있는 요소 하나가 삭제되었습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 배열은 null입니다.또는<paramref name="continuationFunction" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 인수에서 잘못된 값을 지정합니다.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 배열에 null 값이 있거나 비어 있습니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task})">
      <summary>제공된 집합에서 작업이 완료될 때 시작할 연속 <see cref="T:System.Threading.Tasks.Task" />를 만듭니다.</summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task" />입니다.</returns>
      <param name="tasks">작업이 완료될 때 계속할 작업의 배열입니다.</param>
      <param name="continuationAction">
        <paramref name="tasks" /> 배열에서 작업이 완료될 때 실행할 동작 대리자입니다.</param>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 배열에 있는 요소 중 하나가 삭제되었습니다. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 배열은 null입니다. 또는여는 <paramref name="continuationAction" /> 인수가 null합니다. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 배열에 포함 된 null 값입니다. 또는<paramref name="tasks" /> 배열이 비어 있습니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task},System.Threading.CancellationToken)">
      <summary>제공된 집합에서 작업이 완료될 때 시작할 연속 <see cref="T:System.Threading.Tasks.Task" />를 만듭니다.</summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task" />입니다.</returns>
      <param name="tasks">작업이 완료될 때 계속할 작업의 배열입니다.</param>
      <param name="continuationAction">
        <paramref name="tasks" /> 배열에서 작업이 완료될 때 실행할 동작 대리자입니다.</param>
      <param name="cancellationToken">새 연속 작업에 할당할 <see cref="T:System.Threading.CancellationToken" />입니다.</param>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 배열에 있는 요소 중 하나가 삭제되었습니다. 또는<paramref name="cancellationToken" /> 이미 삭제 되었습니다. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 배열은 null입니다. 또는<paramref name="continuationAction" /> 인수가 null인 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 배열에 포함 된 null 값입니다. 또는<paramref name="tasks" /> 배열이 비어 있습니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>제공된 집합에서 작업이 완료될 때 시작할 연속 <see cref="T:System.Threading.Tasks.Task" />를 만듭니다.</summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task" />입니다.</returns>
      <param name="tasks">작업이 완료될 때 계속할 작업의 배열입니다.</param>
      <param name="continuationAction">
        <paramref name="tasks" /> 배열에서 작업이 완료될 때 실행할 동작 대리자입니다.</param>
      <param name="cancellationToken">새 연속 작업에 할당할 <see cref="T:System.Threading.CancellationToken" />입니다.</param>
      <param name="continuationOptions">만들어진 연속 <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />의 동작을 제어하는 <see cref="T:System.Threading.Tasks.Task" /> 값입니다.</param>
      <param name="scheduler">만들어진 연속 <see cref="T:System.Threading.Tasks.TaskScheduler" />를 예약하는 데 사용할 <see cref="T:System.Threading.Tasks.Task" />입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 배열이 null인 경우 throw되는 예외입니다.또는<paramref name="continuationAction" /> 인수가 null일 때 throw되는 예외입니다.또는<paramref name="scheduler" /> 인수가 null일 때 throw되는 예외입니다.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 배열에 Null 값이 있을 때 throw되는 예외입니다.또는<paramref name="tasks" /> 배열이 비어 있을 때 throw되는 예외입니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>제공된 집합에서 작업이 완료될 때 시작할 연속 <see cref="T:System.Threading.Tasks.Task" />를 만듭니다.</summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task" />입니다.</returns>
      <param name="tasks">작업이 완료될 때 계속할 작업의 배열입니다.</param>
      <param name="continuationAction">
        <paramref name="tasks" /> 배열에서 작업이 완료될 때 실행할 동작 대리자입니다.</param>
      <param name="continuationOptions">만들어진 연속 <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />의 동작을 제어하는 <see cref="T:System.Threading.Tasks.Task" /> 값입니다.</param>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 배열의 요소 중 하나가 삭제된 경우 throw되는 예외입니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 배열이 null인 경우 throw되는 예외입니다.또는<paramref name="continuationAction" /> 인수가 null일 때 throw되는 예외입니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 인수가 유효하지 않은 TaskContinuationOptions 값을 지정할 때 throw되는 예외입니다.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 배열에 Null 값이 있을 때 throw되는 예외입니다.또는<paramref name="tasks" /> 배열이 비어 있을 때 throw되는 예외입니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,``0})">
      <summary>제공된 집합에서 작업이 완료될 때 시작할 연속 <see cref="T:System.Threading.Tasks.Task`1" />를 만듭니다.</summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</returns>
      <param name="tasks">작업이 완료될 때 계속할 작업의 배열입니다.</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 배열에서 작업이 완료될 때 비동기로 실행할 함수 대리자입니다.</param>
      <typeparam name="TResult">
        <paramref name="continuationFunction" /> 대리자가 반환하고 만들어진 <see cref="T:System.Threading.Tasks.Task`1" />와 연결되는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 배열의 요소 중 하나가 삭제된 경우 throw되는 예외입니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 배열이 null인 경우 throw되는 예외입니다.또는<paramref name="continuationFunction" /> 인수가 null일 때 throw되는 예외입니다.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 배열에 Null 값이 있을 때 throw되는 예외입니다.또는<paramref name="tasks" /> 배열이 비어 있을 때 throw되는 예외입니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,``0},System.Threading.CancellationToken)">
      <summary>제공된 집합에서 작업이 완료될 때 시작할 연속 <see cref="T:System.Threading.Tasks.Task`1" />를 만듭니다.</summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</returns>
      <param name="tasks">작업이 완료될 때 계속할 작업의 배열입니다.</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 배열에서 작업이 완료될 때 비동기로 실행할 함수 대리자입니다.</param>
      <param name="cancellationToken">새 연속 작업에 할당할 <see cref="T:System.Threading.CancellationToken" />입니다.</param>
      <typeparam name="TResult">
        <paramref name="continuationFunction" /> 대리자가 반환하고 만들어진 <see cref="T:System.Threading.Tasks.Task`1" />와 연결되는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 배열의 요소 중 하나가 삭제된 경우 throw되는 예외입니다.또는제공된 <see cref="T:System.Threading.CancellationToken" />가 이미 삭제된 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 배열이 null인 경우 throw되는 예외입니다.또는<paramref name="continuationFunction" /> 인수가 null일 때 throw되는 예외입니다.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 배열에 Null 값이 있을 때 throw되는 예외입니다.또는<paramref name="tasks" /> 배열이 비어 있을 때 throw되는 예외입니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,``0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>제공된 집합에서 작업이 완료될 때 시작할 연속 <see cref="T:System.Threading.Tasks.Task`1" />를 만듭니다.</summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</returns>
      <param name="tasks">작업이 완료될 때 계속할 작업의 배열입니다.</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 배열에서 작업이 완료될 때 비동기로 실행할 함수 대리자입니다.</param>
      <param name="cancellationToken">새 연속 작업에 할당할 <see cref="T:System.Threading.CancellationToken" />입니다.</param>
      <param name="continuationOptions">만들어진 연속 <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />의 동작을 제어하는 <see cref="T:System.Threading.Tasks.Task`1" /> 값입니다.</param>
      <param name="scheduler">만들어진 연속 <see cref="T:System.Threading.Tasks.TaskScheduler" />를 예약하는 데 사용할 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</param>
      <typeparam name="TResult">
        <paramref name="continuationFunction" /> 대리자가 반환하고 만들어진 <see cref="T:System.Threading.Tasks.Task`1" />와 연결되는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 배열이 null인 경우 throw되는 예외입니다.또는<paramref name="continuationFunction" /> 인수가 null일 때 throw되는 예외입니다.또는<paramref name="scheduler" /> 인수가 null일 때 throw되는 예외입니다.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 배열에 Null 값이 있을 때 throw되는 예외입니다.또는<paramref name="tasks" /> 배열이 비어 있을 때 throw되는 예외입니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,``0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>제공된 집합에서 작업이 완료될 때 시작할 연속 <see cref="T:System.Threading.Tasks.Task`1" />를 만듭니다.</summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</returns>
      <param name="tasks">작업이 완료될 때 계속할 작업의 배열입니다.</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 배열에서 작업이 완료될 때 비동기로 실행할 함수 대리자입니다.</param>
      <param name="continuationOptions">만들어진 연속 <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />의 동작을 제어하는 <see cref="T:System.Threading.Tasks.Task`1" /> 값입니다.</param>
      <typeparam name="TResult">
        <paramref name="continuationFunction" /> 대리자가 반환하고 만들어진 <see cref="T:System.Threading.Tasks.Task`1" />와 연결되는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 배열의 요소 중 하나가 삭제된 경우 throw되는 예외입니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 배열이 null인 경우 throw되는 예외입니다.또는<paramref name="continuationFunction" /> 인수가 null일 때 throw되는 예외입니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 인수가 유효하지 않은 TaskContinuationOptions 값을 지정할 때 throw되는 예외입니다.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 배열에 Null 값이 있을 때 throw되는 예외입니다.또는<paramref name="tasks" /> 배열이 비어 있을 때 throw되는 예외입니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}})">
      <summary>제공된 집합에서 작업이 완료될 때 시작할 연속 <see cref="T:System.Threading.Tasks.Task" />를 만듭니다.</summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task" />입니다.</returns>
      <param name="tasks">작업이 완료될 때 계속할 작업의 배열입니다.</param>
      <param name="continuationAction">
        <paramref name="tasks" /> 배열에서 작업이 완료될 때 실행할 동작 대리자입니다.</param>
      <typeparam name="TAntecedentResult">선행 <paramref name="tasks" />의 결과 형식입니다.</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 배열의 요소 중 하나가 삭제된 경우 throw되는 예외입니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 배열이 null인 경우 throw되는 예외입니다.또는<paramref name="continuationAction" /> 인수가 null일 때 throw되는 예외입니다.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 배열에 Null 값이 있을 때 throw되는 예외입니다.또는<paramref name="tasks" /> 배열이 비어 있을 때 throw되는 예외입니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}},System.Threading.CancellationToken)">
      <summary>제공된 집합에서 작업이 완료될 때 시작할 연속 <see cref="T:System.Threading.Tasks.Task" />를 만듭니다.</summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task" />입니다.</returns>
      <param name="tasks">작업이 완료될 때 계속할 작업의 배열입니다.</param>
      <param name="continuationAction">
        <paramref name="tasks" /> 배열에서 작업이 완료될 때 실행할 동작 대리자입니다.</param>
      <param name="cancellationToken">새 연속 작업에 할당할 <see cref="T:System.Threading.CancellationToken" />입니다.</param>
      <typeparam name="TAntecedentResult">선행 <paramref name="tasks" />의 결과 형식입니다.</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 배열의 요소 중 하나가 삭제된 경우 throw되는 예외입니다.또는제공된 <see cref="T:System.Threading.CancellationToken" />가 이미 삭제된 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 배열이 null인 경우 throw되는 예외입니다.또는<paramref name="continuationAction" /> 인수가 null일 때 throw되는 예외입니다.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 배열에 Null 값이 있을 때 throw되는 예외입니다.또는<paramref name="tasks" /> 배열이 비어 있을 때 throw되는 예외입니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>제공된 집합에서 작업이 완료될 때 시작할 연속 <see cref="T:System.Threading.Tasks.Task" />를 만듭니다.</summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task" />입니다.</returns>
      <param name="tasks">작업이 완료될 때 계속할 작업의 배열입니다.</param>
      <param name="continuationAction">
        <paramref name="tasks" /> 배열에서 작업이 완료될 때 실행할 동작 대리자입니다.</param>
      <param name="cancellationToken">새 연속 작업에 할당할 <see cref="T:System.Threading.CancellationToken" />입니다.</param>
      <param name="continuationOptions">만들어진 연속 <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />의 동작을 제어하는 <see cref="T:System.Threading.Tasks.Task" /> 값입니다.</param>
      <param name="scheduler">만들어진 연속 <see cref="T:System.Threading.Tasks.TaskScheduler" />를 예약하는 데 사용할 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</param>
      <typeparam name="TAntecedentResult">선행 <paramref name="tasks" />의 결과 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 배열이 null인 경우 throw되는 예외입니다.또는<paramref name="continuationAction" /> 인수가 null일 때 throw되는 예외입니다.또는<paramref name="scheduler" /> 인수가 null일 때 throw되는 예외입니다.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 배열에 Null 값이 있을 때 throw되는 예외입니다.또는<paramref name="tasks" /> 배열이 비어 있을 때 throw되는 예외입니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>제공된 집합에서 작업이 완료될 때 시작할 연속 <see cref="T:System.Threading.Tasks.Task" />를 만듭니다.</summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task" />입니다.</returns>
      <param name="tasks">작업이 완료될 때 계속할 작업의 배열입니다.</param>
      <param name="continuationAction">
        <paramref name="tasks" /> 배열에서 작업이 완료될 때 실행할 동작 대리자입니다.</param>
      <param name="continuationOptions">만들어진 연속 <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />의 동작을 제어하는 <see cref="T:System.Threading.Tasks.Task" /> 값입니다.</param>
      <typeparam name="TAntecedentResult">선행 <paramref name="tasks" />의 결과 형식입니다.</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 배열의 요소 중 하나가 삭제된 경우 throw되는 예외입니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 배열이 null인 경우 throw되는 예외입니다.또는<paramref name="continuationAction" /> 인수가 null일 때 throw되는 예외입니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 인수가 유효하지 않은 TaskContinuationOptions 값을 지정할 때 throw되는 예외입니다.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 배열에 Null 값이 있을 때 throw되는 예외입니다.또는<paramref name="tasks" /> 배열이 비어 있을 때 throw되는 예외입니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},``1})">
      <summary>제공된 집합에서 작업이 완료될 때 시작할 연속 <see cref="T:System.Threading.Tasks.Task`1" />를 만듭니다.</summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</returns>
      <param name="tasks">작업이 완료될 때 계속할 작업의 배열입니다.</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 배열에서 작업이 완료될 때 비동기로 실행할 함수 대리자입니다.</param>
      <typeparam name="TAntecedentResult">선행 <paramref name="tasks" />의 결과 형식입니다.</typeparam>
      <typeparam name="TResult">
        <paramref name="continuationFunction" /> 대리자가 반환하고 만들어진 <see cref="T:System.Threading.Tasks.Task`1" />와 연결되는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 배열의 요소 중 하나가 삭제된 경우 throw되는 예외입니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 배열이 null인 경우 throw되는 예외입니다.또는<paramref name="continuationFunction" /> 인수가 null일 때 throw되는 예외입니다.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 배열에 Null 값이 있을 때 throw되는 예외입니다.또는<paramref name="tasks" /> 배열이 비어 있을 때 throw되는 예외입니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},``1},System.Threading.CancellationToken)">
      <summary>제공된 집합에서 작업이 완료될 때 시작할 연속 <see cref="T:System.Threading.Tasks.Task`1" />를 만듭니다.</summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</returns>
      <param name="tasks">작업이 완료될 때 계속할 작업의 배열입니다.</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 배열에서 작업이 완료될 때 비동기로 실행할 함수 대리자입니다.</param>
      <param name="cancellationToken">새 연속 작업에 할당할 <see cref="T:System.Threading.CancellationToken" />입니다.</param>
      <typeparam name="TAntecedentResult">선행 <paramref name="tasks" />의 결과 형식입니다.</typeparam>
      <typeparam name="TResult">
        <paramref name="continuationFunction" /> 대리자가 반환하고 만들어진 <see cref="T:System.Threading.Tasks.Task`1" />와 연결되는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 배열의 요소 중 하나가 삭제된 경우 throw되는 예외입니다.또는제공된 <see cref="T:System.Threading.CancellationToken" />가 이미 삭제된 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 배열이 null인 경우 throw되는 예외입니다.또는<paramref name="continuationFunction" /> 인수가 null일 때 throw되는 예외입니다.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 배열에 Null 값이 있을 때 throw되는 예외입니다.또는<paramref name="tasks" /> 배열이 비어 있을 때 throw되는 예외입니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},``1},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>제공된 집합에서 작업이 완료될 때 시작할 연속 <see cref="T:System.Threading.Tasks.Task`1" />를 만듭니다.</summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</returns>
      <param name="tasks">작업이 완료될 때 계속할 작업의 배열입니다.</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 배열에서 작업이 완료될 때 비동기로 실행할 함수 대리자입니다.</param>
      <param name="cancellationToken">새 연속 작업에 할당할 <see cref="T:System.Threading.CancellationToken" />입니다.</param>
      <param name="continuationOptions">만들어진 연속 <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />의 동작을 제어하는 <see cref="T:System.Threading.Tasks.Task`1" /> 값입니다.</param>
      <param name="scheduler">만들어진 연속 <see cref="T:System.Threading.Tasks.TaskScheduler" />를 예약하는 데 사용할 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</param>
      <typeparam name="TAntecedentResult">선행 <paramref name="tasks" />의 결과 형식입니다.</typeparam>
      <typeparam name="TResult">
        <paramref name="continuationFunction" /> 대리자가 반환하고 만들어진 <see cref="T:System.Threading.Tasks.Task`1" />와 연결되는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 배열이 null인 경우 throw되는 예외입니다.또는<paramref name="continuationFunction" /> 인수가 null일 때 throw되는 예외입니다.또는<paramref name="scheduler" /> 인수가 null일 때 throw되는 예외입니다.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 배열에 Null 값이 있을 때 throw되는 예외입니다.또는<paramref name="tasks" /> 배열이 비어 있을 때 throw되는 예외입니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},``1},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>제공된 집합에서 작업이 완료될 때 시작할 연속 <see cref="T:System.Threading.Tasks.Task`1" />를 만듭니다.</summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</returns>
      <param name="tasks">작업이 완료될 때 계속할 작업의 배열입니다.</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 배열에서 작업이 완료될 때 비동기로 실행할 함수 대리자입니다.</param>
      <param name="continuationOptions">만들어진 연속 <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />의 동작을 제어하는 <see cref="T:System.Threading.Tasks.Task`1" /> 값입니다.</param>
      <typeparam name="TAntecedentResult">선행 <paramref name="tasks" />의 결과 형식입니다.</typeparam>
      <typeparam name="TResult">
        <paramref name="continuationFunction" /> 대리자가 반환하고 만들어진 <see cref="T:System.Threading.Tasks.Task`1" />와 연결되는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 배열의 요소 중 하나가 삭제된 경우 throw되는 예외입니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 배열이 null인 경우 throw되는 예외입니다.또는<paramref name="continuationFunction" /> 인수가 null일 때 throw되는 예외입니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 인수가 유효하지 않은 TaskContinuationOptions 값을 지정할 때 throw되는 예외입니다.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 배열에 Null 값이 있을 때 throw되는 예외입니다.또는<paramref name="tasks" /> 배열이 비어 있을 때 throw되는 예외입니다.</exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory.CreationOptions">
      <summary>이 작업 팩터리에 대한 기본 작업 만들기 옵션을 가져옵니다.</summary>
      <returns>이 작업 팩터리에 대한 기본 작업 만들기 옵션입니다.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object)">
      <summary>비동기 프로그래밍 모델 패턴을 따르는 Begin 및 End 메서드 쌍을 나타내는 <see cref="T:System.Threading.Tasks.Task" />를 만듭니다.</summary>
      <returns>비동기 작업을 나타내는 만들어진 <see cref="T:System.Threading.Tasks.Task" />입니다.</returns>
      <param name="beginMethod">비동기 작업을 시작하는 대리자입니다.</param>
      <param name="endMethod">비동기 작업을 종료하는 대리자입니다.</param>
      <param name="state">
        <paramref name="beginMethod" /> 대리자가 사용할 데이터가 포함된 개체입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 인수가 null일 때 throw되는 예외입니다.또는<paramref name="endMethod" /> 인수가 null일 때 throw되는 예외입니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>비동기 프로그래밍 모델 패턴을 따르는 Begin 및 End 메서드 쌍을 나타내는 <see cref="T:System.Threading.Tasks.Task" />를 만듭니다.</summary>
      <returns>비동기 작업을 나타내는 만들어진 <see cref="T:System.Threading.Tasks.Task" />입니다.</returns>
      <param name="beginMethod">비동기 작업을 시작하는 대리자입니다.</param>
      <param name="endMethod">비동기 작업을 종료하는 대리자입니다.</param>
      <param name="state">
        <paramref name="beginMethod" /> 대리자가 사용할 데이터가 포함된 개체입니다.</param>
      <param name="creationOptions">만들어진 <see cref="T:System.Threading.Tasks.Task" />의 동작을 제어하는 TaskCreationOptions 값입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 인수가 null일 때 throw되는 예외입니다.또는<paramref name="endMethod" /> 인수가 null일 때 throw되는 예외입니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 인수가 잘못된 TaskCreationOptions 값을 지정하는 경우 throw되는 예외입니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``0},System.Object)">
      <summary>비동기 프로그래밍 모델 패턴을 따르는 Begin 및 End 메서드 쌍을 나타내는 <see cref="T:System.Threading.Tasks.Task`1" />를 만듭니다.</summary>
      <returns>비동기 작업을 나타내는 만들어진 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</returns>
      <param name="beginMethod">비동기 작업을 시작하는 대리자입니다.</param>
      <param name="endMethod">비동기 작업을 종료하는 대리자입니다.</param>
      <param name="state">
        <paramref name="beginMethod" /> 대리자가 사용할 데이터가 포함된 개체입니다.</param>
      <typeparam name="TResult">
        <see cref="T:System.Threading.Tasks.Task`1" />를 통해 사용할 수 있는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 인수가 null일 때 throw되는 예외입니다.또는<paramref name="endMethod" /> 인수가 null일 때 throw되는 예외입니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``0},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>비동기 프로그래밍 모델 패턴을 따르는 Begin 및 End 메서드 쌍을 나타내는 <see cref="T:System.Threading.Tasks.Task`1" />를 만듭니다.</summary>
      <returns>비동기 작업을 나타내는 만들어진 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</returns>
      <param name="beginMethod">비동기 작업을 시작하는 대리자입니다.</param>
      <param name="endMethod">비동기 작업을 종료하는 대리자입니다.</param>
      <param name="state">
        <paramref name="beginMethod" /> 대리자가 사용할 데이터가 포함된 개체입니다.</param>
      <param name="creationOptions">만들어진 <see cref="T:System.Threading.Tasks.Task`1" />의 동작을 제어하는 TaskCreationOptions 값입니다.</param>
      <typeparam name="TResult">
        <see cref="T:System.Threading.Tasks.Task`1" />를 통해 사용할 수 있는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 인수가 null일 때 throw되는 예외입니다.또는<paramref name="endMethod" /> 인수가 null일 때 throw되는 예외입니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 인수가 잘못된 TaskCreationOptions 값을 지정하는 경우 throw되는 예외입니다.<paramref name="creationOptions" /> 인수가 잘못된 TaskCreationOptions 값을 지정하는 경우 throw되는 예외입니다.자세한 내용은 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />에 대한 설명을 참조하십시오.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,System.Object)">
      <summary>비동기 프로그래밍 모델 패턴을 따르는 Begin 및 End 메서드 쌍을 나타내는 <see cref="T:System.Threading.Tasks.Task" />를 만듭니다.</summary>
      <returns>비동기 작업을 나타내는 만들어진 <see cref="T:System.Threading.Tasks.Task" />입니다.</returns>
      <param name="beginMethod">비동기 작업을 시작하는 대리자입니다.</param>
      <param name="endMethod">비동기 작업을 종료하는 대리자입니다.</param>
      <param name="arg1">
        <paramref name="beginMethod" /> 대리자에 전달되는 첫 번째 인수입니다.</param>
      <param name="state">
        <paramref name="beginMethod" /> 대리자가 사용할 데이터가 포함된 개체입니다.</param>
      <typeparam name="TArg1">
        <paramref name="beginMethod" /> 대리자에 전달되는 첫 번째 인수의 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 인수가 null일 때 throw되는 예외입니다.또는<paramref name="endMethod" /> 인수가 null일 때 throw되는 예외입니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>비동기 프로그래밍 모델 패턴을 따르는 Begin 및 End 메서드 쌍을 나타내는 <see cref="T:System.Threading.Tasks.Task" />를 만듭니다.</summary>
      <returns>비동기 작업을 나타내는 만들어진 <see cref="T:System.Threading.Tasks.Task" />입니다.</returns>
      <param name="beginMethod">비동기 작업을 시작하는 대리자입니다.</param>
      <param name="endMethod">비동기 작업을 종료하는 대리자입니다.</param>
      <param name="arg1">
        <paramref name="beginMethod" /> 대리자에 전달되는 첫 번째 인수입니다.</param>
      <param name="state">
        <paramref name="beginMethod" /> 대리자가 사용할 데이터가 포함된 개체입니다.</param>
      <param name="creationOptions">만들어진 <see cref="T:System.Threading.Tasks.Task" />의 동작을 제어하는 TaskCreationOptions 값입니다.</param>
      <typeparam name="TArg1">
        <paramref name="beginMethod" /> 대리자에 전달되는 첫 번째 인수의 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 인수가 null일 때 throw되는 예외입니다.또는<paramref name="endMethod" /> 인수가 null일 때 throw되는 예외입니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 인수가 잘못된 TaskCreationOptions 값을 지정하는 경우 throw되는 예외입니다.<paramref name="creationOptions" /> 인수가 잘못된 TaskCreationOptions 값을 지정하는 경우 throw되는 예외입니다.자세한 내용은 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />에 대한 설명을 참조하십시오.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``2(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``1},``0,System.Object)">
      <summary>비동기 프로그래밍 모델 패턴을 따르는 Begin 및 End 메서드 쌍을 나타내는 <see cref="T:System.Threading.Tasks.Task`1" />를 만듭니다.</summary>
      <returns>비동기 작업을 나타내는 만들어진 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</returns>
      <param name="beginMethod">비동기 작업을 시작하는 대리자입니다.</param>
      <param name="endMethod">비동기 작업을 종료하는 대리자입니다.</param>
      <param name="arg1">
        <paramref name="beginMethod" /> 대리자에 전달되는 첫 번째 인수입니다.</param>
      <param name="state">
        <paramref name="beginMethod" /> 대리자가 사용할 데이터가 포함된 개체입니다.</param>
      <typeparam name="TArg1">
        <paramref name="beginMethod" /> 대리자에 전달되는 첫 번째 인수의 형식입니다.</typeparam>
      <typeparam name="TResult">
        <see cref="T:System.Threading.Tasks.Task`1" />를 통해 사용할 수 있는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 인수가 null일 때 throw되는 예외입니다.또는<paramref name="endMethod" /> 인수가 null일 때 throw되는 예외입니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``2(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``1},``0,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>비동기 프로그래밍 모델 패턴을 따르는 Begin 및 End 메서드 쌍을 나타내는 <see cref="T:System.Threading.Tasks.Task`1" />를 만듭니다.</summary>
      <returns>비동기 작업을 나타내는 만들어진 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</returns>
      <param name="beginMethod">비동기 작업을 시작하는 대리자입니다.</param>
      <param name="endMethod">비동기 작업을 종료하는 대리자입니다.</param>
      <param name="arg1">
        <paramref name="beginMethod" /> 대리자에 전달되는 첫 번째 인수입니다.</param>
      <param name="state">
        <paramref name="beginMethod" /> 대리자가 사용할 데이터가 포함된 개체입니다.</param>
      <param name="creationOptions">만들어진 <see cref="T:System.Threading.Tasks.Task`1" />의 동작을 제어하는 TaskCreationOptions 값입니다.</param>
      <typeparam name="TArg1">
        <paramref name="beginMethod" /> 대리자에 전달되는 첫 번째 인수의 형식입니다.</typeparam>
      <typeparam name="TResult">
        <see cref="T:System.Threading.Tasks.Task`1" />를 통해 사용할 수 있는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 인수가 null일 때 throw되는 예외입니다.또는<paramref name="endMethod" /> 인수가 null일 때 throw되는 예외입니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 인수가 잘못된 TaskCreationOptions 값을 지정하는 경우 throw되는 예외입니다.<paramref name="creationOptions" /> 인수가 잘못된 TaskCreationOptions 값을 지정하는 경우 throw되는 예외입니다.자세한 내용은 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />에 대한 설명을 참조하십시오.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``2(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,``1,System.Object)">
      <summary>비동기 프로그래밍 모델 패턴을 따르는 Begin 및 End 메서드 쌍을 나타내는 <see cref="T:System.Threading.Tasks.Task" />를 만듭니다.</summary>
      <returns>비동기 작업을 나타내는 만들어진 <see cref="T:System.Threading.Tasks.Task" />입니다.</returns>
      <param name="beginMethod">비동기 작업을 시작하는 대리자입니다.</param>
      <param name="endMethod">비동기 작업을 종료하는 대리자입니다.</param>
      <param name="arg1">
        <paramref name="beginMethod" /> 대리자에 전달되는 첫 번째 인수입니다.</param>
      <param name="arg2">
        <paramref name="beginMethod" /> 대리자에 전달되는 두 번째 인수입니다.</param>
      <param name="state">
        <paramref name="beginMethod" /> 대리자가 사용할 데이터가 포함된 개체입니다.</param>
      <typeparam name="TArg1">
        <paramref name="beginMethod" /> 대리자에 전달되는 두 번째 인수의 형식입니다.</typeparam>
      <typeparam name="TArg2">
        <paramref name="beginMethod" /> 대리자에 전달되는 첫 번째 인수의 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 인수가 null일 때 throw되는 예외입니다.또는<paramref name="endMethod" /> 인수가 null일 때 throw되는 예외입니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``2(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,``1,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>비동기 프로그래밍 모델 패턴을 따르는 Begin 및 End 메서드 쌍을 나타내는 <see cref="T:System.Threading.Tasks.Task" />를 만듭니다.</summary>
      <returns>비동기 작업을 나타내는 만들어진 <see cref="T:System.Threading.Tasks.Task" />입니다.</returns>
      <param name="beginMethod">비동기 작업을 시작하는 대리자입니다.</param>
      <param name="endMethod">비동기 작업을 종료하는 대리자입니다.</param>
      <param name="arg1">
        <paramref name="beginMethod" /> 대리자에 전달되는 첫 번째 인수입니다.</param>
      <param name="arg2">
        <paramref name="beginMethod" /> 대리자에 전달되는 두 번째 인수입니다.</param>
      <param name="state">
        <paramref name="beginMethod" /> 대리자가 사용할 데이터가 포함된 개체입니다.</param>
      <param name="creationOptions">만들어진 <see cref="T:System.Threading.Tasks.Task" />의 동작을 제어하는 TaskCreationOptions 값입니다.</param>
      <typeparam name="TArg1">
        <paramref name="beginMethod" /> 대리자에 전달되는 두 번째 인수의 형식입니다.</typeparam>
      <typeparam name="TArg2">
        <paramref name="beginMethod" /> 대리자에 전달되는 첫 번째 인수의 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 인수가 null일 때 throw되는 예외입니다.또는<paramref name="endMethod" /> 인수가 null일 때 throw되는 예외입니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 인수가 잘못된 TaskCreationOptions 값을 지정하는 경우 throw되는 예외입니다.<paramref name="creationOptions" /> 인수가 잘못된 TaskCreationOptions 값을 지정하는 경우 throw되는 예외입니다.자세한 내용은 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />에 대한 설명을 참조하십시오.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``3(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``2},``0,``1,System.Object)">
      <summary>비동기 프로그래밍 모델 패턴을 따르는 Begin 및 End 메서드 쌍을 나타내는 <see cref="T:System.Threading.Tasks.Task`1" />를 만듭니다.</summary>
      <returns>비동기 작업을 나타내는 만들어진 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</returns>
      <param name="beginMethod">비동기 작업을 시작하는 대리자입니다.</param>
      <param name="endMethod">비동기 작업을 종료하는 대리자입니다.</param>
      <param name="arg1">
        <paramref name="beginMethod" /> 대리자에 전달되는 첫 번째 인수입니다.</param>
      <param name="arg2">
        <paramref name="beginMethod" /> 대리자에 전달되는 두 번째 인수입니다.</param>
      <param name="state">
        <paramref name="beginMethod" /> 대리자가 사용할 데이터가 포함된 개체입니다.</param>
      <typeparam name="TArg1">
        <paramref name="beginMethod" /> 대리자에 전달되는 두 번째 인수의 형식입니다.</typeparam>
      <typeparam name="TArg2">
        <paramref name="beginMethod" /> 대리자에 전달되는 첫 번째 인수의 형식입니다.</typeparam>
      <typeparam name="TResult">
        <see cref="T:System.Threading.Tasks.Task`1" />를 통해 사용할 수 있는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 인수가 null일 때 throw되는 예외입니다.또는<paramref name="endMethod" /> 인수가 null일 때 throw되는 예외입니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``3(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``2},``0,``1,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>비동기 프로그래밍 모델 패턴을 따르는 Begin 및 End 메서드 쌍을 나타내는 <see cref="T:System.Threading.Tasks.Task`1" />를 만듭니다.</summary>
      <returns>비동기 작업을 나타내는 만들어진 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</returns>
      <param name="beginMethod">비동기 작업을 시작하는 대리자입니다.</param>
      <param name="endMethod">비동기 작업을 종료하는 대리자입니다.</param>
      <param name="arg1">
        <paramref name="beginMethod" /> 대리자에 전달되는 첫 번째 인수입니다.</param>
      <param name="arg2">
        <paramref name="beginMethod" /> 대리자에 전달되는 두 번째 인수입니다.</param>
      <param name="state">
        <paramref name="beginMethod" /> 대리자가 사용할 데이터가 포함된 개체입니다.</param>
      <param name="creationOptions">만들어진 <see cref="T:System.Threading.Tasks.Task`1" />의 동작을 제어하는 TaskCreationOptions 값입니다.</param>
      <typeparam name="TArg1">
        <paramref name="beginMethod" /> 대리자에 전달되는 두 번째 인수의 형식입니다.</typeparam>
      <typeparam name="TArg2">
        <paramref name="beginMethod" /> 대리자에 전달되는 첫 번째 인수의 형식입니다.</typeparam>
      <typeparam name="TResult">
        <see cref="T:System.Threading.Tasks.Task`1" />를 통해 사용할 수 있는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 인수가 null일 때 throw되는 예외입니다.또는<paramref name="endMethod" /> 인수가 null일 때 throw되는 예외입니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 인수가 잘못된 TaskCreationOptions 값을 지정하는 경우 throw되는 예외입니다.<paramref name="creationOptions" /> 인수가 잘못된 TaskCreationOptions 값을 지정하는 경우 throw되는 예외입니다.자세한 내용은 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />에 대한 설명을 참조하십시오.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``3(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,``1,``2,System.Object)">
      <summary>비동기 프로그래밍 모델 패턴을 따르는 Begin 및 End 메서드 쌍을 나타내는 <see cref="T:System.Threading.Tasks.Task" />를 만듭니다.</summary>
      <returns>비동기 작업을 나타내는 만들어진 <see cref="T:System.Threading.Tasks.Task" />입니다.</returns>
      <param name="beginMethod">비동기 작업을 시작하는 대리자입니다.</param>
      <param name="endMethod">비동기 작업을 종료하는 대리자입니다.</param>
      <param name="arg1">
        <paramref name="beginMethod" /> 대리자에 전달되는 첫 번째 인수입니다.</param>
      <param name="arg2">
        <paramref name="beginMethod" /> 대리자에 전달되는 두 번째 인수입니다.</param>
      <param name="arg3">
        <paramref name="beginMethod" /> 대리자에 전달되는 세 번째 인수입니다.</param>
      <param name="state">
        <paramref name="beginMethod" /> 대리자가 사용할 데이터가 포함된 개체입니다.</param>
      <typeparam name="TArg1">
        <paramref name="beginMethod" /> 대리자에 전달되는 두 번째 인수의 형식입니다.</typeparam>
      <typeparam name="TArg2">
        <paramref name="beginMethod" /> 대리자에 전달되는 세 번째 인수의 형식입니다.</typeparam>
      <typeparam name="TArg3">
        <paramref name="beginMethod" /> 대리자에 전달되는 첫 번째 인수의 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 인수가 null일 때 throw되는 예외입니다.또는<paramref name="endMethod" /> 인수가 null일 때 throw되는 예외입니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``3(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,``1,``2,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>비동기 프로그래밍 모델 패턴을 따르는 Begin 및 End 메서드 쌍을 나타내는 <see cref="T:System.Threading.Tasks.Task" />를 만듭니다.</summary>
      <returns>비동기 작업을 나타내는 만들어진 <see cref="T:System.Threading.Tasks.Task" />입니다.</returns>
      <param name="beginMethod">비동기 작업을 시작하는 대리자입니다.</param>
      <param name="endMethod">비동기 작업을 종료하는 대리자입니다.</param>
      <param name="arg1">
        <paramref name="beginMethod" /> 대리자에 전달되는 첫 번째 인수입니다.</param>
      <param name="arg2">
        <paramref name="beginMethod" /> 대리자에 전달되는 두 번째 인수입니다.</param>
      <param name="arg3">
        <paramref name="beginMethod" /> 대리자에 전달되는 세 번째 인수입니다.</param>
      <param name="state">
        <paramref name="beginMethod" /> 대리자가 사용할 데이터가 포함된 개체입니다.</param>
      <param name="creationOptions">만들어진 <see cref="T:System.Threading.Tasks.Task" />의 동작을 제어하는 TaskCreationOptions 값입니다.</param>
      <typeparam name="TArg1">
        <paramref name="beginMethod" /> 대리자에 전달되는 두 번째 인수의 형식입니다.</typeparam>
      <typeparam name="TArg2">
        <paramref name="beginMethod" /> 대리자에 전달되는 세 번째 인수의 형식입니다.</typeparam>
      <typeparam name="TArg3">
        <paramref name="beginMethod" /> 대리자에 전달되는 첫 번째 인수의 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 인수가 null일 때 throw되는 예외입니다.또는<paramref name="endMethod" /> 인수가 null일 때 throw되는 예외입니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 인수가 잘못된 TaskCreationOptions 값을 지정하는 경우 throw되는 예외입니다.<paramref name="creationOptions" /> 인수가 잘못된 TaskCreationOptions 값을 지정하는 경우 throw되는 예외입니다.자세한 내용은 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />에 대한 설명을 참조하십시오.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``4(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``3},``0,``1,``2,System.Object)">
      <summary>비동기 프로그래밍 모델 패턴을 따르는 Begin 및 End 메서드 쌍을 나타내는 <see cref="T:System.Threading.Tasks.Task`1" />를 만듭니다.</summary>
      <returns>비동기 작업을 나타내는 만들어진 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</returns>
      <param name="beginMethod">비동기 작업을 시작하는 대리자입니다.</param>
      <param name="endMethod">비동기 작업을 종료하는 대리자입니다.</param>
      <param name="arg1">
        <paramref name="beginMethod" /> 대리자에 전달되는 첫 번째 인수입니다.</param>
      <param name="arg2">
        <paramref name="beginMethod" /> 대리자에 전달되는 두 번째 인수입니다.</param>
      <param name="arg3">
        <paramref name="beginMethod" /> 대리자에 전달되는 세 번째 인수입니다.</param>
      <param name="state">
        <paramref name="beginMethod" /> 대리자가 사용할 데이터가 포함된 개체입니다.</param>
      <typeparam name="TArg1">
        <paramref name="beginMethod" /> 대리자에 전달되는 두 번째 인수의 형식입니다.</typeparam>
      <typeparam name="TArg2">
        <paramref name="beginMethod" /> 대리자에 전달되는 세 번째 인수의 형식입니다.</typeparam>
      <typeparam name="TArg3">
        <paramref name="beginMethod" /> 대리자에 전달되는 첫 번째 인수의 형식입니다.</typeparam>
      <typeparam name="TResult">
        <see cref="T:System.Threading.Tasks.Task`1" />를 통해 사용할 수 있는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 인수가 null일 때 throw되는 예외입니다.또는<paramref name="endMethod" /> 인수가 null일 때 throw되는 예외입니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``4(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``3},``0,``1,``2,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>비동기 프로그래밍 모델 패턴을 따르는 Begin 및 End 메서드 쌍을 나타내는 <see cref="T:System.Threading.Tasks.Task`1" />를 만듭니다.</summary>
      <returns>비동기 작업을 나타내는 만들어진 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</returns>
      <param name="beginMethod">비동기 작업을 시작하는 대리자입니다.</param>
      <param name="endMethod">비동기 작업을 종료하는 대리자입니다.</param>
      <param name="arg1">
        <paramref name="beginMethod" /> 대리자에 전달되는 첫 번째 인수입니다.</param>
      <param name="arg2">
        <paramref name="beginMethod" /> 대리자에 전달되는 두 번째 인수입니다.</param>
      <param name="arg3">
        <paramref name="beginMethod" /> 대리자에 전달되는 세 번째 인수입니다.</param>
      <param name="state">
        <paramref name="beginMethod" /> 대리자가 사용할 데이터가 포함된 개체입니다.</param>
      <param name="creationOptions">만들어진 <see cref="T:System.Threading.Tasks.Task`1" />의 동작을 제어하는 TaskCreationOptions 값입니다.</param>
      <typeparam name="TArg1">
        <paramref name="beginMethod" /> 대리자에 전달되는 두 번째 인수의 형식입니다.</typeparam>
      <typeparam name="TArg2">
        <paramref name="beginMethod" /> 대리자에 전달되는 세 번째 인수의 형식입니다.</typeparam>
      <typeparam name="TArg3">
        <paramref name="beginMethod" /> 대리자에 전달되는 첫 번째 인수의 형식입니다.</typeparam>
      <typeparam name="TResult">
        <see cref="T:System.Threading.Tasks.Task`1" />를 통해 사용할 수 있는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 인수가 null일 때 throw되는 예외입니다.또는<paramref name="endMethod" /> 인수가 null일 때 throw되는 예외입니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 인수가 잘못된 TaskCreationOptions 값을 지정하는 경우 throw되는 예외입니다.<paramref name="creationOptions" /> 인수가 잘못된 TaskCreationOptions 값을 지정하는 경우 throw되는 예외입니다.자세한 내용은 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />에 대한 설명을 참조하십시오.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync(System.IAsyncResult,System.Action{System.IAsyncResult})">
      <summary>지정된 <see cref="T:System.Threading.Tasks.Task" />가 완료될 때 End 메서드 작업을 실행하는 <see cref="T:System.IAsyncResult" />를 만듭니다.</summary>
      <returns>비동기 작업을 나타내는 <see cref="T:System.Threading.Tasks.Task" />입니다.</returns>
      <param name="asyncResult">완료될 때 <paramref name="endMethod" />의 처리를 트리거하는 IAsyncResult입니다.</param>
      <param name="endMethod">완료된 <paramref name="asyncResult" />를 처리하는 동작 대리자입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> 인수가 null일 때 throw되는 예외입니다.또는<paramref name="endMethod" /> 인수가 null일 때 throw되는 예외입니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync(System.IAsyncResult,System.Action{System.IAsyncResult},System.Threading.Tasks.TaskCreationOptions)">
      <summary>지정된 <see cref="T:System.Threading.Tasks.Task" />가 완료될 때 End 메서드 작업을 실행하는 <see cref="T:System.IAsyncResult" />를 만듭니다.</summary>
      <returns>비동기 작업을 나타내는 <see cref="T:System.Threading.Tasks.Task" />입니다.</returns>
      <param name="asyncResult">완료될 때 <paramref name="endMethod" />의 처리를 트리거하는 IAsyncResult입니다.</param>
      <param name="endMethod">완료된 <paramref name="asyncResult" />를 처리하는 동작 대리자입니다.</param>
      <param name="creationOptions">만들어진 <see cref="T:System.Threading.Tasks.Task" />의 동작을 제어하는 TaskCreationOptions 값입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> 인수가 null일 때 throw되는 예외입니다.또는<paramref name="endMethod" /> 인수가 null일 때 throw되는 예외입니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 인수가 잘못된 TaskCreationOptions 값을 지정하는 경우 throw되는 예외입니다.자세한 내용은 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />에 대한 설명을 참조하십시오.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync(System.IAsyncResult,System.Action{System.IAsyncResult},System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>지정된 <see cref="T:System.Threading.Tasks.Task" />가 완료될 때 End 메서드 작업을 실행하는 <see cref="T:System.IAsyncResult" />를 만듭니다.</summary>
      <returns>비동기 작업을 나타내는 만들어진 <see cref="T:System.Threading.Tasks.Task" />입니다.</returns>
      <param name="asyncResult">완료될 때 <paramref name="endMethod" />의 처리를 트리거하는 IAsyncResult입니다.</param>
      <param name="endMethod">완료된 <paramref name="asyncResult" />를 처리하는 동작 대리자입니다.</param>
      <param name="creationOptions">만들어진 <see cref="T:System.Threading.Tasks.Task" />의 동작을 제어하는 TaskCreationOptions 값입니다.</param>
      <param name="scheduler">End 메서드를 실행하는 작업을 예약하는 데 사용할 <see cref="T:System.Threading.Tasks.TaskScheduler" />입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> 인수가 null일 때 throw되는 예외입니다.또는<paramref name="endMethod" /> 인수가 null일 때 throw되는 예외입니다.또는<paramref name="scheduler" /> 인수가 null일 때 throw되는 예외입니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 인수가 잘못된 TaskCreationOptions 값을 지정하는 경우 throw되는 예외입니다.<paramref name="creationOptions" /> 인수가 잘못된 TaskCreationOptions 값을 지정하는 경우 throw되는 예외입니다.자세한 내용은 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />에 대한 설명을 참조하십시오.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.IAsyncResult,System.Func{System.IAsyncResult,``0})">
      <summary>지정된 <see cref="T:System.Threading.Tasks.Task`1" />가 완료될 때 End 메서드 함수를 실행하는 <see cref="T:System.IAsyncResult" />를 만듭니다.</summary>
      <returns>비동기 작업을 나타내는 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</returns>
      <param name="asyncResult">완료될 때 <paramref name="endMethod" />의 처리를 트리거하는 IAsyncResult입니다.</param>
      <param name="endMethod">완료된 <paramref name="asyncResult" />를 처리하는 함수 대리자입니다.</param>
      <typeparam name="TResult">
        <see cref="T:System.Threading.Tasks.Task`1" />를 통해 사용할 수 있는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> 인수가 null일 때 throw되는 예외입니다.또는<paramref name="endMethod" /> 인수가 null일 때 throw되는 예외입니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.IAsyncResult,System.Func{System.IAsyncResult,``0},System.Threading.Tasks.TaskCreationOptions)">
      <summary>지정된 <see cref="T:System.Threading.Tasks.Task`1" />가 완료될 때 End 메서드 함수를 실행하는 <see cref="T:System.IAsyncResult" />를 만듭니다.</summary>
      <returns>비동기 작업을 나타내는 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</returns>
      <param name="asyncResult">완료될 때 <paramref name="endMethod" />의 처리를 트리거하는 IAsyncResult입니다.</param>
      <param name="endMethod">완료된 <paramref name="asyncResult" />를 처리하는 함수 대리자입니다.</param>
      <param name="creationOptions">만들어진 <see cref="T:System.Threading.Tasks.Task`1" />의 동작을 제어하는 TaskCreationOptions 값입니다.</param>
      <typeparam name="TResult">
        <see cref="T:System.Threading.Tasks.Task`1" />를 통해 사용할 수 있는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> 인수가 null일 때 throw되는 예외입니다.또는<paramref name="endMethod" /> 인수가 null일 때 throw되는 예외입니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 인수가 잘못된 TaskCreationOptions 값을 지정하는 경우 throw되는 예외입니다.<paramref name="creationOptions" /> 인수가 잘못된 TaskCreationOptions 값을 지정하는 경우 throw되는 예외입니다.자세한 내용은 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />에 대한 설명을 참조하십시오.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.IAsyncResult,System.Func{System.IAsyncResult,``0},System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>지정된 <see cref="T:System.Threading.Tasks.Task`1" />가 완료될 때 End 메서드 함수를 실행하는 <see cref="T:System.IAsyncResult" />를 만듭니다.</summary>
      <returns>비동기 작업을 나타내는 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</returns>
      <param name="asyncResult">완료될 때 <paramref name="endMethod" />의 처리를 트리거하는 IAsyncResult입니다.</param>
      <param name="endMethod">완료된 <paramref name="asyncResult" />를 처리하는 함수 대리자입니다.</param>
      <param name="creationOptions">만들어진 <see cref="T:System.Threading.Tasks.Task`1" />의 동작을 제어하는 TaskCreationOptions 값입니다.</param>
      <param name="scheduler">End 메서드를 실행하는 작업을 예약하는 데 사용할 <see cref="T:System.Threading.Tasks.TaskScheduler" />입니다.</param>
      <typeparam name="TResult">
        <see cref="T:System.Threading.Tasks.Task`1" />를 통해 사용할 수 있는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> 인수가 null일 때 throw되는 예외입니다.또는<paramref name="endMethod" /> 인수가 null일 때 throw되는 예외입니다.또는<paramref name="scheduler" /> 인수가 null일 때 throw되는 예외입니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 인수가 잘못된 TaskCreationOptions 값을 지정하는 경우 throw되는 예외입니다.<paramref name="creationOptions" /> 인수가 잘못된 TaskCreationOptions 값을 지정하는 경우 throw되는 예외입니다.자세한 내용은 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />에 대한 설명을 참조하십시오.</exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory.Scheduler">
      <summary>이 작업 팩터리에 대한 기본 작업 스케줄러를 가져옵니다.</summary>
      <returns>이 작업 팩터리에 대한 기본 작업 스케줄러입니다.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action)">
      <summary>작업을 만들고 시작합니다.</summary>
      <returns>시작된 작업입니다.</returns>
      <param name="action">비동기적으로 실행할 동작 대리자입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="action" /> 인수가 null인 경우</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action,System.Threading.CancellationToken)">
      <summary>
        <see cref="T:System.Threading.Tasks.Task" />를 만들고 시작합니다.</summary>
      <returns>시작된 <see cref="T:System.Threading.Tasks.Task" />입니다.</returns>
      <param name="action">비동기적으로 실행할 동작 대리자입니다.</param>
      <param name="cancellationToken">새 작업에 할당할 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />입니다.</param>
      <exception cref="T:System.ObjectDisposedException">제공된 <see cref="T:System.Threading.CancellationToken" />가 이미 삭제된 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="action" /> 인수가 null일 때 throw되는 예외입니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>
        <see cref="T:System.Threading.Tasks.Task" />를 만들고 시작합니다.</summary>
      <returns>시작된 <see cref="T:System.Threading.Tasks.Task" />입니다.</returns>
      <param name="action">비동기적으로 실행할 동작 대리자입니다.</param>
      <param name="cancellationToken">새 <see cref="T:System.Threading.Tasks.Task" />에 할당할 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />입니다.</param>
      <param name="creationOptions">만들어진 <see cref="T:System.Threading.Tasks.Task" />의 동작을 제어하는 TaskCreationOptions 값입니다.</param>
      <param name="scheduler">만들어진 <see cref="T:System.Threading.Tasks.TaskScheduler" />를 예약하는 데 사용할 <see cref="T:System.Threading.Tasks.Task" />입니다.</param>
      <exception cref="T:System.ObjectDisposedException">제공된 <see cref="T:System.Threading.CancellationToken" />가 이미 삭제된 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="action" /> 인수가 null일 때 throw되는 예외입니다.또는<paramref name="scheduler" /> 인수가 null일 때 throw되는 예외입니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 인수가 잘못된 TaskCreationOptions 값을 지정하는 경우 throw되는 예외입니다.<paramref name="creationOptions" /> 인수가 잘못된 TaskCreationOptions 값을 지정하는 경우 throw되는 예외입니다.자세한 내용은 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />에 대한 설명을 참조하십시오.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action,System.Threading.Tasks.TaskCreationOptions)">
      <summary>
        <see cref="T:System.Threading.Tasks.Task" />를 만들고 시작합니다.</summary>
      <returns>시작된 <see cref="T:System.Threading.Tasks.Task" />입니다.</returns>
      <param name="action">비동기적으로 실행할 동작 대리자입니다.</param>
      <param name="creationOptions">만들어진 <see cref="T:System.Threading.Tasks.Task" />의 동작을 제어하는 TaskCreationOptions 값입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="action" /> 인수가 null일 때 throw되는 예외입니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 인수가 잘못된 TaskCreationOptions 값을 지정하는 경우 throw되는 예외입니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action{System.Object},System.Object)">
      <summary>
        <see cref="T:System.Threading.Tasks.Task" />를 만들고 시작합니다. </summary>
      <returns>시작된 <see cref="T:System.Threading.Tasks.Task" />입니다. </returns>
      <param name="action">비동기적으로 실행할 동작 대리자입니다. </param>
      <param name="state">
        <paramref name="action" /> 대리자가 사용할 데이터가 포함된 개체입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="action" /> 인수가 null인 경우 </exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action{System.Object},System.Object,System.Threading.CancellationToken)">
      <summary>
        <see cref="T:System.Threading.Tasks.Task" />를 만들고 시작합니다.</summary>
      <returns>시작된 <see cref="T:System.Threading.Tasks.Task" />입니다.</returns>
      <param name="action">비동기적으로 실행할 동작 대리자입니다.</param>
      <param name="state">
        <paramref name="action" /> 대리자가 사용할 데이터가 포함된 개체입니다.</param>
      <param name="cancellationToken">새 <see cref="T:System.Threading.Tasks.Task" />에 할당할 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />입니다.</param>
      <exception cref="T:System.ObjectDisposedException">제공된 <see cref="T:System.Threading.CancellationToken" />가 이미 삭제된 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="action" /> 인수가 null일 때 throw되는 예외입니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action{System.Object},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>
        <see cref="T:System.Threading.Tasks.Task" />를 만들고 시작합니다.</summary>
      <returns>시작된 <see cref="T:System.Threading.Tasks.Task" />입니다.</returns>
      <param name="action">비동기적으로 실행할 동작 대리자입니다.</param>
      <param name="state">
        <paramref name="action" /> 대리자가 사용할 데이터가 포함된 개체입니다.</param>
      <param name="cancellationToken">새 작업에 할당할 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />입니다.</param>
      <param name="creationOptions">만들어진 <see cref="T:System.Threading.Tasks.Task" />의 동작을 제어하는 TaskCreationOptions 값입니다.</param>
      <param name="scheduler">만들어진 <see cref="T:System.Threading.Tasks.TaskScheduler" />를 예약하는 데 사용할 <see cref="T:System.Threading.Tasks.Task" />입니다.</param>
      <exception cref="T:System.ObjectDisposedException">제공된 <see cref="T:System.Threading.CancellationToken" />가 이미 삭제된 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="action" /> 인수가 null일 때 throw되는 예외입니다.또는<paramref name="scheduler" /> 인수가 null일 때 throw되는 예외입니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 인수가 잘못된 TaskCreationOptions 값을 지정하는 경우 throw되는 예외입니다.<paramref name="creationOptions" /> 인수가 잘못된 TaskCreationOptions 값을 지정하는 경우 throw되는 예외입니다.자세한 내용은 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />에 대한 설명을 참조하십시오.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action{System.Object},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>
        <see cref="T:System.Threading.Tasks.Task" />를 만들고 시작합니다.</summary>
      <returns>시작된 <see cref="T:System.Threading.Tasks.Task" />입니다.</returns>
      <param name="action">비동기적으로 실행할 동작 대리자입니다.</param>
      <param name="state">
        <paramref name="action" /> 대리자가 사용할 데이터가 포함된 개체입니다.</param>
      <param name="creationOptions">만들어진 <see cref="T:System.Threading.Tasks.Task" />의 동작을 제어하는 TaskCreationOptions 값입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="action" /> 인수가 null일 때 throw되는 예외입니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 인수가 잘못된 TaskCreationOptions 값을 지정하는 경우 throw되는 예외입니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{``0})">
      <summary>
        <see cref="T:System.Threading.Tasks.Task`1" />를 만들고 시작합니다.</summary>
      <returns>시작된 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</returns>
      <param name="function">
        <see cref="T:System.Threading.Tasks.Task`1" />를 통해 사용할 수 있는 이후 결과를 반환하는 함수 대리자입니다.</param>
      <typeparam name="TResult">
        <see cref="T:System.Threading.Tasks.Task`1" />를 통해 사용할 수 있는 결과의 형식입니다. </typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 인수가 null인 경우</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{``0},System.Threading.CancellationToken)">
      <summary>
        <see cref="T:System.Threading.Tasks.Task`1" />를 만들고 시작합니다.</summary>
      <returns>시작된 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</returns>
      <param name="function">
        <see cref="T:System.Threading.Tasks.Task`1" />를 통해 사용할 수 있는 이후 결과를 반환하는 함수 대리자입니다.</param>
      <param name="cancellationToken">새 <see cref="T:System.Threading.Tasks.Task" />에 할당할 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />입니다.</param>
      <typeparam name="TResult">
        <see cref="T:System.Threading.Tasks.Task`1" />를 통해 사용할 수 있는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ObjectDisposedException">제공된 <see cref="T:System.Threading.CancellationToken" />가 이미 삭제된 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 인수가 null일 때 throw되는 예외입니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{``0},System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>
        <see cref="T:System.Threading.Tasks.Task`1" />를 만들고 시작합니다.</summary>
      <returns>시작된 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</returns>
      <param name="function">
        <see cref="T:System.Threading.Tasks.Task`1" />를 통해 사용할 수 있는 이후 결과를 반환하는 함수 대리자입니다.</param>
      <param name="cancellationToken">새 작업에 할당할 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />입니다.</param>
      <param name="creationOptions">만들어진 <see cref="T:System.Threading.Tasks.Task`1" />의 동작을 제어하는 TaskCreationOptions 값입니다.</param>
      <param name="scheduler">만들어진 <see cref="T:System.Threading.Tasks.TaskScheduler" />를 예약하는 데 사용할 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</param>
      <typeparam name="TResult">
        <see cref="T:System.Threading.Tasks.Task`1" />를 통해 사용할 수 있는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ObjectDisposedException">제공된 <see cref="T:System.Threading.CancellationToken" />가 이미 삭제된 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 인수가 null일 때 throw되는 예외입니다.또는<paramref name="scheduler" /> 인수가 null일 때 throw되는 예외입니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 인수가 잘못된 TaskCreationOptions 값을 지정하는 경우 throw되는 예외입니다.<paramref name="creationOptions" /> 인수가 잘못된 TaskCreationOptions 값을 지정하는 경우 throw되는 예외입니다.자세한 내용은 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />에 대한 설명을 참조하십시오.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{``0},System.Threading.Tasks.TaskCreationOptions)">
      <summary>
        <see cref="T:System.Threading.Tasks.Task`1" />를 만들고 시작합니다.</summary>
      <returns>시작된 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</returns>
      <param name="function">
        <see cref="T:System.Threading.Tasks.Task`1" />를 통해 사용할 수 있는 이후 결과를 반환하는 함수 대리자입니다.</param>
      <param name="creationOptions">만들어진 <see cref="T:System.Threading.Tasks.Task`1" />의 동작을 제어하는 TaskCreationOptions 값입니다.</param>
      <typeparam name="TResult">
        <see cref="T:System.Threading.Tasks.Task`1" />를 통해 사용할 수 있는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 인수가 null일 때 throw되는 예외입니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 인수가 잘못된 TaskCreationOptions 값을 지정하는 경우 throw되는 예외입니다.<paramref name="creationOptions" /> 인수가 잘못된 TaskCreationOptions 값을 지정하는 경우 throw되는 예외입니다.자세한 내용은 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />에 대한 설명을 참조하십시오.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{System.Object,``0},System.Object)">
      <summary>
        <see cref="T:System.Threading.Tasks.Task`1" />를 만들고 시작합니다.</summary>
      <returns>시작된 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</returns>
      <param name="function">
        <see cref="T:System.Threading.Tasks.Task`1" />를 통해 사용할 수 있는 이후 결과를 반환하는 함수 대리자입니다.</param>
      <param name="state">
        <paramref name="function" /> 대리자가 사용할 데이터가 포함된 개체입니다.</param>
      <typeparam name="TResult">
        <see cref="T:System.Threading.Tasks.Task`1" />를 통해 사용할 수 있는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 인수가 null일 때 throw되는 예외입니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{System.Object,``0},System.Object,System.Threading.CancellationToken)">
      <summary>
        <see cref="T:System.Threading.Tasks.Task`1" />를 만들고 시작합니다.</summary>
      <returns>시작된 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</returns>
      <param name="function">
        <see cref="T:System.Threading.Tasks.Task`1" />를 통해 사용할 수 있는 이후 결과를 반환하는 함수 대리자입니다.</param>
      <param name="state">
        <paramref name="function" /> 대리자가 사용할 데이터가 포함된 개체입니다.</param>
      <param name="cancellationToken">새 <see cref="T:System.Threading.Tasks.Task" />에 할당할 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />입니다.</param>
      <typeparam name="TResult">
        <see cref="T:System.Threading.Tasks.Task`1" />를 통해 사용할 수 있는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ObjectDisposedException">제공된 <see cref="T:System.Threading.CancellationToken" />가 이미 삭제된 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 인수가 null일 때 throw되는 예외입니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{System.Object,``0},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>
        <see cref="T:System.Threading.Tasks.Task`1" />를 만들고 시작합니다.</summary>
      <returns>시작된 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</returns>
      <param name="function">
        <see cref="T:System.Threading.Tasks.Task`1" />를 통해 사용할 수 있는 이후 결과를 반환하는 함수 대리자입니다.</param>
      <param name="state">
        <paramref name="function" /> 대리자가 사용할 데이터가 포함된 개체입니다.</param>
      <param name="cancellationToken">새 작업에 할당할 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />입니다.</param>
      <param name="creationOptions">만들어진 <see cref="T:System.Threading.Tasks.Task`1" />의 동작을 제어하는 TaskCreationOptions 값입니다.</param>
      <param name="scheduler">만들어진 <see cref="T:System.Threading.Tasks.TaskScheduler" />를 예약하는 데 사용할 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</param>
      <typeparam name="TResult">
        <see cref="T:System.Threading.Tasks.Task`1" />를 통해 사용할 수 있는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ObjectDisposedException">제공된 <see cref="T:System.Threading.CancellationToken" />가 이미 삭제된 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 인수가 null일 때 throw되는 예외입니다.또는<paramref name="scheduler" /> 인수가 null일 때 throw되는 예외입니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 인수가 잘못된 TaskCreationOptions 값을 지정하는 경우 throw되는 예외입니다.<paramref name="creationOptions" /> 인수가 잘못된 TaskCreationOptions 값을 지정하는 경우 throw되는 예외입니다.자세한 내용은 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />에 대한 설명을 참조하십시오.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{System.Object,``0},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>
        <see cref="T:System.Threading.Tasks.Task`1" />를 만들고 시작합니다.</summary>
      <returns>시작된 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</returns>
      <param name="function">
        <see cref="T:System.Threading.Tasks.Task`1" />를 통해 사용할 수 있는 이후 결과를 반환하는 함수 대리자입니다.</param>
      <param name="state">
        <paramref name="function" /> 대리자가 사용할 데이터가 포함된 개체입니다.</param>
      <param name="creationOptions">만들어진 <see cref="T:System.Threading.Tasks.Task`1" />의 동작을 제어하는 TaskCreationOptions 값입니다.</param>
      <typeparam name="TResult">
        <see cref="T:System.Threading.Tasks.Task`1" />를 통해 사용할 수 있는 결과의 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 인수가 null일 때 throw되는 예외입니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 인수가 잘못된 TaskCreationOptions 값을 지정하는 경우 throw되는 예외입니다.<paramref name="creationOptions" /> 인수가 잘못된 TaskCreationOptions 값을 지정하는 경우 throw되는 예외입니다.자세한 내용은 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />에 대한 설명을 참조하십시오.</exception>
    </member>
    <member name="T:System.Threading.Tasks.TaskFactory`1">
      <summary>
        <see cref="T:System.Threading.Tasks.Task`1" /> 개체를 만들고 예약하도록 지원합니다.</summary>
      <typeparam name="TResult">이 클래스의 메서드로 만든 <see cref="T:System.Threading.Tasks.Task`1" /> 개체의 반환 값입니다. </typeparam>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.#ctor">
      <summary>기본 구성을 사용하여 <see cref="T:System.Threading.Tasks.TaskFactory`1" /> 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.#ctor(System.Threading.CancellationToken)">
      <summary>기본 구성을 사용하여 <see cref="T:System.Threading.Tasks.TaskFactory`1" /> 인스턴스를 초기화합니다.</summary>
      <param name="cancellationToken">팩터리 메서드를 호출할 때 다른 취소 토큰을 명시적으로 지정하지 않는 경우 이 <see cref="T:System.Threading.Tasks.TaskFactory" />에서 만든 작업에 할당할 기본 취소 토큰입니다.</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.#ctor(System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>지정된 구성을 사용하여 <see cref="T:System.Threading.Tasks.TaskFactory`1" /> 인스턴스를 초기화합니다.</summary>
      <param name="cancellationToken">팩터리 메서드를 호출할 때 다른 취소 토큰을 명시적으로 지정하지 않는 경우 이 <see cref="T:System.Threading.Tasks.TaskFactory" />에서 만든 작업에 할당할 기본 취소 토큰입니다.</param>
      <param name="creationOptions">이 <see cref="T:System.Threading.Tasks.TaskFactory`1" />을 사용하여 작업을 만들 때 사용할 기본 옵션입니다.</param>
      <param name="continuationOptions">이 <see cref="T:System.Threading.Tasks.TaskFactory`1" />을 사용하여 연속 작업을 만들 때 사용할 기본 옵션입니다.</param>
      <param name="scheduler">이 <see cref="T:System.Threading.Tasks.TaskFactory`1" />을 사용하여 만든 작업을 예약하는 데 사용할 기본 스케줄러입니다.null 값은 <see cref="P:System.Threading.Tasks.TaskScheduler.Current" />를 사용해야 함을 나타냅니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 또는 <paramref name="continuationOptions" />에 잘못된 값을 지정하는 경우</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.#ctor(System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>지정된 구성을 사용하여 <see cref="T:System.Threading.Tasks.TaskFactory`1" /> 인스턴스를 초기화합니다.</summary>
      <param name="creationOptions">이 <see cref="T:System.Threading.Tasks.TaskFactory`1" />을 사용하여 작업을 만들 때 사용할 기본 옵션입니다.</param>
      <param name="continuationOptions">이 <see cref="T:System.Threading.Tasks.TaskFactory`1" />을 사용하여 연속 작업을 만들 때 사용할 기본 옵션입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 또는 <paramref name="continuationOptions" />에 잘못된 값을 지정하는 경우</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.#ctor(System.Threading.Tasks.TaskScheduler)">
      <summary>지정된 구성을 사용하여 <see cref="T:System.Threading.Tasks.TaskFactory`1" /> 인스턴스를 초기화합니다.</summary>
      <param name="scheduler">이 <see cref="T:System.Threading.Tasks.TaskFactory`1" />을 사용하여 만든 작업을 예약하는 데 사용할 스케줄러입니다.null 값은 현재 <see cref="T:System.Threading.Tasks.TaskScheduler" />를 사용해야 함을 나타냅니다.</param>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory`1.CancellationToken">
      <summary>이 작업 팩터리에 대한 기본 취소 토큰을 가져옵니다.</summary>
      <returns>이 작업 팩터리에 대한 기본 취소 토큰입니다.</returns>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory`1.ContinuationOptions">
      <summary>이 작업 팩터리에 대한 <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> 열거형 값을 가져옵니다.</summary>
      <returns>이 작업 팩터리에 대한 기본 연속 옵션을 지정하는 열거형 값 중 하나입니다.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],`0})">
      <summary>제공된 작업 집합이 완료될 때 시작할 연속 작업을 만듭니다.</summary>
      <returns>새 연속 작업입니다.</returns>
      <param name="tasks">계속할 작업의 배열입니다.</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 배열의 모든 작업이 완료되었을 때 비동기로 실행할 함수 대리자입니다.</param>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 배열에 있는 요소 중 하나가 삭제되었습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 배열이 null인 경우또는<paramref name="continuationFunction" />이 null인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 배열에 null 값이 있거나 비어 있습니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],`0},System.Threading.CancellationToken)">
      <summary>제공된 작업 집합이 완료될 때 시작할 연속 작업을 만듭니다.</summary>
      <returns>새 연속 작업입니다.</returns>
      <param name="tasks">계속할 작업의 배열입니다.</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 배열의 모든 작업이 완료되었을 때 비동기로 실행할 함수 대리자입니다.</param>
      <param name="cancellationToken">새 연속 작업에 할당할 취소 토큰입니다.</param>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 배열에 있는 요소 중 하나가 삭제되었습니다.또는<see cref="T:System.Threading.CancellationTokenSource" /> 만든<paramref name=" cancellationToken" /> 이미 삭제 되었습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 배열은 null입니다.또는<paramref name="continuationFunction" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 배열에 null 값이 있거나 비어 있습니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>제공된 작업 집합이 완료될 때 시작할 연속 작업을 만듭니다.</summary>
      <returns>새 연속 작업입니다.</returns>
      <param name="tasks">계속할 작업의 배열입니다.</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 배열의 모든 작업이 완료되었을 때 비동기로 실행할 함수 대리자입니다.</param>
      <param name="cancellationToken">새 연속 작업에 할당할 취소 토큰입니다.</param>
      <param name="continuationOptions">만들어진 연속 작업의 동작을 제어하는 열거형 값 중 하나입니다.NotOn* 또는 OnlyOn* 값이 잘못되었습니다.</param>
      <param name="scheduler">만들어진 연속 작업을 예약하는 데 사용할 스케줄러입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 배열은 null입니다.또는<paramref name="continuationFunction" /> 인수가 null인 경우또는<paramref name="scheduler" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 배열에 null 값이 있거나 비어 있습니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" />가 잘못된 값을 지정하는 경우</exception>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 배열에 있는 요소 중 하나가 삭제되었습니다.또는<see cref="T:System.Threading.CancellationTokenSource" /> 만든<paramref name=" cancellationToken" /> 이미 삭제 되었습니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],`0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>제공된 작업 집합이 완료될 때 시작할 연속 작업을 만듭니다.</summary>
      <returns>새 연속 작업입니다.</returns>
      <param name="tasks">계속할 작업의 배열입니다.</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 배열의 모든 작업이 완료되었을 때 비동기로 실행할 함수 대리자입니다.</param>
      <param name="continuationOptions">만들어진 연속 작업의 동작을 제어하는 열거형 값 중 하나입니다.NotOn* 또는 OnlyOn* 값이 잘못되었습니다.</param>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 배열에 있는 요소 중 하나가 삭제되었습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 배열은 null입니다.또는<paramref name="continuationFunction" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 인수에서 잘못된 값을 지정합니다.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 배열에 null 값이 있거나 비어 있습니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],`0})">
      <summary>제공된 작업 집합이 완료될 때 시작할 연속 작업을 만듭니다.</summary>
      <returns>새 연속 작업입니다.</returns>
      <param name="tasks">계속할 작업의 배열입니다.</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 배열의 모든 작업이 완료되었을 때 비동기로 실행할 함수 대리자입니다.</param>
      <typeparam name="TAntecedentResult">선행 <paramref name="tasks" />의 결과 형식입니다.</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 배열에 있는 요소 중 하나가 삭제되었습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 배열은 null입니다.또는<paramref name="continuationFunction" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 배열에 null 값이 있거나 비어 있습니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],`0},System.Threading.CancellationToken)">
      <summary>제공된 작업 집합이 완료될 때 시작할 연속 작업을 만듭니다.</summary>
      <returns>새 연속 작업입니다.</returns>
      <param name="tasks">계속할 작업의 배열입니다.</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 배열의 모든 작업이 완료되었을 때 비동기로 실행할 함수 대리자입니다.</param>
      <param name="cancellationToken">새 연속 작업에 할당할 취소 토큰입니다.</param>
      <typeparam name="TAntecedentResult">선행 <paramref name="tasks" />의 결과 형식입니다.</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 배열에 있는 요소 중 하나가 삭제되었습니다.또는<see cref="T:System.Threading.CancellationTokenSource" /> 만든<paramref name=" cancellationToken" /> 이미 삭제 되었습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 배열은 null입니다.또는<paramref name="continuationFunction" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 배열에 null 값이 있거나 비어 있습니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>제공된 작업 집합이 완료될 때 시작할 연속 작업을 만듭니다.</summary>
      <returns>새 연속 작업입니다.</returns>
      <param name="tasks">계속할 작업의 배열입니다.</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 배열의 모든 작업이 완료되었을 때 비동기로 실행할 함수 대리자입니다.</param>
      <param name="cancellationToken">새 연속 작업에 할당할 취소 토큰입니다.</param>
      <param name="continuationOptions">만들어진 연속 작업의 동작을 제어하는 열거형 값 중 하나입니다.NotOn* 또는 OnlyOn* 값이 잘못되었습니다.</param>
      <param name="scheduler">만들어진 연속 작업을 예약하는 데 사용할 스케줄러입니다.</param>
      <typeparam name="TAntecedentResult">선행 <paramref name="tasks" />의 결과 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 배열은 null입니다.또는<paramref name="continuationFunction" /> 인수가 null인 경우또는<paramref name="scheduler" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 배열에 null 값이 있거나 비어 있습니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 인수에서 잘못된 값을 지정합니다.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 배열에 있는 요소 중 하나가 삭제되었습니다.또는<see cref="T:System.Threading.CancellationTokenSource" /> 만든<paramref name=" cancellationToken" /> 이미 삭제 되었습니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],`0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>제공된 작업 집합이 완료될 때 시작할 연속 작업을 만듭니다.</summary>
      <returns>새 연속 작업입니다.</returns>
      <param name="tasks">계속할 작업의 배열입니다.</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 배열의 모든 작업이 완료되었을 때 비동기로 실행할 함수 대리자입니다.</param>
      <param name="continuationOptions">만들어진 연속 작업의 동작을 제어하는 열거형 값 중 하나입니다.NotOn* 또는 OnlyOn* 값이 잘못되었습니다.</param>
      <typeparam name="TAntecedentResult">선행 <paramref name="tasks" />의 결과 형식입니다.</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 배열에 있는 요소 중 하나가 삭제되었습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 배열은 null입니다.또는<paramref name="continuationFunction" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 인수에서 잘못된 값을 지정합니다.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 배열에 null 값이 있거나 비어 있습니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,`0})">
      <summary>제공된 집합에서 모든 작업이 완료될 때 시작할 연속 작업을 만듭니다. </summary>
      <returns>새 연속 작업입니다.</returns>
      <param name="tasks">작업이 완료될 때 계속할 작업의 배열입니다.</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 배열에서 작업이 완료될 때 비동기로 실행할 함수 대리자입니다.</param>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 배열에 있는 요소 중 하나가 삭제되었습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 배열은 null입니다.또는<paramref name="continuationFunction" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 배열에 null 값이 있거나 비어 있습니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,`0},System.Threading.CancellationToken)">
      <summary>제공된 집합에서 모든 작업이 완료될 때 시작할 연속 작업을 만듭니다.</summary>
      <returns>새 연속 작업입니다.</returns>
      <param name="tasks">작업이 완료될 때 계속할 작업의 배열입니다.</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 배열에서 작업이 완료될 때 비동기로 실행할 함수 대리자입니다.</param>
      <param name="cancellationToken">새 연속 작업에 할당할 취소 토큰입니다.</param>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 배열에 있는 요소 중 하나가 삭제되었습니다.또는<see cref="T:System.Threading.CancellationTokenSource" /> 만든<paramref name=" cancellationToken" /> 이미 삭제 되었습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 배열이 null입니다.또는<paramref name="continuationFunction" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 배열에 null 값이 들어 있습니다.또는<paramref name="tasks" /> 배열이 비어 있습니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>제공된 집합에서 모든 작업이 완료될 때 시작할 연속 작업을 만듭니다.</summary>
      <returns>새 연속 작업입니다.</returns>
      <param name="tasks">작업이 완료될 때 계속할 작업의 배열입니다.</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 배열에서 작업이 완료될 때 비동기로 실행할 함수 대리자입니다.</param>
      <param name="cancellationToken">새 연속 작업에 할당할 취소 토큰입니다.</param>
      <param name="continuationOptions">만들어진 연속 작업의 동작을 제어하는 열거형 값 중 하나입니다.NotOn* 또는 OnlyOn* 값이 잘못되었습니다.</param>
      <param name="scheduler">만들어진 연속 작업을 예약하는 데 사용할 작업 스케줄러입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 배열은 null입니다.또는<paramref name="continuationFunction" /> 인수가 null인 경우또는<paramref name="scheduler" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 배열에 null 값이 들어 있습니다.또는<paramref name="tasks" /> 배열이 비어 있습니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 인수에서 잘못된 <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> 값을 지정합니다.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 배열에 있는 요소 중 하나가 삭제되었습니다.또는<see cref="T:System.Threading.CancellationTokenSource" /> 만든<paramref name=" cancellationToken" /> 이미 삭제 되었습니다. </exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,`0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>제공된 집합에서 모든 작업이 완료될 때 시작할 연속 작업을 만듭니다.</summary>
      <returns>새 연속 작업입니다.</returns>
      <param name="tasks">작업이 완료될 때 계속할 작업의 배열입니다.</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 배열에서 작업이 완료될 때 비동기로 실행할 함수 대리자입니다.</param>
      <param name="continuationOptions">만들어진 연속 작업의 동작을 제어하는 열거형 값 중 하나입니다.NotOn* 또는 OnlyOn* 값이 잘못되었습니다.</param>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 배열에 있는 요소 중 하나가 삭제되었습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 배열은 null입니다.또는<paramref name="continuationFunction" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 인수가 잘못된 열거형 값을 지정합니다.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 배열에 null 값이 들어 있습니다.또는<paramref name="tasks" /> 배열이 비어 있습니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},`0})">
      <summary>제공된 집합에서 모든 작업이 완료될 때 시작할 연속 작업을 만듭니다.</summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</returns>
      <param name="tasks">작업이 완료될 때 계속할 작업의 배열입니다.</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 배열에서 작업이 완료될 때 비동기로 실행할 함수 대리자입니다.</param>
      <typeparam name="TAntecedentResult">선행 <paramref name="tasks" />의 결과 형식입니다.</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 배열에 있는 요소 중 하나가 삭제되었습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 배열은 null입니다.또는<paramref name="continuationFunction" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 배열에 null 값이 들어 있습니다.또는<paramref name="tasks" /> 배열이 비어 있습니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},`0},System.Threading.CancellationToken)">
      <summary>제공된 집합에서 모든 작업이 완료될 때 시작할 연속 작업을 만듭니다.</summary>
      <returns>새 연속 작업입니다.</returns>
      <param name="tasks">작업이 완료될 때 계속할 작업의 배열입니다.</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 배열에서 작업이 완료될 때 비동기로 실행할 함수 대리자입니다.</param>
      <param name="cancellationToken">새 연속 작업에 할당할 취소 토큰입니다.</param>
      <typeparam name="TAntecedentResult">선행 <paramref name="tasks" />의 결과 형식입니다.</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 배열에 있는 요소 중 하나가 삭제되었습니다.또는<see cref="T:System.Threading.CancellationTokenSource" /> 만든<paramref name=" cancellationToken" /> 이미 삭제 되었습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 배열은 null입니다.또는<paramref name="continuationFunction" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 배열에 null 값이 들어 있습니다.또는<paramref name="tasks" /> 배열이 비어 있습니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>제공된 집합에서 모든 작업이 완료될 때 시작할 연속 작업을 만듭니다.</summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</returns>
      <param name="tasks">작업이 완료될 때 계속할 작업의 배열입니다.</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 배열에서 작업이 완료될 때 비동기로 실행할 함수 대리자입니다.</param>
      <param name="cancellationToken">새 연속 작업에 할당할 취소 토큰입니다.</param>
      <param name="continuationOptions">만들어진 연속 작업의 동작을 제어하는 열거형 값 중 하나입니다.NotOn* 또는 OnlyOn* 값이 잘못되었습니다.</param>
      <param name="scheduler">만들어진 연속 <see cref="T:System.Threading.Tasks.TaskScheduler" />를 예약하는 데 사용할 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</param>
      <typeparam name="TAntecedentResult">선행 <paramref name="tasks" />의 결과 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 배열은 null입니다.또는<paramref name="continuationFunction" /> 인수가 null인 경우또는<paramref name="scheduler" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 배열에 null 값이 들어 있습니다.또는<paramref name="tasks" /> 배열이 비어 있습니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 인수는 잘못된 TaskContinuationOptions 값을 지정합니다.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 배열에 있는 요소 중 하나가 삭제되었습니다.또는<see cref="T:System.Threading.CancellationTokenSource" /> 만든<paramref name=" cancellationToken" /> 이미 삭제 되었습니다. </exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},`0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>제공된 집합에서 모든 작업이 완료될 때 시작할 연속 작업을 만듭니다.</summary>
      <returns>새 연속 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</returns>
      <param name="tasks">작업이 완료될 때 계속할 작업의 배열입니다.</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 배열에서 작업이 완료될 때 비동기로 실행할 함수 대리자입니다.</param>
      <param name="continuationOptions">만들어진 연속 작업의 동작을 제어하는 열거형 값 중 하나입니다.NotOn* 또는 OnlyOn* 값이 잘못되었습니다.</param>
      <typeparam name="TAntecedentResult">선행 <paramref name="tasks" />의 결과 형식입니다.</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 배열에 있는 요소 중 하나가 삭제되었습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 배열은 null입니다.또는<paramref name="continuationFunction" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 인수가 잘못된 열거형 값을 지정합니다.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 배열에 null 값이 들어 있습니다.또는<paramref name="tasks" /> 배열이 비어 있습니다.</exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory`1.CreationOptions">
      <summary>이 작업 팩터리에 대한 <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> 열거형 값을 가져옵니다.</summary>
      <returns>이 작업 팩터리에 대한 기본 만들기 옵션을 지정하는 열거형 값 중 하나입니다.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},System.Object)">
      <summary>비동기 프로그래밍 모델 패턴을 따르는 Begin 및 End 메서드 쌍을 나타내는 작업을 만듭니다.</summary>
      <returns>비동기 작업을 나타내는 만들어진 작업입니다.</returns>
      <param name="beginMethod">비동기 작업을 시작하는 대리자입니다.</param>
      <param name="endMethod">비동기 작업을 종료하는 대리자입니다.</param>
      <param name="state">
        <paramref name="beginMethod" /> 대리자가 사용할 데이터가 포함된 개체입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 인수가 null인 경우또는<paramref name="endMethod" /> 인수가 null인 경우</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>비동기 프로그래밍 모델 패턴을 따르는 Begin 및 End 메서드 쌍을 나타내는 작업을 만듭니다.</summary>
      <returns>비동기 작업을 나타내는 만들어진 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</returns>
      <param name="beginMethod">비동기 작업을 시작하는 대리자입니다.</param>
      <param name="endMethod">비동기 작업을 종료하는 대리자입니다.</param>
      <param name="state">
        <paramref name="beginMethod" /> 대리자가 사용할 데이터가 포함된 개체입니다.</param>
      <param name="creationOptions">만들어진 작업의 동작을 제어하는 열거형 값 중 하나입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 인수가 null인 경우또는<paramref name="endMethod" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 인수에서 잘못된 값을 지정합니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``1(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,System.Object)">
      <summary>비동기 프로그래밍 모델 패턴을 따르는 Begin 및 End 메서드 쌍을 나타내는 작업을 만듭니다.</summary>
      <returns>비동기 작업을 나타내는 만들어진 작업입니다.</returns>
      <param name="beginMethod">비동기 작업을 시작하는 대리자입니다.</param>
      <param name="endMethod">비동기 작업을 종료하는 대리자입니다.</param>
      <param name="arg1">
        <paramref name="beginMethod" /> 대리자에 전달되는 첫 번째 인수입니다.</param>
      <param name="state">
        <paramref name="beginMethod" /> 대리자가 사용할 데이터가 포함된 개체입니다.</param>
      <typeparam name="TArg1">
        <paramref name="beginMethod" /> 대리자에 전달되는 첫 번째 인수의 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 인수가 null인 경우또는<paramref name="endMethod" /> 인수가 null인 경우</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``1(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>비동기 프로그래밍 모델 패턴을 따르는 Begin 및 End 메서드 쌍을 나타내는 작업을 만듭니다.</summary>
      <returns>비동기 작업을 나타내는 만들어진 작업입니다.</returns>
      <param name="beginMethod">비동기 작업을 시작하는 대리자입니다.</param>
      <param name="endMethod">비동기 작업을 종료하는 대리자입니다.</param>
      <param name="arg1">
        <paramref name="beginMethod" /> 대리자에 전달되는 첫 번째 인수입니다.</param>
      <param name="state">
        <paramref name="beginMethod" /> 대리자가 사용할 데이터가 포함된 개체입니다.</param>
      <param name="creationOptions">만들어진 작업의 동작을 제어하는 열거형 값 중 하나입니다.</param>
      <typeparam name="TArg1">
        <paramref name="beginMethod" /> 대리자에 전달되는 첫 번째 인수의 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 인수가 null인 경우또는<paramref name="endMethod" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 매개 변수가 잘못된 값을 지정합니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``2(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,``1,System.Object)">
      <summary>비동기 프로그래밍 모델 패턴을 따르는 Begin 및 End 메서드 쌍을 나타내는 작업을 만듭니다.</summary>
      <returns>비동기 작업을 나타내는 만들어진 작업입니다.</returns>
      <param name="beginMethod">비동기 작업을 시작하는 대리자입니다.</param>
      <param name="endMethod">비동기 작업을 종료하는 대리자입니다.</param>
      <param name="arg1">
        <paramref name="beginMethod" /> 대리자에 전달되는 첫 번째 인수입니다.</param>
      <param name="arg2">
        <paramref name="beginMethod" /> 대리자에 전달되는 두 번째 인수입니다.</param>
      <param name="state">
        <paramref name="beginMethod" /> 대리자가 사용할 데이터가 포함된 개체입니다.</param>
      <typeparam name="TArg1">
        <paramref name="beginMethod" /> 대리자에 전달되는 두 번째 인수의 형식입니다.</typeparam>
      <typeparam name="TArg2">
        <paramref name="beginMethod" /> 대리자에 전달되는 첫 번째 인수의 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 인수가 null인 경우또는<paramref name="endMethod" /> 인수가 null인 경우</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``2(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,``1,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>비동기 프로그래밍 모델 패턴을 따르는 Begin 및 End 메서드 쌍을 나타내는 작업을 만듭니다.</summary>
      <returns>비동기 작업을 나타내는 만들어진 작업입니다.</returns>
      <param name="beginMethod">비동기 작업을 시작하는 대리자입니다.</param>
      <param name="endMethod">비동기 작업을 종료하는 대리자입니다.</param>
      <param name="arg1">
        <paramref name="beginMethod" /> 대리자에 전달되는 첫 번째 인수입니다.</param>
      <param name="arg2">
        <paramref name="beginMethod" /> 대리자에 전달되는 두 번째 인수입니다.</param>
      <param name="state">
        <paramref name="beginMethod" /> 대리자가 사용할 데이터가 포함된 개체입니다.</param>
      <param name="creationOptions">만들어진 <see cref="T:System.Threading.Tasks.Task`1" />의 동작을 제어하는 개체입니다.</param>
      <typeparam name="TArg1">
        <paramref name="beginMethod" /> 대리자에 전달되는 두 번째 인수의 형식입니다.</typeparam>
      <typeparam name="TArg2">
        <paramref name="beginMethod" /> 대리자에 전달되는 첫 번째 인수의 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 인수가 null인 경우또는<paramref name="endMethod" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 매개 변수가 잘못된 값을 지정합니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``3(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,``1,``2,System.Object)">
      <summary>비동기 프로그래밍 모델 패턴을 따르는 Begin 및 End 메서드 쌍을 나타내는 작업을 만듭니다.</summary>
      <returns>비동기 작업을 나타내는 만들어진 작업입니다.</returns>
      <param name="beginMethod">비동기 작업을 시작하는 대리자입니다.</param>
      <param name="endMethod">비동기 작업을 종료하는 대리자입니다.</param>
      <param name="arg1">
        <paramref name="beginMethod" /> 대리자에 전달되는 첫 번째 인수입니다.</param>
      <param name="arg2">
        <paramref name="beginMethod" /> 대리자에 전달되는 두 번째 인수입니다.</param>
      <param name="arg3">
        <paramref name="beginMethod" /> 대리자에 전달되는 세 번째 인수입니다.</param>
      <param name="state">
        <paramref name="beginMethod" /> 대리자가 사용할 데이터가 포함된 개체입니다.</param>
      <typeparam name="TArg1">
        <paramref name="beginMethod" /> 대리자에 전달되는 두 번째 인수의 형식입니다.</typeparam>
      <typeparam name="TArg2">
        <paramref name="beginMethod" /> 대리자에 전달되는 세 번째 인수의 형식입니다.</typeparam>
      <typeparam name="TArg3">
        <paramref name="beginMethod" /> 대리자에 전달되는 첫 번째 인수의 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 인수가 null인 경우또는<paramref name="endMethod" /> 인수가 null인 경우</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``3(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,``1,``2,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>비동기 프로그래밍 모델 패턴을 따르는 Begin 및 End 메서드 쌍을 나타내는 작업을 만듭니다.</summary>
      <returns>비동기 작업을 나타내는 만들어진 작업입니다.</returns>
      <param name="beginMethod">비동기 작업을 시작하는 대리자입니다.</param>
      <param name="endMethod">비동기 작업을 종료하는 대리자입니다.</param>
      <param name="arg1">
        <paramref name="beginMethod" /> 대리자에 전달되는 첫 번째 인수입니다.</param>
      <param name="arg2">
        <paramref name="beginMethod" /> 대리자에 전달되는 두 번째 인수입니다.</param>
      <param name="arg3">
        <paramref name="beginMethod" /> 대리자에 전달되는 세 번째 인수입니다.</param>
      <param name="state">
        <paramref name="beginMethod" /> 대리자가 사용할 데이터가 포함된 개체입니다.</param>
      <param name="creationOptions">만들어진 작업의 동작을 제어하는 개체입니다.</param>
      <typeparam name="TArg1">
        <paramref name="beginMethod" /> 대리자에 전달되는 두 번째 인수의 형식입니다.</typeparam>
      <typeparam name="TArg2">
        <paramref name="beginMethod" /> 대리자에 전달되는 세 번째 인수의 형식입니다.</typeparam>
      <typeparam name="TArg3">
        <paramref name="beginMethod" /> 대리자에 전달되는 첫 번째 인수의 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 인수가 null인 경우또는<paramref name="endMethod" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 매개 변수가 잘못된 값을 지정합니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync(System.IAsyncResult,System.Func{System.IAsyncResult,`0})">
      <summary>지정된 <see cref="T:System.IAsyncResult" />가 완료될 때 End 메서드 함수를 실행하는 작업을 만듭니다.</summary>
      <returns>비동기 작업을 나타내는 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</returns>
      <param name="asyncResult">완료될 때 <see cref="T:System.IAsyncResult" />의 처리를 트리거해야 하는 <paramref name="endMethod" />입니다.</param>
      <param name="endMethod">완료된 <paramref name="asyncResult" />를 처리하는 함수 대리자입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> 인수가 null인 경우또는<paramref name="endMethod" /> 인수가 null인 경우</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync(System.IAsyncResult,System.Func{System.IAsyncResult,`0},System.Threading.Tasks.TaskCreationOptions)">
      <summary>지정된 <see cref="T:System.IAsyncResult" />가 완료될 때 End 메서드 함수를 실행하는 작업을 만듭니다.</summary>
      <returns>비동기 작업(operation)을 나타내는 작업(task)입니다.</returns>
      <param name="asyncResult">완료될 때 <see cref="T:System.IAsyncResult" />의 처리를 트리거해야 하는 <paramref name="endMethod" />입니다.</param>
      <param name="endMethod">완료된 <paramref name="asyncResult" />를 처리하는 함수 대리자입니다.</param>
      <param name="creationOptions">만들어진 작업의 동작을 제어하는 열거형 값 중 하나입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> 인수가 null인 경우또는<paramref name="endMethod" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 인수에서 잘못된 값을 지정합니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync(System.IAsyncResult,System.Func{System.IAsyncResult,`0},System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>지정된 <see cref="T:System.IAsyncResult" />가 완료될 때 End 메서드 함수를 실행하는 작업을 만듭니다.</summary>
      <returns>비동기 작업을 나타내는 만들어진 작업입니다.</returns>
      <param name="asyncResult">완료될 때 <see cref="T:System.IAsyncResult" />의 처리를 트리거해야 하는 <paramref name="endMethod" />입니다.</param>
      <param name="endMethod">완료된 <paramref name="asyncResult" />를 처리하는 함수 대리자입니다.</param>
      <param name="creationOptions">만들어진 작업의 동작을 제어하는 열거형 값 중 하나입니다.</param>
      <param name="scheduler">End 메서드를 실행하는 작업을 예약하는 데 사용할 작업 스케줄러입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> 인수가 null인 경우또는<paramref name="endMethod" /> 인수가 null인 경우또는<paramref name="scheduler" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 매개 변수가 잘못된 값을 지정합니다.</exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory`1.Scheduler">
      <summary>이 작업 팩터리에 대한 작업 스케줄러를 가져옵니다.</summary>
      <returns>이 작업 팩터리에 대한 작업 스케줄러입니다.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{`0})">
      <summary>작업을 만들고 시작합니다.</summary>
      <returns>시작된 작업입니다.</returns>
      <param name="function">작업을 통해 사용할 수 있는 이후 결과를 반환하는 함수 대리자입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 인수가 null인 경우</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{`0},System.Threading.CancellationToken)">
      <summary>작업을 만들고 시작합니다.</summary>
      <returns>시작된 작업입니다.</returns>
      <param name="function">작업을 통해 사용할 수 있는 이후 결과를 반환하는 함수 대리자입니다.</param>
      <param name="cancellationToken">새 작업에 할당할 취소 토큰입니다.</param>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="cancellationToken" />을 만든 취소 토큰 소스가 이미 삭제되었습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 인수가 null인 경우</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>작업을 만들고 시작합니다.</summary>
      <returns>시작된 작업입니다.</returns>
      <param name="function">작업을 통해 사용할 수 있는 이후 결과를 반환하는 함수 대리자입니다.</param>
      <param name="cancellationToken">새 작업에 할당할 취소 토큰입니다.</param>
      <param name="creationOptions">만들어진 작업의 동작을 제어하는 열거형 값 중 하나입니다.</param>
      <param name="scheduler">만들어진 작업을 예약하는 데 사용할 작업 스케줄러입니다.</param>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="cancellationToken" />을 만든 취소 토큰 소스가 이미 삭제되었습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 인수가 null인 경우또는<paramref name="scheduler" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 매개 변수가 잘못된 값을 지정합니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{`0},System.Threading.Tasks.TaskCreationOptions)">
      <summary>작업을 만들고 시작합니다.</summary>
      <returns>시작된 <see cref="T:System.Threading.Tasks.Task`1" />입니다.</returns>
      <param name="function">작업을 통해 사용할 수 있는 이후 결과를 반환하는 함수 대리자입니다.</param>
      <param name="creationOptions">만들어진 작업의 동작을 제어하는 열거형 값 중 하나입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 매개 변수가 잘못된 값을 지정합니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{System.Object,`0},System.Object)">
      <summary>작업을 만들고 시작합니다.</summary>
      <returns>시작된 작업입니다.</returns>
      <param name="function">작업을 통해 사용할 수 있는 이후 결과를 반환하는 함수 대리자입니다.</param>
      <param name="state">
        <paramref name="function" /> 대리자에서 사용할 데이터가 포함된 개체입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 인수가 null인 경우</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{System.Object,`0},System.Object,System.Threading.CancellationToken)">
      <summary>작업을 만들고 시작합니다.</summary>
      <returns>시작된 작업입니다.</returns>
      <param name="function">작업을 통해 사용할 수 있는 이후 결과를 반환하는 함수 대리자입니다.</param>
      <param name="state">
        <paramref name="function" /> 대리자에서 사용할 데이터가 포함된 개체입니다.</param>
      <param name="cancellationToken">새 작업에 할당할 취소 토큰입니다.</param>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="cancellationToken" />을 만든 취소 토큰 소스가 이미 삭제되었습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 인수가 null인 경우</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{System.Object,`0},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>작업을 만들고 시작합니다.</summary>
      <returns>시작된 작업입니다.</returns>
      <param name="function">작업을 통해 사용할 수 있는 이후 결과를 반환하는 함수 대리자입니다.</param>
      <param name="state">
        <paramref name="function" /> 대리자에서 사용할 데이터가 포함된 개체입니다.</param>
      <param name="cancellationToken">새 작업에 할당할 취소 토큰입니다.</param>
      <param name="creationOptions">만들어진 작업의 동작을 제어하는 열거형 값 중 하나입니다.</param>
      <param name="scheduler">만들어진 작업을 예약하는 데 사용할 작업 스케줄러입니다.</param>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="cancellationToken" />을 만든 취소 토큰 소스가 이미 삭제되었습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 인수가 null인 경우또는<paramref name="scheduler" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 매개 변수가 잘못된 값을 지정합니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{System.Object,`0},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>작업을 만들고 시작합니다.</summary>
      <returns>시작된 작업입니다.</returns>
      <param name="function">작업을 통해 사용할 수 있는 이후 결과를 반환하는 함수 대리자입니다.</param>
      <param name="state">
        <paramref name="function" /> 대리자에서 사용할 데이터가 포함된 개체입니다.</param>
      <param name="creationOptions">만들어진 작업의 동작을 제어하는 열거형 값 중 하나입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 매개 변수가 잘못된 값을 지정합니다.</exception>
    </member>
    <member name="T:System.Threading.Tasks.TaskScheduler">
      <summary>작업을 스레드의 큐에 대기하는 낮은 수준의 작업을 처리하는 개체를 나타냅니다.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.#ctor">
      <summary>
        <see cref="T:System.Threading.Tasks.TaskScheduler" />를 초기화합니다.</summary>
    </member>
    <member name="P:System.Threading.Tasks.TaskScheduler.Current">
      <summary>현재 실행 중인 작업과 연결된 <see cref="T:System.Threading.Tasks.TaskScheduler" />를 가져옵니다.</summary>
      <returns>현재 실행 중인 작업과 연결된 <see cref="T:System.Threading.Tasks.TaskScheduler" />를 반환합니다.</returns>
    </member>
    <member name="P:System.Threading.Tasks.TaskScheduler.Default">
      <summary>.NET Framework에 의해 제공된 기본 <see cref="T:System.Threading.Tasks.TaskScheduler" /> 인스턴스를 가져옵니다.</summary>
      <returns>기본 <see cref="T:System.Threading.Tasks.TaskScheduler" /> 인스턴스를 반환합니다.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.FromCurrentSynchronizationContext">
      <summary>현재 <see cref="T:System.Threading.SynchronizationContext" />와 연결된 <see cref="T:System.Threading.Tasks.TaskScheduler" />를 만듭니다.</summary>
      <returns>
        <see cref="P:System.Threading.SynchronizationContext.Current" />에서 확인된 현재 <see cref="T:System.Threading.SynchronizationContext" />와 연결된 <see cref="T:System.Threading.Tasks.TaskScheduler" />입니다.</returns>
      <exception cref="T:System.InvalidOperationException">현재 SynchronizationContext를 TaskScheduler로 사용할 수 없습니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.GetScheduledTasks">
      <summary>디버거를 지원하기 위해 현재 스케줄러의 큐에 대기되어 실행을 기다리고 있는 <see cref="T:System.Threading.Tasks.Task" /> 인스턴스의 열거 가능한 형식을 생성합니다.</summary>
      <returns>디버거가 현재 이 스케줄러의 큐에 대기된 작업을 트래버스할 수 있도록 허용하는 열거 가능한 형식입니다.</returns>
      <exception cref="T:System.NotSupportedException">이 스케줄러는 현재 대기 중인 작업 목록을 생성할 수 없습니다.</exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskScheduler.Id">
      <summary>이 <see cref="T:System.Threading.Tasks.TaskScheduler" />의 고유 ID를 가져옵니다.</summary>
      <returns>이 <see cref="T:System.Threading.Tasks.TaskScheduler" />의 고유 ID를 반환합니다.</returns>
    </member>
    <member name="P:System.Threading.Tasks.TaskScheduler.MaximumConcurrencyLevel">
      <summary>이 <see cref="T:System.Threading.Tasks.TaskScheduler" />가 지원할 수 있는 최대 동시성 수준을 나타냅니다.</summary>
      <returns>최대 동시성 수준을 나타내는 정수를 반환합니다.기본 스케줄러는 <see cref="F:System.Int32.MaxValue" />를 반환합니다.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.QueueTask(System.Threading.Tasks.Task)">
      <summary>
        <see cref="T:System.Threading.Tasks.Task" />를 스케줄러의 큐에 대기합니다. </summary>
      <param name="task">큐에 대기할 <see cref="T:System.Threading.Tasks.Task" />입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="task" /> 인수가 null인 경우</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.TryDequeue(System.Threading.Tasks.Task)">
      <summary>이전에 이 스케줄러의 큐에 대기된 <see cref="T:System.Threading.Tasks.Task" />를 큐에서 제거하려고 합니다.</summary>
      <returns>
        <paramref name="task" /> 인수가 큐에서 제거되었는지 여부를 나타내는 부울입니다.</returns>
      <param name="task">큐에서 제거할 <see cref="T:System.Threading.Tasks.Task" />입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="task" /> 인수가 null인 경우</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.TryExecuteTask(System.Threading.Tasks.Task)">
      <summary>이 스케줄러에 제공된 <see cref="T:System.Threading.Tasks.Task" />를 실행하려고 합니다.</summary>
      <returns>
        <paramref name="task" />가 실행되었으면 true이고, 그렇지 않으면 false인 부울입니다.실행이 실패하는 일반적인 이유는 작업이 이전에 실행되었거나 다른 스레드에서 실행 중이기 때문입니다.</returns>
      <param name="task">실행할 <see cref="T:System.Threading.Tasks.Task" /> 개체입니다.</param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="task" />는 이 스케줄러와 연결되어 있지 않습니다.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.TryExecuteTaskInline(System.Threading.Tasks.Task,System.Boolean)">
      <summary>제공된 <see cref="T:System.Threading.Tasks.Task" />를 이 호출에서 동기적으로 실행할 수 있는지 여부를 확인하고 실행 가능할 경우 실행합니다.</summary>
      <returns>작업이 인라인으로 실행되었는지 여부를 나타내는 부울 값입니다.</returns>
      <param name="task">실행할 <see cref="T:System.Threading.Tasks.Task" />입니다.</param>
      <param name="taskWasPreviouslyQueued">작업이 이전에 큐에 대기되었는지 여부를 나타내는 부울입니다.이 매개 변수가 True이면 작업이 이전에 큐에 대기된 것일 수 있습니다. False이면 작업이 큐에 대기되지 않은 것입니다. 작업을 큐에 대기하지 않고 인라인으로 실행하려면 이 호출을 수행합니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="task" /> 인수가 null인 경우</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="task" />가 이미 실행되었습니다.</exception>
    </member>
    <member name="E:System.Threading.Tasks.TaskScheduler.UnobservedTaskException">
      <summary>오류가 발생한 작업의 관찰되지 않은 예외가 기본적으로 프로세스를 종료하는 예외 에스컬레이션 정책을 트리거하려는 경우 발생합니다.</summary>
    </member>
    <member name="T:System.Threading.Tasks.TaskSchedulerException">
      <summary>
        <see cref="T:System.Threading.Tasks.TaskScheduler" />에서 잘못된 작업을 알리기 위해 사용되는 예외를 나타냅니다.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskSchedulerException.#ctor">
      <summary>오류를 설명하는 시스템 제공 메시지를 사용하여 <see cref="T:System.Threading.Tasks.TaskSchedulerException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskSchedulerException.#ctor(System.Exception)">
      <summary>기본 오류 메시지와 이 예외의 원인인 내부 예외에 대한 참조를 사용하여 <see cref="T:System.Threading.Tasks.TaskSchedulerException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="innerException">현재 예외의 원인이 되는 예외입니다.</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskSchedulerException.#ctor(System.String)">
      <summary>오류를 설명하는 지정된 메시지를 사용하여 <see cref="T:System.Threading.Tasks.TaskSchedulerException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">예외를 설명하는 메시지입니다.이 생성자의 호출자는 이 문자열이 현재 시스템 문화권에 맞게 지역화되었는지 확인하는 데 필요합니다.</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskSchedulerException.#ctor(System.String,System.Exception)">
      <summary>지정된 오류 메시지와 해당 예외의 근본 원인인 내부 예외에 대한 참조를 사용하여 <see cref="T:System.Threading.Tasks.TaskSchedulerException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">예외를 설명하는 메시지입니다.이 생성자의 호출자는 이 문자열이 현재 시스템 문화권에 맞게 지역화되었는지 확인하는 데 필요합니다.</param>
      <param name="innerException">현재 예외의 원인이 되는 예외입니다.<paramref name="innerException" /> 매개 변수가 null이 아니면 현재 예외는 내부 예외를 처리하는 catch 블록에서 발생합니다.</param>
    </member>
    <member name="T:System.Threading.Tasks.TaskStatus">
      <summary>
        <see cref="T:System.Threading.Tasks.Task" />의 수명 주기에서 현재 단계를 나타냅니다.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.Canceled">
      <summary>작업을 실행하기 전에 토큰이 신호를 받은 상태이거나 작업의 CancellationToken이 이미 신호를 받은 상태에서 자체 CancellationToken과 함께 OperationCanceledException을 throw하여 작업이 취소를 승인했습니다.자세한 내용은 작업 취소을 참조하십시오.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.Created">
      <summary>작업이 초기화되었지만 예약되지는 않았습니다.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.Faulted">
      <summary>작업이 처리되지 않은 예외 때문에 완료되었습니다.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.RanToCompletion">
      <summary>작업이 실행을 완료했습니다.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.Running">
      <summary>작업이 실행되고 있지만 완료되지 않았습니다.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.WaitingForActivation">
      <summary>작업이 .NET Framework 인프라를 통해 내부적으로 활성화되고 예약되기 위해 대기 중입니다.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.WaitingForChildrenToComplete">
      <summary>작업이 실행을 마쳤지만 연결된 자식 작업이 완료되기까지 암시적으로 대기 중입니다.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.WaitingToRun">
      <summary>작업이 실행되도록 예약되었지만 아직 실행되지 않았습니다.</summary>
    </member>
    <member name="T:System.Threading.Tasks.UnobservedTaskExceptionEventArgs">
      <summary>오류가 발생한 <see cref="T:System.Threading.Tasks.Task" />의 예외가 관찰되지 않을 때 발생하는 이벤트에 대한 데이터를 제공합니다.</summary>
    </member>
    <member name="M:System.Threading.Tasks.UnobservedTaskExceptionEventArgs.#ctor(System.AggregateException)">
      <summary>관찰되지 않은 예외를 사용하여 <see cref="T:System.Threading.Tasks.UnobservedTaskExceptionEventArgs" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="exception">관찰되지 않은 예외입니다.</param>
    </member>
    <member name="P:System.Threading.Tasks.UnobservedTaskExceptionEventArgs.Exception">
      <summary>관찰되지 않은 예외입니다.</summary>
      <returns>관찰되지 않은 예외입니다.</returns>
    </member>
    <member name="P:System.Threading.Tasks.UnobservedTaskExceptionEventArgs.Observed">
      <summary>이 예외가 "관찰됨"으로 표시되었는지 여부를 가져옵니다.</summary>
      <returns>이 예외가 "관찰됨"으로 표시되었으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="M:System.Threading.Tasks.UnobservedTaskExceptionEventArgs.SetObserved">
      <summary>
        <see cref="P:System.Threading.Tasks.UnobservedTaskExceptionEventArgs.Exception" />을 "관찰됨"으로 표시하여 기본적으로 프로세스를 종료하는 예외 에스컬레이션 정책을 트리거하지 않도록 합니다.</summary>
    </member>
  </members>
</doc>