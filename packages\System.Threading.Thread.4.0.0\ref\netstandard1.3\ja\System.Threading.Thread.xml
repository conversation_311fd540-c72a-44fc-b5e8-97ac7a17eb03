﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Threading.Thread</name>
  </assembly>
  <members>
    <member name="T:System.Threading.ParameterizedThreadStart">
      <summary>
        <see cref="T:System.Threading.Thread" /> で実行するメソッドを表します。</summary>
      <param name="obj">スレッド プロシージャのデータを格納しているオブジェクト。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Threading.Thread">
      <summary>スレッドを作成および制御し、その優先順位の設定およびステータスの取得を実行します。この種類の .NET Framework ソース コードを参照するには、参照ソースをご覧ください。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Thread.#ctor(System.Threading.ParameterizedThreadStart)">
      <summary>スレッドの開始時にオブジェクトをスレッドに渡すことを許可するデリゲートを指定して、<see cref="T:System.Threading.Thread" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="start">このスレッドが実行を開始するときに呼び出されるメソッドを表すデリゲート。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="start" /> is null. </exception>
    </member>
    <member name="M:System.Threading.Thread.#ctor(System.Threading.ThreadStart)">
      <summary>
        <see cref="T:System.Threading.Thread" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="start">このスレッドが実行を開始するときに呼び出されるメソッドを表す <see cref="T:System.Threading.ThreadStart" /> デリゲート。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="start" /> parameter is null. </exception>
    </member>
    <member name="P:System.Threading.Thread.CurrentThread">
      <summary>現在実行中のスレッドを取得します。</summary>
      <returns>現在実行中のスレッドを表す <see cref="T:System.Threading.Thread" />。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Threading.Thread.IsAlive">
      <summary>現在のスレッドの実行ステータスを示す値を取得します。</summary>
      <returns>このスレッドが起動していて、正常終了しておらず中止されてもいない場合は true。それ以外の場合は false。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Threading.Thread.IsBackground">
      <summary>スレッドがバックグラウンド スレッドであるかどうかを示す値を取得または設定します。</summary>
      <returns>このスレッドがバックグラウンド スレッドである場合またはバックグラウンド スレッドになる場合は true。それ以外の場合は false。</returns>
      <exception cref="T:System.Threading.ThreadStateException">The thread is dead. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Thread.Join">
      <summary>1 つのスレッドが終了するまで呼び出し元のスレッドをブロックします。標準 COM および SendMessage ポンピングの実行は継続されます。</summary>
      <exception cref="T:System.Threading.ThreadStateException">The caller attempted to join a thread that is in the <see cref="F:System.Threading.ThreadState.Unstarted" /> state. </exception>
      <exception cref="T:System.Threading.ThreadInterruptedException">The thread is interrupted while waiting. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Thread.Join(System.Int32)">
      <summary>1 つのスレッドが終了するまで、または指定された時間が経過するまで、呼び出し元のスレッドをブロックします。標準 COM/SendMessage ポンピングの実行は継続されます。</summary>
      <returns>スレッドが終了した場合は true。false パラメーターで指定した時間が経過してもスレッドが終了していない場合は <paramref name="millisecondsTimeout" />。</returns>
      <param name="millisecondsTimeout">スレッドが終了するまでの待機時間を表すミリ秒数。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The value of <paramref name="millisecondsTimeout" /> is negative and is not equal to <see cref="F:System.Threading.Timeout.Infinite" /> in milliseconds. </exception>
      <exception cref="T:System.Threading.ThreadStateException">The thread has not been started. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Threading.Thread.ManagedThreadId">
      <summary>現在のマネージ スレッドの一意の識別子を取得します。</summary>
      <returns>このマネージ スレッドの一意の識別子を表す整数。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Threading.Thread.Name">
      <summary>スレッドの名前を取得または設定します。</summary>
      <returns>スレッドの名前を含む文字列。名前が設定されていない場合は null。</returns>
      <exception cref="T:System.InvalidOperationException">A set operation was requested, but the Name property has already been set. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Thread.Sleep(System.Int32)">
      <summary>指定したミリ秒数の間現在のスレッドを中断します。</summary>
      <param name="millisecondsTimeout">スレッドが中断ブロックされるミリ秒数。<paramref name="millisecondsTimeout" /> 引数の値が 0 である場合は、スレッドは自らのタイム スライスの残りの部分を放棄し、実行する準備ができている同じ優先順位の他のスレッドに渡します。優先順位が同じで実行する準備ができている他のスレッドが存在しない場合は、現在のスレッドの実行は中断されません。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The time-out value is negative and is not equal to <see cref="F:System.Threading.Timeout.Infinite" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Thread.Sleep(System.TimeSpan)">
      <summary>指定した時間の長さにわたって現在のスレッドを中断します。</summary>
      <param name="timeout">スレッドが中断される時間の長さ。<paramref name="millisecondsTimeout" /> 引数の値が <see cref="F:System.TimeSpan.Zero" /> である場合は、スレッドは自らのタイム スライスの残りの部分を放棄し、実行する準備ができている同じ優先順位の他のスレッドに渡します。優先順位が同じで実行する準備ができている他のスレッドが存在しない場合は、現在のスレッドの実行は中断されません。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The value of <paramref name="timeout" /> is negative and is not equal to <see cref="F:System.Threading.Timeout.Infinite" /> in milliseconds, or is greater than <see cref="F:System.Int32.MaxValue" /> milliseconds. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Thread.Start">
      <summary>オペレーティング システムによって、現在のインスタンスの状態を <see cref="F:System.Threading.ThreadState.Running" /> に変更します。</summary>
      <exception cref="T:System.Threading.ThreadStateException">The thread has already been started. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory available to start this thread. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Thread.Start(System.Object)">
      <summary>オペレーティング システムによって現在のインスタンスの状態が <see cref="F:System.Threading.ThreadState.Running" /> に変更され、オプションでスレッドが実行するメソッドで使用するデータを格納するオブジェクトが提供されます。</summary>
      <param name="parameter">スレッドが実行するメソッドで使用するデータを格納するオブジェクト。</param>
      <exception cref="T:System.Threading.ThreadStateException">The thread has already been started. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory available to start this thread. </exception>
      <exception cref="T:System.InvalidOperationException">This thread was created using a <see cref="T:System.Threading.ThreadStart" /> delegate instead of a <see cref="T:System.Threading.ParameterizedThreadStart" /> delegate.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Threading.Thread.ThreadState">
      <summary>現在のスレッドの状態を示す値を取得します。</summary>
      <returns>現在のスレッドの状態を示す <see cref="T:System.Threading.ThreadState" /> 値の 1 つ。初期値は Unstarted です。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Threading.ThreadStart">
      <summary>
        <see cref="T:System.Threading.Thread" /> で実行するメソッドを表します。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Threading.ThreadStartException">
      <summary>基になるオペレーティング システムのスレッドが起動された後、スレッドでユーザー コードを実行する準備が完了する前にマネージ スレッドでエラーが発生したときにスローされる例外。</summary>
    </member>
    <member name="T:System.Threading.ThreadState">
      <summary>
        <see cref="T:System.Threading.Thread" /> の実行状態を指定します。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.Threading.ThreadState.Aborted">
      <summary>スレッド状態に <see cref="F:System.Threading.ThreadState.AbortRequested" /> が含まれ、そのスレッドは停止していますが、状態はまだ <see cref="F:System.Threading.ThreadState.Stopped" /> に変わっていません。</summary>
    </member>
    <member name="F:System.Threading.ThreadState.AbortRequested">
      <summary>スレッド上で <see cref="M:System.Threading.Thread.Abort(System.Object)" /> メソッドを呼び出しますが、そのスレッドの終了を試みる保留中の <see cref="T:System.Threading.ThreadAbortException" /> をスレッドが受け取っていません。</summary>
    </member>
    <member name="F:System.Threading.ThreadState.Background">
      <summary>スレッドは、フォアグラウンド スレッドではなく、バックグランド スレッドとして実行します。この状態は、<see cref="P:System.Threading.Thread.IsBackground" /> プロパティを設定して制御されます。</summary>
    </member>
    <member name="F:System.Threading.ThreadState.Running">
      <summary>スレッドをブロックせずに起動します。保留中の <see cref="T:System.Threading.ThreadAbortException" /> もありません。</summary>
    </member>
    <member name="F:System.Threading.ThreadState.Stopped">
      <summary>スレッドを停止します。</summary>
    </member>
    <member name="F:System.Threading.ThreadState.StopRequested">
      <summary>スレッドの停止を要求します。これは、内部でだけ使用します。</summary>
    </member>
    <member name="F:System.Threading.ThreadState.Suspended">
      <summary>スレッドは中断しています。</summary>
    </member>
    <member name="F:System.Threading.ThreadState.SuspendRequested">
      <summary>スレッドの中断を要求します。</summary>
    </member>
    <member name="F:System.Threading.ThreadState.Unstarted">
      <summary>スレッド上に <see cref="M:System.Threading.Thread.Start" /> メソッドを呼び出しません。</summary>
    </member>
    <member name="F:System.Threading.ThreadState.WaitSleepJoin">
      <summary>スレッドがブロックされています。これは、<see cref="M:System.Threading.Thread.Sleep(System.Int32)" /> または <see cref="M:System.Threading.Thread.Join" /> の呼び出し、ロックの要求 (たとえば、<see cref="M:System.Threading.Monitor.Enter(System.Object)" /> や <see cref="M:System.Threading.Monitor.Wait(System.Object,System.Int32,System.Boolean)" /> の呼び出しによる)、または <see cref="T:System.Threading.ManualResetEvent" /> などのスレッド同期オブジェクトの待機の結果である可能性があります。</summary>
    </member>
    <member name="T:System.Threading.ThreadStateException">
      <summary>メソッドの呼び出しで <see cref="T:System.Threading.Thread" /> が無効な <see cref="P:System.Threading.Thread.ThreadState" /> である場合は、例外がスローされます。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.ThreadStateException.#ctor">
      <summary>
        <see cref="T:System.Threading.ThreadStateException" /> クラスの新しいインスタンスを既定のプロパティを使用して初期化します。</summary>
    </member>
    <member name="M:System.Threading.ThreadStateException.#ctor(System.String)">
      <summary>指定したエラー メッセージを使用して、<see cref="T:System.Threading.ThreadStateException" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="message">例外の原因を説明するエラー メッセージ。</param>
    </member>
    <member name="M:System.Threading.ThreadStateException.#ctor(System.String,System.Exception)">
      <summary>指定したエラー メッセージと、この例外の原因である内部例外への参照を使用して、<see cref="T:System.Threading.ThreadStateException" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="message">例外の原因を説明するエラー メッセージ。</param>
      <param name="innerException">現在の例外の原因である例外。<paramref name="innerException" /> パラメーターが null でない場合は、内部例外を処理する catch ブロックで現在の例外が発生します。</param>
    </member>
  </members>
</doc>