﻿Imports System.IO
Imports System.Text.RegularExpressions
Imports System.Net
Imports System.Text
Imports DevExpress.XtraEditors
Public Class frmGhost
    Dim originalHtml As String = "" ' تخزين HTML الأصلي
    Dim cssBlocks As New List(Of String) ' قائمة لتخزين أكواد CSS مؤقتًا
    Dim titleBlocks As New List(Of String) ' قائمة لتخزين عنوان الصفحة <title>
    Dim dynamicKeywords As String() = {
        "[-Email-]", "[-IP-]", "[-IPChina-]", "[-Domain-]", "[-Name-]", "[-Date-]", "[-DateTomorrow-]",
        "[-RandomBrowser-]", "[-RCountry-]", "[-FakePhone-]", "[-FakeEmail-]", "[-NewYork-]", "[-UCase-]",
        "[-Link-]", "[-Logo-]", "[-QRCode-]", "[-RN1-]", "[-RN2-]", "[-RN3-]", "[-RN4-]", "[-RN5-]", "[-RN6-]",
        "[-RN7-]", "[-RN8-]", "[-RN9-]", "[-RN10-]", "[-RN12-]", "[-RCh1-]", "[-RCh2-]", "[-RCh3-]", "[-RCh4-]",
        "[-RCh5-]", "[-RCh6-]", "[-RCh7-]", "[-RCh8-]"
    }
    Private Sub frmGhost_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ' جعل النموذج غير مرئي أثناء تطبيق التصميم
        Me.Opacity = 0

        ' تعيين القيم في الكومبو بوكس عند تحميل الفورم
        cmbEncryptionMode.SelectedIndex = 0 ' الخيار الافتراضي
        BntEncoded.Enabled = False

        ' تعيين عنوان النموذج
        Me.Text = "Ghost Letter"

        ' تطبيق تصميم Binance مباشرة
        Try
            ApplyBinanceStyle()

            ' إضافة معالج حدث لتغيير حجم النموذج
            AddHandler Me.Resize, AddressOf frmGhost_Resize

            ' وضع العناصر في المنتصف عند تحميل النموذج
            CenterControls()

            ' إضافة عنوان في أعلى النموذج
            AddHeaderImage()

            ' جعل النموذج مرئي بعد تطبيق التصميم
            Me.Opacity = 1
        Catch ex As Exception
            ' في حالة حدوث خطأ، جعل النموذج مرئي على أي حال
            Me.Opacity = 1
            Debug.WriteLine($"Error in frmGhost_Load: {ex.Message}")
            Debug.WriteLine(ex.StackTrace)
        End Try
    End Sub

    ''' <summary>
    ''' معالج حدث تغيير حجم النموذج
    ''' </summary>
    Private Sub frmGhost_Resize(sender As Object, e As EventArgs)
        ' وضع العناصر في المنتصف عند تغيير حجم النموذج
        CenterControls()
    End Sub

    ''' <summary>
    ''' تنسيق أحجام العناصر وموقعها
    ''' </summary>
    Private Sub CenterControls()
        Try
            ' الحصول على حجم النموذج
            Dim formWidth As Integer = Me.ClientSize.Width
            Dim formHeight As Integer = Me.ClientSize.Height

            ' البحث عن HeaderPictureBox
            Dim headerPictureBox As PictureBox = Nothing
            For Each ctrl As Control In Me.Controls
                If TypeOf ctrl Is PictureBox AndAlso ctrl.Name = "HeaderPictureBox" Then
                    headerPictureBox = DirectCast(ctrl, PictureBox)
                    Exit For
                End If
            Next

            ' تنسيق أحجام GroupControl1 و GroupControl2
            Dim panelWidth As Integer = (formWidth - 40) / 2
            Dim panelHeight As Integer = formHeight - (If(headerPictureBox IsNot Nothing, headerPictureBox.Height, 0)) - 15

            ' تنسيق GroupControl1
            GroupControl1.Size = New Size(panelWidth, panelHeight)
            GroupControl1.Location = New Point(10, If(headerPictureBox IsNot Nothing, headerPictureBox.Bottom + 5, 10))

            ' تنسيق GroupControl2
            GroupControl2.Size = New Size(panelWidth, panelHeight)
            GroupControl2.Location = New Point(GroupControl1.Right + 20, If(headerPictureBox IsNot Nothing, headerPictureBox.Bottom + 5, 10))

            ' تنسيق LayoutControl1
            LayoutControl1.Dock = DockStyle.Fill

            ' تنسيق أحجام الأزرار
            BntUploade.Size = New Size(100, 30)
            BntEncoded.Size = New Size(100, 30)
            bntRest.Size = New Size(100, 30)
            BntSave.Size = New Size(100, 30)

            ' تنسيق LetterEncoder_txtFilepath
            LetterEncoder_txtFilepath.Size = New Size(300, LetterEncoder_txtFilepath.Height)

            Debug.WriteLine($"Controls resized. Form size: {formWidth}x{formHeight}")
        Catch ex As Exception
            ' طباعة الخطأ للتصحيح
            Debug.WriteLine($"Error resizing controls: {ex.Message}")
            Debug.WriteLine(ex.StackTrace)
        End Try
    End Sub

    ''' <summary>
    ''' إضافة صورة العنوان في أعلى النموذج
    ''' </summary>
    Private Sub AddHeaderImage()
        Try
            ' إنشاء PictureBox للصورة
            Dim headerPictureBox As New PictureBox()
            headerPictureBox.Name = "HeaderPictureBox"
            headerPictureBox.BackColor = Color.Transparent
            headerPictureBox.SizeMode = PictureBoxSizeMode.Zoom ' جعل الصورة قابلة للتكبير

            ' تحميل الصورة من الموارد
            Try
                headerPictureBox.Image = My.Resources.GhostImage
                Debug.WriteLine("Image loaded from resources successfully")
            Catch ex As Exception
                Debug.WriteLine($"Error loading image from resources: {ex.Message}")
                ' إذا لم يتم العثور على الصورة، نستخدم صورة افتراضية
                headerPictureBox.BackColor = Color.FromArgb(13, 13, 13)
                headerPictureBox.Image = Nothing
            End Try

            ' تعيين حجم وموقع الصورة
            Dim imageHeight As Integer = 50
            headerPictureBox.Size = New Size(200, imageHeight)
            headerPictureBox.Location = New Point((Me.ClientSize.Width - headerPictureBox.Width) \ 2, 0)
            headerPictureBox.Dock = DockStyle.Top ' جعل الصورة في أعلى النموذج

            ' إضافة PictureBox إلى النموذج
            Me.Controls.Add(headerPictureBox)
            headerPictureBox.BringToFront()

            Debug.WriteLine("Header image added successfully")
        Catch ex As Exception
            Debug.WriteLine($"Error adding header image: {ex.Message}")
            Debug.WriteLine(ex.StackTrace)
        End Try
    End Sub

    ''' <summary>
    ''' تطبيق تصميم Binance على النموذج
    ''' </summary>
    Private Sub ApplyBinanceStyle()
        Try
            ' تعيين لون خلفية النموذج - لون Binance الأساسي الداكن
            Me.BackColor = Color.FromArgb(13, 13, 13) ' لون أكثر قتامة مثل Binance

            ' تنسيق LayoutControl1
            LayoutControl1.BackColor = Color.FromArgb(13, 13, 13)
            LayoutControl1.ForeColor = Color.White

            ' تنسيق GroupControl1
            GroupControl1.Appearance.BackColor = Color.FromArgb(22, 26, 30) ' لون Binance للبطاقات
            GroupControl1.Appearance.BorderColor = Color.FromArgb(30, 35, 40) ' حدود رمادية داكنة
            GroupControl1.Appearance.ForeColor = Color.White
            GroupControl1.Appearance.Options.UseBackColor = True
            GroupControl1.Appearance.Options.UseBorderColor = True
            GroupControl1.Appearance.Options.UseForeColor = True

            GroupControl1.AppearanceCaption.BackColor = Color.FromArgb(22, 26, 30)
            GroupControl1.AppearanceCaption.BorderColor = Color.FromArgb(30, 35, 40)
            GroupControl1.AppearanceCaption.ForeColor = Color.White
            GroupControl1.AppearanceCaption.Options.UseBackColor = True
            GroupControl1.AppearanceCaption.Options.UseBorderColor = True
            GroupControl1.AppearanceCaption.Options.UseForeColor = True
            GroupControl1.Text = "Normal"

            ' تنسيق GroupControl2
            GroupControl2.Appearance.BackColor = Color.FromArgb(22, 26, 30) ' لون Binance للبطاقات
            GroupControl2.Appearance.BorderColor = Color.FromArgb(30, 35, 40) ' حدود رمادية داكنة
            GroupControl2.Appearance.ForeColor = Color.White
            GroupControl2.Appearance.Options.UseBackColor = True
            GroupControl2.Appearance.Options.UseBorderColor = True
            GroupControl2.Appearance.Options.UseForeColor = True

            GroupControl2.AppearanceCaption.BackColor = Color.FromArgb(22, 26, 30)
            GroupControl2.AppearanceCaption.BorderColor = Color.FromArgb(30, 35, 40)
            GroupControl2.AppearanceCaption.ForeColor = Color.White
            GroupControl2.AppearanceCaption.Options.UseBackColor = True
            GroupControl2.AppearanceCaption.Options.UseBorderColor = True
            GroupControl2.AppearanceCaption.Options.UseForeColor = True
            GroupControl2.Text = "Encoded"

            ' تنسيق LetterEncoder_Richtext - مثل حقول الإدخال في Binance
            LetterEncoder_Richtext.BackColor = Color.FromArgb(30, 35, 40)
            LetterEncoder_Richtext.ForeColor = Color.White
            LetterEncoder_Richtext.BorderStyle = BorderStyle.None
            LetterEncoder_Richtext.Font = New Font("Segoe UI", 9.5F)

            ' تنسيق LetterEncoder_Richtext2 - مع لون النص الأصفر المميز لـ Binance
            LetterEncoder_Richtext2.BackColor = Color.FromArgb(30, 35, 40)
            LetterEncoder_Richtext2.ForeColor = Color.FromArgb(240, 185, 11) ' اللون الأصفر الذهبي لـ Binance
            LetterEncoder_Richtext2.BorderStyle = BorderStyle.None
            LetterEncoder_Richtext2.Font = New Font("Segoe UI", 9.5F)

            ' تنسيق LetterEncoder_txtFilepath - مثل حقول البحث في Binance
            LetterEncoder_txtFilepath.Properties.Appearance.BackColor = Color.FromArgb(30, 35, 40)
            LetterEncoder_txtFilepath.Properties.Appearance.ForeColor = Color.White
            LetterEncoder_txtFilepath.Properties.Appearance.Options.UseBackColor = True
            LetterEncoder_txtFilepath.Properties.Appearance.Options.UseForeColor = True
            LetterEncoder_txtFilepath.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
            LetterEncoder_txtFilepath.Properties.Appearance.Font = New Font("Segoe UI", 9)

            ' تنسيق ComboBoxEdit
            cmbEncryptionMode.Properties.Appearance.BackColor = Color.FromArgb(30, 35, 40)
            cmbEncryptionMode.Properties.Appearance.ForeColor = Color.White
            cmbEncryptionMode.Properties.Appearance.Options.UseBackColor = True
            cmbEncryptionMode.Properties.Appearance.Options.UseForeColor = True
            cmbEncryptionMode.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
            cmbEncryptionMode.Properties.Appearance.Font = New Font("Segoe UI", 9)


            Debug.WriteLine("Binance style applied successfully")
        Catch ex As Exception
            Debug.WriteLine($"Error applying Binance style: {ex.Message}")
            Debug.WriteLine(ex.StackTrace)
        End Try
    End Sub

    ''' <summary>
    ''' تنسيق الزر بأسلوب Binance (حواف فقط)
    ''' </summary>
    Private Sub FormatBinanceButton(button As DevExpress.XtraEditors.SimpleButton, tooltipText As String)
        Try
            ' تعيين نص التلميح
            button.ToolTip = tooltipText

            ' تعيين خصائص الزر - أسلوب Binance
            button.Appearance.BackColor = Color.FromArgb(40, 40, 40) ' خلفية داكنة
            button.Appearance.ForeColor = Color.White ' نص أبيض
            button.Cursor = Cursors.Hand
            button.Appearance.Options.UseBackColor = True
            button.Appearance.Options.UseForeColor = True

            ' تعيين حواف الزر باللون الأصفر
            button.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple
            button.Appearance.BorderColor = Color.FromArgb(96, 96, 96) ' لون الحواف العادي
            button.Appearance.Options.UseBorderColor = True

            ' تعيين خصائص الزر عند المرور فوقه
            button.Appearance.Options.UseFont = True
            button.Appearance.Font = New Font("Segoe UI", 9, FontStyle.Regular)

            ' تعيين خصائص الزر عند الضغط عليه
            button.PaintStyle = DevExpress.XtraEditors.Controls.PaintStyles.Light

            ' تعيين الحواف المنحنية
            button.LookAndFeel.UseDefaultLookAndFeel = False
            button.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Flat

            ' تعيين خصائص الزر عند المرور فوقه - تغيير لون الحواف فقط
            button.AppearanceHovered.BorderColor = Color.FromArgb(254, 219, 65) ' لون الحواف الأصفر عند المرور
            button.AppearanceHovered.Options.UseBorderColor = True

            ' تعيين خصائص الزر عند الضغط عليه
            button.AppearancePressed.BorderColor = Color.FromArgb(254, 219, 65) ' لون الحواف الأصفر عند الضغط
            button.AppearancePressed.Options.UseBorderColor = True
        Catch ex As Exception
            Debug.WriteLine($"Error formatting button: {ex.Message}")
            Debug.WriteLine(ex.StackTrace)
        End Try
    End Sub

    Private Function HideUnicode(text As String) As String
        If String.IsNullOrEmpty(text) Then Return text
        Dim hiddenChar As String = ChrW(&H200F) ' Right-to-Left Mark
        Dim result As New StringBuilder()
        ' التحقق من اختيار المستخدم
        If cmbEncryptionMode.SelectedItem.ToString() = "Encryption Method 1" Then
            ' 🔹 تشفير بعد كل حرف
            For Each ch As Char In text
                result.Append(ch & hiddenChar)
            Next
        Else
            ' 🔹 تشفير بعد كل كلمة
            Dim words As String() = text.Split(" "c) ' تقسيم النص إلى كلمات
            For Each word In words
                result.Append(word & hiddenChar & " ") ' إضافة الحرف المخفي بعد كل كلمة
            Next
        End If
        Return result.ToString().TrimEnd() ' إزالة آخر حرف زائد
    End Function




    Private Sub BntUploade_Click(sender As Object, e As EventArgs) Handles BntUploade.Click
        LetterEncoder_txtFilepath.ResetText()
        LetterEncoder_Richtext.ResetText()
        LetterEncoder_Richtext2.ResetText()
        BntEncoded.Enabled = True
        Dim openFileDialog As New OpenFileDialog With {
            .Filter = "HTML Files|*.html;*.htm",
            .Title = "Select an HTML File"
        }
        If openFileDialog.ShowDialog() = DialogResult.OK Then
            LetterEncoder_txtFilepath.Text = openFileDialog.FileName
            Using reader As New StreamReader(openFileDialog.FileName, Encoding.Default, True)
                originalHtml = reader.ReadToEnd()
            End Using
            LetterEncoder_Richtext.Text = originalHtml
            BntSave.Enabled = False
        End If
    End Sub

    Private Sub bntRest_Click(sender As Object, e As EventArgs) Handles bntRest.Click
        LetterEncoder_txtFilepath.ResetText()
        LetterEncoder_Richtext.ResetText()
        LetterEncoder_Richtext2.ResetText()
        BntEncoded.Enabled = True
    End Sub

    Private Sub BntEncoded_Click(sender As Object, e As EventArgs) Handles BntEncoded.Click
        If String.IsNullOrWhiteSpace(originalHtml) Then
            XtraMessageBox.Show("Please select an HTML file first!", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If
        ' 🔹 استثناء الـ CSS
        cssBlocks.Clear()
        originalHtml = Regex.Replace(originalHtml, "<style.*?>.*?</style>",
        Function(m)
            cssBlocks.Add(m.Value)
            Return "<style>"
        End Function, RegexOptions.Singleline Or RegexOptions.IgnoreCase)
        ' 🔹 استثناء العنوان <title>
        titleBlocks.Clear()
        originalHtml = Regex.Replace(originalHtml, "<title.*?>.*?</title>",
        Function(m)
            titleBlocks.Add(m.Value)
            Return "<title>"
        End Function, RegexOptions.Singleline Or RegexOptions.IgnoreCase)
        ' 🔹 البحث عن النصوص داخل الوسوم فقط، مع استثناء "style="
        Dim pattern As String = "(>)([^<>]+?)(<)"
        Dim encodedHtml As String = Regex.Replace(originalHtml, pattern,
        Function(m)
            Dim textOnly As String = WebUtility.HtmlDecode(m.Groups(2).Value.Trim())
            If String.IsNullOrEmpty(textOnly) Then Return m.Value
            Dim newText As New StringBuilder()
            Dim regexDynamic As String = String.Join("|", dynamicKeywords.Select(Function(k) Regex.Escape(k)))
            Dim parts As String() = Regex.Split(textOnly, $"({regexDynamic})")
            For Each part In parts
                newText.Append(If(dynamicKeywords.Contains(part), part, HideUnicode(part)))
            Next
            Return m.Groups(1).Value & newText.ToString() & m.Groups(3).Value
        End Function, RegexOptions.Singleline)
        ' 🔹 استرجاع CSS والعنوان كما كان
        For Each cssCode In cssBlocks
            encodedHtml = encodedHtml.Replace("<style>", cssCode)
        Next
        For Each titleCode In titleBlocks
            encodedHtml = encodedHtml.Replace("<title>", titleCode)
        Next
        originalHtml = encodedHtml
        LetterEncoder_Richtext2.Text = originalHtml
        XtraMessageBox.Show("Done Encrypter .HTML", "information", MessageBoxButtons.OK, MessageBoxIcon.Information)
        BntEncoded.Enabled = False
        BntSave.Enabled = True
    End Sub

    Private Sub BntSave_Click(sender As Object, e As EventArgs) Handles BntSave.Click
        If String.IsNullOrWhiteSpace(originalHtml) Then
            XtraMessageBox.Show("Please Select Your Letter First!", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If
        ' 🔹 فتح مربع حوار لحفظ الملف
        Using saveDialog As New SaveFileDialog()
            saveDialog.Title = "Save Encoded Letter"
            saveDialog.Filter = "HTML Files (*.html)|*.html"
            saveDialog.FileName = "EncodedLetter.html" ' اسم افتراضي
            If saveDialog.ShowDialog() = DialogResult.OK Then
                Dim filePath As String = saveDialog.FileName
                ' 🔹 حفظ المحتوى في الملف
                File.WriteAllText(filePath, originalHtml, Encoding.UTF8)
                ' 🔹 فتح المجلد الذي تم الحفظ فيه
                Dim folderPath As String = Path.GetDirectoryName(filePath)
                Process.Start("explorer.exe", folderPath)
                ' 🔹 إظهار رسالة تأكيد
                XtraMessageBox.Show($"The file has been successfully saved to: {filePath}", "Saved Successfully", MessageBoxButtons.OK, MessageBoxIcon.Information)
                ' 🔹 إعادة تعيين الحقول
                LetterEncoder_txtFilepath.ResetText()
                LetterEncoder_Richtext.ResetText()
                LetterEncoder_Richtext2.ResetText()
                BntEncoded.Enabled = True
            End If
        End Using
    End Sub
End Class
