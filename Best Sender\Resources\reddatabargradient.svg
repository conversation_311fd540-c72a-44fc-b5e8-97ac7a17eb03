﻿<?xml version='1.0' encoding='UTF-8'?>
<svg x="0px" y="0px" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" id="Red_Data_Bar_Gradient" style="enable-background:new 0 0 32 32">
  <style type="text/css">
	.Black{fill:#727272;}
	.Red{fill:#D11C1C;}
	.st0{opacity:0.75;}
	.st1{opacity:0.25;}
	.st2{opacity:0.84;}
	.st3{opacity:0.67;}
	.st4{opacity:0.5;}
	.st5{opacity:0.34;}
	.st6{opacity:0.17;}
</style>
  <path d="M31,0H1C0.4,0,0,0.4,0,1v30c0,0.6,0.4,1,1,1h30c0.6,0,1-0.4,1-1V1C32,0.4,31.6,0,31,0z M30,30H2v-8h28V30z   M30,20H2v-8h28V20z M30,10H2V2h28V10z" class="Black" />
  <g class="st0">
    <rect x="8" y="14" width="4" height="4" class="Red" />
  </g>
  <g class="st1">
    <rect x="16" y="14" width="4" height="4" class="Red" />
  </g>
  <path d="M8,8H4V4h4V8z M8,14H4v4h4V14z M8,24H4v4h4V24z" class="Red" />
  <g class="st2">
    <rect x="8" y="24" width="4" height="4" class="Red" />
  </g>
  <g class="st3">
    <rect x="12" y="24" width="4" height="4" class="Red" />
  </g>
  <g class="st4">
    <path d="M12,8H8V4h4V8z M16,14h-4v4h4V14z M20,24h-4v4h4V24z" class="Red" />
  </g>
  <g class="st5">
    <rect x="20" y="24" width="4" height="4" class="Red" />
  </g>
  <g class="st6">
    <rect x="24" y="24" width="4" height="4" class="Red" />
  </g>
</svg>