﻿Imports System.IO
Imports DevExpress.XtraBars.Docking2010
Public Class frmMaillist
    Private Sub frmEmailSorter_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Me.Opacity = 0
        trmfadein.Enabled = True
    End Sub
    Private Sub trmfadein_Tick(sender As Object, e As EventArgs) Handles trmfadein.Tick
        Me.Opacity = Me.Opacity + 0.02
        If Me.Opacity = 1 Then
            Me.Opacity = 99 / 100
            trmfadein.Enabled = False
            trmfadein.Stop()
        End If
    End Sub
    Private Sub SimpleButton2_Click(sender As Object, e As EventArgs) Handles SimpleButton2.Click
        txtList.SelectAll()
        txtList.Copy()
    End Sub
    Private Sub SimpleButton3_Click(sender As Object, e As EventArgs) Handles SimpleButton3.Click
        ' Get the path to the Desktop directory.
        Dim desktopPath As String = Environment.GetFolderPath(Environment.SpecialFolder.DesktopDirectory)
        ' Define the path for the "Best Defender" folder.
        Dim bestDefenderPath As String = path.Combine(desktopPath, "Best Sender")
        ' Check if the "Best Defender" folder exists. If not, create it.
        If Not Directory.Exists(bestDefenderPath) Then
            Directory.CreateDirectory(bestDefenderPath)
        End If
        ' Define the path for the "Email Sorter List" folder.
        Dim emailSorterListPath As String = path.Combine(bestDefenderPath, "Email Sorter List")
        ' Check if the "Email Sorter List" folder exists. If not, create it.
        If Not Directory.Exists(emailSorterListPath) Then
            Directory.CreateDirectory(emailSorterListPath)
        End If
        Dim filePath As String = Path.Combine(emailSorterListPath, GroupControl2.Text.Trim & ".txt")
        File.WriteAllText(filePath, txtList.Text())
        Dim argument As String = "/select," & filePath
        Process.Start("explorer.exe", argument)
        Process.Start(filePath)
        DevExpress.XtraEditors.XtraMessageBox.Show(GroupControl2.Text.Trim & " Successfully Saved...!", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
End Class