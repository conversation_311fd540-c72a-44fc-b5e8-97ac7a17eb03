﻿Imports System.Windows.Controls
Imports System.Windows.Forms.VisualStyles.VisualStyleElement
Imports System.Drawing
Imports System.IO
Public Class TagBestSender
    Private Sub btn1_Click(sender As Object, e As EventArgs) Handles btn1.Click
        Clipboard.SetText(T1.Text)
        T1.ForeColor = Color.FromArgb(34, 203, 121)
        T1.Text = "[-Email-] ✓ "
    End Sub
    Private Sub btn2_Click(sender As Object, e As EventArgs) Handles btn2.Click
        Clipboard.SetText(T2.Text)
        T2.ForeColor = Color.FromArgb(34, 203, 121)
        T2.Text = "[-Email64-] ✓ "
    End Sub
    Private Sub btn3_Click(sender As Object, e As EventArgs) Handles btn3.Click
        Clipboard.SetText(T3.Text)
        T3.ForeColor = Color.FromArgb(34, 203, 121)
        T3.Text = "[-UCase-] ✓ "
    End Sub
    Private Sub btn4_Click(sender As Object, e As EventArgs) Handles btn4.Click
        Clipboard.SetText(T4.Text)
        T4.ForeColor = Color.FromArgb(34, 203, 121)
        T4.Text = "[-Link-] ✓ "
    End Sub
    Private Sub btn5_Click(sender As Object, e As EventArgs) Handles btn5.Click
        Clipboard.SetText(T5.Text)
        T5.ForeColor = Color.FromArgb(34, 203, 121)
        T5.Text = "[-Logo-] ✓ "
    End Sub
    Private Sub btn6_Click(sender As Object, e As EventArgs) Handles btn6.Click
        Clipboard.SetText(T6.Text)
        T6.ForeColor = Color.FromArgb(34, 203, 121)
        T6.Text = "[-IP-] ✓ "
    End Sub
    Private Sub btn7_Click(sender As Object, e As EventArgs) Handles btn7.Click
        Clipboard.SetText(T7.Text)
        T7.ForeColor = Color.FromArgb(34, 203, 121)
        T7.Text = "[-Domain-] ✓ "
    End Sub
    Private Sub btn8_Click(sender As Object, e As EventArgs) Handles btn8.Click
        Clipboard.SetText(T8.Text)
        T8.ForeColor = Color.FromArgb(34, 203, 121)
        T8.Text = "[-Name-] ✓ "
    End Sub
    Private Sub btn9_Click(sender As Object, e As EventArgs) Handles btn9.Click
        Clipboard.SetText(T9.Text)
        T9.ForeColor = Color.FromArgb(34, 203, 121)
        T9.Text = "[-CompanyName-] ✓ "
    End Sub
    Private Sub btn10_Click(sender As Object, e As EventArgs) Handles btn10.Click
        Clipboard.SetText(T10.Text)
        T10.ForeColor = Color.FromArgb(34, 203, 121)
        T10.Text = "[-Date-] ✓ "
    End Sub
    Private Sub btn11_Click(sender As Object, e As EventArgs) Handles btn11.Click
        Clipboard.SetText(T11.Text)
        T11.ForeColor = Color.FromArgb(34, 203, 121)
        T11.Text = "[-Time-] ✓ "
    End Sub
    Private Sub btn12_Click(sender As Object, e As EventArgs) Handles btn12.Click
        Clipboard.SetText(T12.Text)
        T12.ForeColor = Color.FromArgb(34, 203, 121)
        T12.Text = "[-DateTomorrow-] ✓ "
    End Sub
    Private Sub btn13_Click(sender As Object, e As EventArgs) Handles btn13.Click
        Clipboard.SetText(T13.Text)
        T13.ForeColor = Color.FromArgb(34, 203, 121)
        T13.Text = "[-FakePhone-] ✓ "
    End Sub
    Private Sub btn14_Click(sender As Object, e As EventArgs) Handles btn14.Click
        Clipboard.SetText(T14.Text)
        T14.ForeColor = Color.FromArgb(34, 203, 121)
        T14.Text = "[-FakeEmail-] ✓ "
    End Sub
    Private Sub btn15_Click(sender As Object, e As EventArgs) Handles btn15.Click
        Clipboard.SetText(T15.Text)
        T15.ForeColor = Color.FromArgb(34, 203, 121)
        T15.Text = "[-RCh3-] ✓ "
    End Sub
    Private Sub btn16_Click(sender As Object, e As EventArgs) Handles btn16.Click
        Clipboard.SetText(T16.Text)
        T16.ForeColor = Color.FromArgb(34, 203, 121)
        T16.Text = "[-RCh4-] ✓ "
    End Sub
    Private Sub btn17_Click(sender As Object, e As EventArgs) Handles btn17.Click
        Clipboard.SetText(T17.Text)
        T17.ForeColor = Color.FromArgb(34, 203, 121)
        T17.Text = "[-RCh5-] ✓ "
    End Sub
    Private Sub btn18_Click(sender As Object, e As EventArgs) Handles btn18.Click
        Clipboard.SetText(T18.Text)
        T18.ForeColor = Color.FromArgb(34, 203, 121)
        T18.Text = "[-RCh6-] ✓ "
    End Sub
    Private Sub btn19_Click(sender As Object, e As EventArgs) Handles btn19.Click
        Clipboard.SetText(T19.Text)
        T19.ForeColor = Color.FromArgb(34, 203, 121)
        T19.Text = "[-RCh7-] ✓ "
    End Sub
    Private Sub btn20_Click(sender As Object, e As EventArgs) Handles btn20.Click
        Clipboard.SetText(T20.Text)
        T20.ForeColor = Color.FromArgb(34, 203, 121)
        T20.Text = "[-RN3-] ✓ "
    End Sub
    Private Sub btn21_Click(sender As Object, e As EventArgs) Handles btn21.Click
        Clipboard.SetText(T21.Text)
        T21.ForeColor = Color.FromArgb(34, 203, 121)
        T21.Text = "[-RN4-] ✓ "
    End Sub
    Private Sub btn22_Click(sender As Object, e As EventArgs) Handles btn22.Click
        Clipboard.SetText(T22.Text)
        T22.ForeColor = Color.FromArgb(34, 203, 121)
        T22.Text = "[-RN5-] ✓ "
    End Sub
    Private Sub btn23_Click(sender As Object, e As EventArgs) Handles btn23.Click
        Clipboard.SetText(T23.Text)
        T23.ForeColor = Color.FromArgb(34, 203, 121)
        T23.Text = "[-RN6-] ✓ "
    End Sub
    Private Sub btn24_Click(sender As Object, e As EventArgs) Handles btn24.Click
        Clipboard.SetText(T24.Text)
        T24.ForeColor = Color.FromArgb(34, 203, 121)
        T24.Text = "[-RN7-] ✓ "
    End Sub
    Private Sub btn25_Click(sender As Object, e As EventArgs) Handles btn25.Click
        Clipboard.SetText(T25.Text)
        T25.ForeColor = Color.FromArgb(34, 203, 121)
        T25.Text = "[-RandomBrowser-] ✓ "
    End Sub
    Private Sub btn26_Click(sender As Object, e As EventArgs) Handles btn26.Click
        Clipboard.SetText(T26.Text)
        T26.ForeColor = Color.FromArgb(34, 203, 121)
        T26.Text = "[-RCountry-] ✓ "
    End Sub
    Private Sub btn27_Click(sender As Object, e As EventArgs) Handles btn27.Click
        Clipboard.SetText(T27.Text)
        T27.ForeColor = Color.FromArgb(34, 203, 121)
        T27.Text = "[-NewYork-] ✓ "
    End Sub
    Private Sub btn28_Click(sender As Object, e As EventArgs) Handles btn28.Click
        Clipboard.SetText(T28.Text)
        T28.ForeColor = Color.FromArgb(34, 203, 121)
        T28.Text = "[-IPChina-] ✓ "
    End Sub
    Private Sub SimpleButton1_Click(sender As Object, e As EventArgs) Handles SimpleButton1.Click
        Clipboard.SetText(T29.Text)
        T29.ForeColor = Color.FromArgb(34, 203, 121)
        T29.Text = "[-QRCode-] ✓ "
    End Sub
    Private Sub btn29_Click(sender As Object, e As EventArgs) Handles btn29.Click
        Clipboard.SetText(T31.Text)
        T31.ForeColor = Color.FromArgb(34, 203, 121)
        T31.Text = "[-RN1-] ✓ "
    End Sub
    Private Sub btn30_Click(sender As Object, e As EventArgs) Handles btn30.Click
        Clipboard.SetText(T32.Text)
        T32.ForeColor = Color.FromArgb(34, 203, 121)
        T32.Text = "[-RN2-] ✓ "
    End Sub
    Private Sub btn31_Click(sender As Object, e As EventArgs) Handles btn31.Click
        Clipboard.SetText(T33.Text)
        T33.ForeColor = Color.FromArgb(34, 203, 121)
        T33.Text = "[-RN8-] ✓ "
    End Sub
    Private Sub btn32_Click(sender As Object, e As EventArgs) Handles btn32.Click
        Clipboard.SetText(T34.Text)
        T34.ForeColor = Color.FromArgb(34, 203, 121)
        T34.Text = "[-RN9-] ✓ "
    End Sub
    Private Sub btn33_Click(sender As Object, e As EventArgs) Handles btn33.Click
        Clipboard.SetText(T35.Text)
        T35.ForeColor = Color.FromArgb(34, 203, 121)
        T35.Text = "[-RN10-] ✓ "
    End Sub
    Private Sub btn34_Click(sender As Object, e As EventArgs) Handles btn34.Click
        Clipboard.SetText(T36.Text)
        T36.ForeColor = Color.FromArgb(34, 203, 121)
        T36.Text = "[-RN12-] ✓ "
    End Sub
    Private Sub btn35_Click(sender As Object, e As EventArgs) Handles btn35.Click
        Clipboard.SetText(T37.Text)
        T37.ForeColor = Color.FromArgb(34, 203, 121)
        T37.Text = "[-RCh1-] ✓ "
    End Sub
    Private Sub btn36_Click(sender As Object, e As EventArgs) Handles btn36.Click
        Clipboard.SetText(T38.Text)
        T38.ForeColor = Color.FromArgb(34, 203, 121)
        T38.Text = "[-RCh2-] ✓ "
    End Sub
    Private Sub btn37_Click(sender As Object, e As EventArgs) Handles btn37.Click
        Clipboard.SetText(T39.Text)
        T39.ForeColor = Color.FromArgb(34, 203, 121)
        T39.Text = "[-RCh8-] ✓ "
    End Sub
    Private Sub T1_MouseEnter(sender As Object, e As EventArgs) Handles T1.MouseEnter, btn1.MouseEnter
        Label28.Visible = True
        Label28.Text = "You can go to edit Letter" & vbCrLf &
                       "and you can put this crown [-Email-]" & vbCrLf &
                       "If you put this crown [-Email-]" & vbCrLf &
                       "inside your HTML and send it" & vbCrLf &
                       "to the email, it will automatically" & vbCrLf &
                       "fetch the email and insert it in" & vbCrLf &
                       "your message that arrives in the mail." & vbCrLf &
                       "For example, if you wrote in your message:" & vbCrLf &
                       "hello [-Email-]" & vbCrLf &
                       "it will appear in the email like this:" & vbCrLf &
                       "hello <EMAIL>"
    End Sub
    Private Sub T1_MouseLeave(sender As Object, e As EventArgs) Handles T1.MouseLeave, btn1.MouseLeave
        Label28.Visible = False
    End Sub
    Private Sub T2_MouseEnter(sender As Object, e As EventArgs) Handles T2.MouseEnter, btn2.MouseEnter
        Label28.Visible = True
        Label28.Text = "You can use this crown [-Email64-]" & vbCrLf &
                       "You can send it in this format:" & vbCrLf &
                       "www.Example.com/[-Email64-]" & vbCrLf &
                       "When you place it at the end of your link," & vbCrLf &
                       "it converts the recipient's email to Base64." & vbCrLf &
                       "Here's an example when someone receives your link:" & vbCrLf &
                       "www.Example.com/dGVzdEBvdXRsb29rLmNvbQ=="
    End Sub
    Private Sub T2_MouseLeave(sender As Object, e As EventArgs) Handles T2.MouseLeave, btn2.MouseLeave
        Label28.Visible = False
    End Sub
    Private Sub T3_MouseEnter(sender As Object, e As EventArgs) Handles T3.MouseEnter, btn3.MouseEnter
        Label28.Visible = True
        Label28.Text = "If you write this tag [-UCase-] in your message" & vbCrLf &
                       "For example:" & vbCrLf &
                       "hello [-UCase-]" & vbCrLf &
                       "And the recipient's name is liam" & vbCrLf &
                       "They will see this in the email:" & vbCrLf &
                       "hello Liam" & vbCrLf &
                       "[-UCase-] = Capitalizes the first letter of the name"
    End Sub
    Private Sub T3_MouseLeave(sender As Object, e As EventArgs) Handles T3.MouseLeave, btn3.MouseLeave
        Label28.Visible = False
    End Sub
    Private Sub T4_MouseEnter(sender As Object, e As EventArgs) Handles T4.MouseEnter, btn4.MouseEnter
        ' Display the English explanation
        Label28.Visible = True
        Label28.Text = "[-Link-]" & vbCrLf &
                   "You can place your link in Best Sender VIP" & vbCrLf &
                   "and put this tag [-Link-] in your message." & vbCrLf &
                   "Best Sender VIP will then send your link" & vbCrLf &
                   "to all users. See image for demonstration."
    End Sub
    Private Sub T4_MouseLeave(sender As Object, e As EventArgs) Handles T4.MouseLeave, btn4.MouseLeave
        ' Only hide the label when mouse leaves, keep PictureBox1 visible
        Label28.Visible = False
    End Sub
    Private Sub PictureBox1_Click(sender As Object, e As EventArgs)
        ' Open the link when PictureBox1 is clicked
        Try
            Process.Start("https://i.ibb.co/sdVwf9CN/Tag-Link-Image.png")
        Catch ex As Exception
            MessageBox.Show("Failed to open link: " & ex.Message)
        End Try
    End Sub
    Private Sub T6_MouseEnter(sender As Object, e As EventArgs) Handles T6.MouseEnter, btn6.MouseEnter
        Label28.Visible = True
        Label28.Text = "[-IP-]" & vbCrLf &
                       "If you place this tag in a message" & vbCrLf &
                       "For example:" & vbCrLf &
                       "Fake IP: [-IP-]" & vbCrLf &
                       "It will appear in the email like this:" & vbCrLf &
                       "Example:" & vbCrLf &
                       "Fake IP: ************"
    End Sub
    Private Sub T6_MouseLeave(sender As Object, e As EventArgs) Handles T6.MouseLeave, btn6.MouseLeave
        Label28.Visible = False
    End Sub
    Private Sub T7_MouseEnter(sender As Object, e As EventArgs) Handles T7.MouseEnter, btn7.MouseEnter
        Label28.Visible = True
        Label28.Text = "[-Domain-]" & vbCrLf &
                       "If you place this tag in Your Letter," & vbCrLf &
                       "it will automatically fetch the email domain." & vbCrLf &
                       "Example: <EMAIL> → example.com"
    End Sub
    Private Sub T7_MouseLeave(sender As Object, e As EventArgs) Handles T7.MouseLeave, btn7.MouseLeave
        Label28.Visible = False
    End Sub
    Private Sub T8_MouseEnter(sender As Object, e As EventArgs) Handles T8.MouseEnter, btn8.MouseEnter
        Label28.Visible = True
        Label28.Text = "[-Name-]" & vbCrLf &
                       "Automatically fetches the recipient's name" & vbCrLf &
                       "when placed in Your Letter." & vbCrLf &
                       "Example:" & vbCrLf &
                       "Hello: [-Name-]" & vbCrLf &
                       "Will appear in the email as:" & vbCrLf &
                       "Hello: Alexander"
    End Sub
    Private Sub T8_MouseLeave(sender As Object, e As EventArgs) Handles T8.MouseLeave, btn8.MouseLeave
        Label28.Visible = False
    End Sub
    Private Sub T9_MouseEnter(sender As Object, e As EventArgs) Handles T9.MouseEnter, btn9.MouseEnter
        Label28.Visible = True
        Label28.Text = "[-CompanyName-]" & vbCrLf &
                       "When placed in Your Letter:" & vbCrLf &
                       "[-CompanyName-] = Company Name" & vbCrLf &
                       "It automatically fetches the company name" & vbCrLf &
                       "in English - the official registered name" & vbCrLf &
                       "under which the company operates"
    End Sub
    Private Sub T9_MouseLeave(sender As Object, e As EventArgs) Handles T9.MouseLeave, btn9.MouseLeave
        Label28.Visible = False
    End Sub
    Private Sub T11_MouseEnter(sender As Object, e As EventArgs) Handles T11.MouseEnter, btn11.MouseEnter
        Label28.Visible = True
        Label28.Text = "[-Time-]" & vbCrLf &
                       "When placed in Your Letter," & vbCrLf &
                       "it will display the exact current time" & vbCrLf &
                       "in the message when viewed."
    End Sub
    Private Sub T12_MouseEnter(sender As Object, e As EventArgs) Handles T12.MouseEnter
        Label28.Visible = True
        Label28.Text = "[-DateTomorrow-]" & vbCrLf &
                   "When placed in Your Letter:" & vbCrLf &
                   "Today's date: " & DateTime.Now.ToString("dd-MM-yyyy") & vbCrLf &
                   "Tomorrow's date: " & DateTime.Now.AddDays(1).ToString("dd-MM-yyyy") & vbCrLf &
                   "Example:" & vbCrLf &
                   "If sent today (" & DateTime.Now.ToString("dd-MM-yyyy") & ")" & vbCrLf &
                   "It will show: " & DateTime.Now.AddDays(1).ToString("dd-MM-yyyy")
    End Sub
    Private Sub T12_MouseLeave(sender As Object, e As EventArgs) Handles T12.MouseLeave
        Label28.Visible = False
    End Sub
    Private Sub T11_MouseLeave(sender As Object, e As EventArgs) Handles T11.MouseLeave, btn11.MouseLeave
        Label28.Visible = False
    End Sub
    Private Sub T10_MouseEnter(sender As Object, e As EventArgs) Handles T10.MouseEnter, btn10.MouseEnter
        Label28.Visible = True
        Label28.Text = "[-Date-]" & vbCrLf &
                       "When placed in Your Letter," & vbCrLf &
                       "it will display the current date." & vbCrLf &
                       "Today's date will appear in the message." & vbCrLf &
                       "Example: " & DateTime.Now.ToString("dd-MM-yyyy")
    End Sub
    Private Sub T10_MouseLeave(sender As Object, e As EventArgs) Handles T10.MouseLeave, btn10.MouseLeave
        Label28.Visible = False
    End Sub
    Private Sub T29_MouseEnter(sender As Object, e As EventArgs) Handles T29.MouseEnter, SimpleButton1.MouseEnter
        Label28.Visible = True
        Label28.Text = "[-QRCode-]" & vbCrLf &
                       "If you want to convert your link to QR code:" & vbCrLf &
                       "1. Create a QR code" & vbCrLf &
                       "2. Place this tag [-QRCode-] in your message" & vbCrLf &
                       "3. The system will automatically convert it"
    End Sub
    Private Sub T29_MouseLeave(sender As Object, e As EventArgs) Handles T29.MouseLeave, SimpleButton1.MouseLeave
        Label28.Visible = False
        ' Don't hide PictureBox1 here - only hide on click
    End Sub
    Private Sub T14_MouseEnter(sender As Object, e As EventArgs) Handles T14.MouseEnter, btn14.MouseEnter
        Label28.Visible = True
        Label28.Text = "[-FakeEmail-]" & vbCrLf &
                       "When placed in Your Letter:" & vbCrLf &
                       "• Generates a fake email address" & vbCrLf &
                       "• Always creates a different email" & vbCrLf &
                       "  for each recipient" & vbCrLf &
                       "Example: user" & New Random().Next(1000, 9999) & "<EMAIL>"
    End Sub
    Private Sub T14_MouseLeave(sender As Object, e As EventArgs) Handles T14.MouseLeave, btn14.MouseLeave
        Label28.Visible = False
    End Sub
    Private Sub T37_MouseEnter(sender As Object, e As EventArgs) Handles T37.MouseEnter, btn35.MouseEnter
        Dim random As New Random()
        Dim chars As String = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
        Dim randomChars As Char() = New Char(7) {} ' 8 characters
        For i As Integer = 0 To randomChars.Length - 1
            randomChars(i) = chars(random.Next(chars.Length))
        Next
        Label28.Visible = True
        Label28.Text = "[-RCh1-]" & vbCrLf &
                   "When placed in Your Letter:" & vbCrLf &
                   "• Generates random alphanumeric code" & vbCrLf &
                   "• Example: " & New String(randomChars) & vbCrLf &
                   "• Different each time it's used" & vbCrLf &
                   "• Contains letters and numbers"
    End Sub
    Private Sub T37_MouseLeave(sender As Object, e As EventArgs) Handles T37.MouseLeave, btn35.MouseLeave
        Label28.Visible = False
    End Sub
    Private Sub T38_MouseEnter(sender As Object, e As EventArgs) Handles T38.MouseEnter, btn36.MouseEnter
        Dim random As New Random()
        Dim numbers As String = "0123456789"
        Dim letters As String = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz"
        ' Generate 1 number + 1 letter
        Dim number As Char = numbers(random.Next(numbers.Length))
        Dim letter As Char = letters(random.Next(letters.Length))
        Dim code As String = $"{number}{letter}" ' e.g., "3A", "7b"
        Label28.Visible = True
        Label28.Text = "[-RCh2-]" & vbCrLf &
                   "When placed in Your Letter:" & vbCrLf &
                   "• Generates 1 number + 1 letter" & vbCrLf &
                   "• Example: " & code & vbCrLf &
                   "• Format: Number-Letter (e.g., 3A, 7b)" & vbCrLf &
                   "• Different each time it's used"
    End Sub
    ' Helper function to generate random alphanumeric code
    Private Function GenerateRandomCode(rnd As Random, charSet As String, length As Integer) As String
        Dim sb As New System.Text.StringBuilder()
        For i As Integer = 1 To length
            sb.Append(charSet(rnd.Next(charSet.Length)))
        Next
        Return sb.ToString()
    End Function
    Private Sub T38_MouseLeave(sender As Object, e As EventArgs) Handles T38.MouseLeave, btn36.MouseLeave
        Label28.Visible = False
    End Sub
    Private Sub T20_MouseEnter(sender As Object, e As EventArgs) Handles T20.MouseEnter, btn20.MouseEnter
        Dim random As New Random()
        Dim randomNumber As Integer = random.Next(100, 999) ' Generates 3-digit number (100-999)
        Label28.Visible = True
        Label28.Text = "[-RN3-]" & vbCrLf &
                       "When placed in Your Letter:" & vbCrLf &
                       "• Generates a 3-digit random number" & vbCrLf &
                       "• Example: " & randomNumber.ToString() & vbCrLf &
                       "• Different each time it's used"
    End Sub
    Private Sub T20_MouseLeave(sender As Object, e As EventArgs) Handles T20.MouseLeave, btn20.MouseLeave
        Label28.Visible = False
    End Sub
    Private Sub T21_MouseEnter(sender As Object, e As EventArgs) Handles T21.MouseEnter, btn21.MouseEnter
        Dim random As New Random()
        Dim randomNumber As Integer = random.Next(1000, 9999) ' Generates 4-digit number (1000-9999)
        Label28.Visible = True
        Label28.Text = "[-RN4-]" & vbCrLf &
                       "When placed in Your Letter:" & vbCrLf &
                       "• Generates a 4-digit random number" & vbCrLf &
                       "• Example: " & randomNumber.ToString() & vbCrLf &
                       "• Different each time it's used"
    End Sub
    Private Sub T21_MouseLeave(sender As Object, e As EventArgs) Handles T21.MouseLeave, btn21.MouseLeave
        Label28.Visible = False
    End Sub
    Private Sub T22_MouseEnter(sender As Object, e As EventArgs) Handles T22.MouseEnter, btn22.MouseEnter
        Dim random As New Random()
        Dim randomNumber As Integer = random.Next(10000, 99999) ' Generates 5-digit number (10000-99999)
        Label28.Visible = True
        Label28.Text = "[-RN5-]" & vbCrLf &
                       "When placed in Your Letter:" & vbCrLf &
                       "• Generates a 5-digit random number" & vbCrLf &
                       "• Example: " & randomNumber.ToString() & vbCrLf &
                       "• Different each time it's used"
    End Sub
    Private Sub T22_MouseLeave(sender As Object, e As EventArgs) Handles T22.MouseLeave, btn22.MouseLeave
        Label28.Visible = False
    End Sub
    Private Sub T23_MouseEnter(sender As Object, e As EventArgs) Handles T23.MouseEnter, btn23.MouseEnter
        Dim random As New Random()
        Dim randomNumber As Integer = random.Next(100000, 999999) ' Generates 6-digit number (100000-999999)
        Label28.Visible = True
        Label28.Text = "[-RN6-]" & vbCrLf &
                       "When placed in Your Letter:" & vbCrLf &
                       "• Generates a 6-digit random code" & vbCrLf &
                       "• Example: " & randomNumber.ToString() & vbCrLf &
                       "• Always creates a unique code"
    End Sub
    Private Sub T23_MouseLeave(sender As Object, e As EventArgs) Handles T23.MouseLeave, btn23.MouseLeave
        Label28.Visible = False
    End Sub
    Private Sub T24_MouseEnter(sender As Object, e As EventArgs) Handles T24.MouseEnter, btn24.MouseEnter
        Dim random As New Random()
        Dim randomNumber As Integer = random.Next(1000000, 9999999) ' Generates 7-digit number (1000000-9999999)
        Label28.Visible = True
        Label28.Text = "[-RN7-]" & vbCrLf &
                       "When placed in Your Letter:" & vbCrLf &
                       "• Generates a 7-digit random code" & vbCrLf &
                       "• Example: " & randomNumber.ToString("D7") & vbCrLf &
                       "• Always creates a unique code" & vbCrLf &
                       "• Format: Exactly 7 digits"
    End Sub
    Private Sub T24_MouseLeave(sender As Object, e As EventArgs) Handles T24.MouseLeave, btn24.MouseLeave
        Label28.Visible = False
    End Sub
    Private Sub T33_MouseEnter(sender As Object, e As EventArgs) Handles T33.MouseEnter, btn31.MouseEnter
        Dim random As New Random()
        Dim randomNumber As Long = random.Next(10000000, 99999999) ' 8-digit number (10000000-99999999)
        Label28.Visible = True
        Label28.Text = "[-RN8-]" & vbCrLf &
                       "When placed in Your Letter:" & vbCrLf &
                       "• Generates an 8-digit random code" & vbCrLf &
                       "• Example: " & randomNumber.ToString("D8") & vbCrLf &
                       "• Guaranteed unique for each recipient" & vbCrLf &
                       "• Perfect for verification codes"
    End Sub
    Private Sub T33_MouseLeave(sender As Object, e As EventArgs) Handles T33.MouseLeave, btn31.MouseLeave
        Label28.Visible = False
    End Sub
    Private Sub T34_MouseEnter(sender As Object, e As EventArgs) Handles T34.MouseEnter, btn32.MouseEnter
        Dim random As New Random()
        ' Generate first digit (1-9) and remaining 8 digits (0-9) to ensure proper 9-digit number
        Dim firstDigit As Integer = random.Next(1, 10) ' Avoid leading zero
        Dim remainingDigits As Integer = random.Next(0, 100000000) ' 8 digits
        Dim randomNumber As String = firstDigit.ToString() & remainingDigits.ToString("D8")
        Label28.Visible = True
        Label28.Text = "[-RN9-]" & vbCrLf &
                       "When placed in Your Letter:" & vbCrLf &
                       "• Generates 9-digit secure random number" & vbCrLf &
                       "• Example: " & randomNumber & vbCrLf &
                       "• Cryptographic-grade randomness" & vbCrLf &
                       "• Ideal for transaction IDs"
    End Sub
    Private Sub T34_MouseLeave(sender As Object, e As EventArgs) Handles T34.MouseLeave, btn32.MouseLeave
        Label28.Visible = False
    End Sub
    Private Sub T35_MouseEnter(sender As Object, e As EventArgs) Handles T35.MouseEnter, btn33.MouseEnter
        Dim random As New Random()
        ' Generate first digit (1-9) and remaining 9 digits (0-9)
        Dim firstDigit As Integer = random.Next(1, 10) ' Ensures no leading zero
        Dim remainingDigits As Long = random.Next(0, 1000000000) ' 9 digits
        Dim randomNumber As String = firstDigit.ToString() & remainingDigits.ToString("D9")
        Label28.Visible = True
        Label28.Text = "[-RN10-]" & vbCrLf &
                       "When placed in Your Letter:" & vbCrLf &
                       "• Generates 10-digit random number" & vbCrLf &
                       "• Example: " & randomNumber & vbCrLf &
                       "• Format: Exactly 10 digits" & vbCrLf &
                       "• No letters or symbols" & vbCrLf &
                       "• Pure numeric output"
    End Sub
    Private Sub T35_MouseLeave(sender As Object, e As EventArgs) Handles T35.MouseLeave, btn33.MouseLeave
        Label28.Visible = False
    End Sub
    Private Sub T36_MouseEnter(sender As Object, e As EventArgs) Handles T36.MouseEnter, btn34.MouseEnter
        Dim random As New Random()
        ' Generate first digit (1-9) and remaining 11 digits (0-9)
        Dim firstDigit As Integer = random.Next(1, 10) ' Ensures no leading zero
        Dim remainingPart1 As Long = random.Next(0, 100000000) ' First 8 digits
        Dim remainingPart2 As Integer = random.Next(0, 1000) ' Last 3 digits
        Dim randomNumber As String = $"{firstDigit}{remainingPart1.ToString("D8")}{remainingPart2.ToString("D3")}"
        Label28.Visible = True
        Label28.Text = "[-RN12-]" & vbCrLf &
                       "When placed in Your Letter:" & vbCrLf &
                       "• Generates 12-digit random number" & vbCrLf &
                       "• Example: " & randomNumber & vbCrLf &
                       "• Format: Exactly 12 digits" & vbCrLf &
                       "• Numeric only (0-9)" & vbCrLf &
                       "• Banking-grade security"
    End Sub
    Private Sub T36_MouseLeave(sender As Object, e As EventArgs) Handles T36.MouseLeave, btn34.MouseLeave
        Label28.Visible = False
    End Sub
    Private Sub T31_MouseEnter(sender As Object, e As EventArgs) Handles T31.MouseEnter, btn29.MouseEnter
        Dim random As New Random()
        Dim randomDigit As Integer = random.Next(0, 10) ' Generates 0-9
        Label28.Visible = True
        Label28.Text = "[-RN1-]" & vbCrLf &
                       "When placed in Your Letter:" & vbCrLf &
                       "• Generates a single random digit" & vbCrLf &
                       "• Example: " & randomDigit.ToString() & vbCrLf &
                       "• Range: 0 to 9" & vbCrLf &
                       "• Pure numeric output"
    End Sub
    Private Sub T31_MouseLeave(sender As Object, e As EventArgs) Handles T31.MouseLeave, btn29.MouseLeave
        Label28.Visible = False
    End Sub
    Private Sub T32_MouseEnter(sender As Object, e As EventArgs) Handles T32.MouseEnter, btn30.MouseEnter
        Dim random As New Random()
        Dim digit1 As Integer = random.Next(0, 10) ' 0-9
        Dim digit2 As Integer = random.Next(0, 10) ' 0-9
        Label28.Visible = True
        Label28.Text = "[-RN2-]" & vbCrLf &
                       "When placed in Your Letter:" & vbCrLf &
                       "• Generates two random digits" & vbCrLf &
                       "• Example: " & digit1 & digit2 & vbCrLf &
                       "• Range: 00 to 99" & vbCrLf &
                       "• Numeric only"
    End Sub
    Private Sub T32_MouseLeave(sender As Object, e As EventArgs) Handles T32.MouseLeave, btn30.MouseLeave
        Label28.Visible = False
    End Sub
    Private Sub T15_MouseEnter(sender As Object, e As EventArgs) Handles T15.MouseEnter, btn15.MouseEnter
        Dim random As New Random()
        Dim letters As String = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
        Dim numbers As String = "0123456789"
        Dim pattern As Integer = random.Next(0, 2) ' 0 أو 1 لاختيار النمط عشوائياً
        Dim result As New System.Text.StringBuilder()
        ' النمط الأول: حرفين ورقم (مثال: AB5)
        If pattern = 0 Then
            result.Append(letters(random.Next(letters.Length))) ' حرف أول
            result.Append(letters(random.Next(letters.Length))) ' حرف ثاني
            result.Append(numbers(random.Next(numbers.Length))) ' رقم
            ' النمط الثاني: رقمين وحرف (مثال: 42C)
        Else
            result.Append(numbers(random.Next(numbers.Length))) ' رقم أول
            result.Append(numbers(random.Next(numbers.Length))) ' رقم ثاني
            result.Append(letters(random.Next(letters.Length))) ' حرف
        End If
        Label28.Visible = True
        Label28.Text = "[-RCh3-]" & vbCrLf &
                   "When placed in Your Letter:" & vbCrLf &
                   "• Generates random code (2 letters + 1 number OR 2 numbers + 1 letter)" & vbCrLf &
                   "• Example: " & result.ToString() & vbCrLf &
                   "• Contains letters and numbers" & vbCrLf &
                   "• Different each time it's used"
    End Sub
    Private Sub T15_MouseLeave(sender As Object, e As EventArgs) Handles T15.MouseLeave, btn15.MouseLeave
        Label28.Visible = False
    End Sub
    Private Sub T16_MouseEnter(sender As Object, e As EventArgs) Handles T16.MouseEnter, btn16.MouseEnter
        Dim random As New Random()
        Dim numbers As String = "0123456789"
        Dim letters As String = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz"
        ' Generate 2 random numbers and 2 random letters
        Dim num1 As Char = numbers(random.Next(numbers.Length))
        Dim num2 As Char = numbers(random.Next(numbers.Length))
        Dim letter1 As Char = letters(random.Next(letters.Length))
        Dim letter2 As Char = letters(random.Next(letters.Length))
        ' Combine in the format: 2 numbers + 2 letters (e.g., 2g6e)
        Dim code As String = $"{num1}{letter1}{num2}{letter2}"
        Label28.Visible = True
        Label28.Text = "[-RCh4-]" & vbCrLf &
                   "When placed in Your Letter:" & vbCrLf &
                   "• Generates 2 numbers + 2 letters" & vbCrLf &
                   "• Example: " & code & vbCrLf &
                   "• Format: Number-Letter-Number-Letter" & vbCrLf &
                   "• Always creates unique combinations"
    End Sub
    Private Sub T16_MouseLeave(sender As Object, e As EventArgs) Handles T16.MouseLeave, btn16.MouseLeave
        Label28.Visible = False
    End Sub
    Private Sub T17_MouseEnter(sender As Object, e As EventArgs) Handles T17.MouseEnter, btn17.MouseEnter
        Dim random As New Random()
        Dim chars As String = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
        Dim code As New System.Text.StringBuilder()
        ' Generate 5 random alphanumeric characters
        For i As Integer = 1 To 5
            code.Append(chars(random.Next(chars.Length)))
        Next
        Label28.Visible = True
        Label28.Text = "[-RCh5-]" & vbCrLf &
                       "When placed in Your Letter:" & vbCrLf &
                       "• Generates 5-character random code" & vbCrLf &
                       "• Example: " & code.ToString() & vbCrLf &
                       "• Contains letters and numbers" & vbCrLf &
                       "• Different each time it's used" & vbCrLf &
                       "• Format: Alphanumeric mix"
    End Sub
    Private Sub T17_MouseLeave(sender As Object, e As EventArgs) Handles T17.MouseLeave, btn17.MouseLeave
        Label28.Visible = False
    End Sub
    Private Sub T18_MouseEnter(sender As Object, e As EventArgs) Handles T18.MouseEnter, btn18.MouseEnter
        Dim random As New Random()
        Dim chars As String = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
        Dim code As New System.Text.StringBuilder()
        ' Generate 6 random alphanumeric characters
        For i As Integer = 1 To 6
            code.Append(chars(random.Next(chars.Length)))
        Next
        Label28.Visible = True
        Label28.Text = "[-RCh6-]" & vbCrLf &
                       "When placed in Your Letter:" & vbCrLf &
                       "• Generates 6-character random code" & vbCrLf &
                       "• Example: " & code.ToString() & vbCrLf &
                       "• Contains letters (A-Z, a-z) and numbers (0-9)" & vbCrLf &
                       "• Different each time it's used" & vbCrLf &
                       "• Ideal for verification codes"
    End Sub
    Private Sub T18_MouseLeave(sender As Object, e As EventArgs) Handles T18.MouseLeave, btn18.MouseLeave
        Label28.Visible = False
    End Sub
    Private Sub T19_MouseEnter(sender As Object, e As EventArgs) Handles T19.MouseEnter, btn19.MouseEnter
        Dim random As New Random()
        Dim chars As String = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
        Dim code As New System.Text.StringBuilder()
        ' Generate 7-character code with at least 1 number and 1 letter
        Dim hasNumber As Boolean = False
        Dim hasLetter As Boolean = False
        While Not (hasNumber AndAlso hasLetter)
            code.Clear()
            hasNumber = False
            hasLetter = False
            For i As Integer = 1 To 7
                Dim c As Char = chars(random.Next(chars.Length))
                code.Append(c)
                If Char.IsDigit(c) Then
                    hasNumber = True
                Else
                    hasLetter = True
                End If
            Next
        End While
        Label28.Visible = True
        Label28.Text = "[-RCh7-]" & vbCrLf &
                       "When placed in Your Letter:" & vbCrLf &
                       "• Generates 7-character secure code" & vbCrLf &
                       "• Example: " & code.ToString() & vbCrLf &
                       "• Contains both letters and numbers" & vbCrLf &
                       "• Guaranteed at least 1 number and 1 letter" & vbCrLf &
                       "• Different every time"
    End Sub
    Private Sub T19_MouseLeave(sender As Object, e As EventArgs) Handles T19.MouseLeave, btn19.MouseLeave
        Label28.Visible = False
    End Sub
    Private Sub T39_MouseEnter(sender As Object, e As EventArgs) Handles T39.MouseEnter, btn37.MouseEnter
        Dim random As New Random()
        Dim chars As String = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
        Dim code As New System.Text.StringBuilder(8)
        ' Generate 8-character code with guaranteed complexity
        Dim hasUpper As Boolean = False
        Dim hasLower As Boolean = False
        Dim hasNumber As Boolean = False
        Do
            code.Clear()
            hasUpper = False
            hasLower = False
            hasNumber = False
            For i As Integer = 1 To 8
                Dim c As Char = chars(random.Next(chars.Length))
                code.Append(c)
                If Char.IsUpper(c) Then hasUpper = True
                If Char.IsLower(c) Then hasLower = True
                If Char.IsDigit(c) Then hasNumber = True
            Next
        Loop Until hasUpper AndAlso hasLower AndAlso hasNumber ' Ensures all character types
        Label28.Visible = True
        Label28.Text = "[-RCh8-]" & vbCrLf &
                       "When placed in Your Letter:" & vbCrLf &
                       "• Generates 8-character complex code" & vbCrLf &
                       "• Example: " & code.ToString() & vbCrLf &
                       "• Contains:" & vbCrLf &
                       "  - Uppercase letters (A-Z)" & vbCrLf &
                       "  - Lowercase letters (a-z)" & vbCrLf &
                       "  - Numbers (0-9)" & vbCrLf &
                       "• Guaranteed all character types"
    End Sub
    Private Sub T39_MouseLeave(sender As Object, e As EventArgs) Handles T39.MouseLeave, btn37.MouseLeave
        Label28.Visible = False
    End Sub
    Private Sub T25_MouseEnter(sender As Object, e As EventArgs) Handles T25.MouseEnter, btn25.MouseEnter
        Dim random As New Random()
        Dim browsers() As String = {
            "Chrome",
            "Firefox",
            "Edge",
            "Safari",
            "Opera",
            "Brave",
            "Vivaldi",
            "Internet Explorer",
            "Tor Browser",
            "DuckDuckGo"
        }
        Dim randomBrowser As String = browsers(random.Next(browsers.Length))
        Label28.Visible = True
        Label28.Text = "[-RandomBrowser-]" & vbCrLf &
                       "When placed in Your Letter:" & vbCrLf &
                       "• Generates a random browser name" & vbCrLf &
                       "• Example: " & randomBrowser & vbCrLf &
                       "• Common browsers only" & vbCrLf &
                       "• Different each time it's used"
    End Sub
    Private Sub T25_MouseLeave(sender As Object, e As EventArgs) Handles T25.MouseLeave, btn25.MouseLeave
        Label28.Visible = False
    End Sub
    Private Sub T26_MouseEnter(sender As Object, e As EventArgs) Handles T26.MouseEnter, btn26.MouseEnter
        Dim random As New Random()
        Dim countries() As String = {
            "United States", "Canada", "United Kingdom", "Australia", "Germany",
            "France", "Japan", "Brazil", "India", "South Africa",
            "Mexico", "Italy", "Spain", "China", "Russia",
            "Egypt", "Argentina", "Sweden", "Norway", "Singapore"
        }
        Dim randomCountry As String = countries(random.Next(countries.Length))
        Label28.Visible = True
        Label28.Text = "[-RCountry-]" & vbCrLf &
                       "When placed in Your Letter:" & vbCrLf &
                       "• Generates a random country name" & vbCrLf &
                       "• Example: " & randomCountry & vbCrLf &
                       "• Includes diverse global locations" & vbCrLf &
                       "• Different each time it's used"
    End Sub
    Private Sub T26_MouseLeave(sender As Object, e As EventArgs) Handles T26.MouseLeave, btn26.MouseLeave
        Label28.Visible = False
    End Sub
    Private Sub T27_MouseEnter(sender As Object, e As EventArgs) Handles T27.MouseEnter, btn27.MouseEnter
        Dim random As New Random()
        Dim nyLocations() As String = {
            "Manhattan", "Brooklyn", "Queens",
            "The Bronx", "Staten Island", "Times Square",
            "Central Park", "Wall Street", "Broadway"
        }
        Dim randomLocation As String = nyLocations(random.Next(nyLocations.Length))
        Label28.Visible = True
        Label28.Text = "[-NewYork-]" & vbCrLf &
                       "When placed in Your Letter:" & vbCrLf &
                       "• Displays New York location" & vbCrLf &
                       "• Example: " & randomLocation & vbCrLf &
                       "• Includes famous NYC areas" & vbCrLf &
                       "• Different each time it's used"
    End Sub
    Private Sub T27_MouseLeave(sender As Object, e As EventArgs) Handles T27.MouseLeave, btn27.MouseLeave
        Label28.Visible = False
    End Sub
    Private Sub T28_MouseEnter(sender As Object, e As EventArgs) Handles T28.MouseEnter, btn28.MouseEnter
        Dim random As New Random()
        ' Common China IP ranges (first 2 octets)
        Dim chinaIPPrefixes() As String = {"36.110.", "39.128.", "42.96.", "58.240.", "61.232."}
        ' Generate random Chinese IP address
        Dim ipPrefix As String = chinaIPPrefixes(random.Next(chinaIPPrefixes.Length))
        Dim ipAddress As String = $"{ipPrefix}{random.Next(1, 255)}.{random.Next(1, 255)}"
        Label28.Visible = True
        Label28.Text = "[-IPChina-]" & vbCrLf &
                       "When placed in Your Letter:" & vbCrLf &
                       "• Generates Chinese IP address" & vbCrLf &
                       "• Example: " & ipAddress & vbCrLf &
                       "• Uses real China IP ranges" & vbCrLf &
                       "• Different each time it's used" & vbCrLf &
                       "• Format: 36.110.XXX.XXX"
    End Sub
    Private Sub T28_MouseLeave(sender As Object, e As EventArgs) Handles T28.MouseLeave, btn28.MouseLeave
        Label28.Visible = False
    End Sub
    Private Sub T13_MouseEnter(sender As Object, e As EventArgs) Handles T13.MouseEnter, btn13.MouseEnter
        Dim random As New Random()
        ' Generate random phone number formats
        Dim formats() As String = {
            "(XXX) XXX-XXXX",
            "XXX-XXX-XXXX",
            "+1 XXX-XXX-XXXX",
            "XXX.XXX.XXXX"
        }
        ' Select random format and generate numbers
        Dim format As String = formats(random.Next(formats.Length))
        Dim phoneNumber As New System.Text.StringBuilder()
        For Each c As Char In format
            If c = "X"c Then
                phoneNumber.Append(random.Next(0, 10))
            Else
                phoneNumber.Append(c)
            End If
        Next
        Label28.Visible = True
        Label28.Text = "[-FakePhone-]" & vbCrLf &
                       "When placed in Your Letter:" & vbCrLf &
                       "• Generates random phone number" & vbCrLf &
                       "• Example: " & phoneNumber.ToString() & vbCrLf &
                       "• Multiple format variations" & vbCrLf &
                       "• Different each time it's used" & vbCrLf &
                       "• Not real phone numbers"
    End Sub
    Private Sub T13_MouseLeave(sender As Object, e As EventArgs) Handles T13.MouseLeave, btn13.MouseLeave
        Label28.Visible = False
    End Sub
End Class