﻿Imports System.IO
Imports DevExpress.XtraEditors
Imports System.Text
Imports DevExpress.XtraBars
Imports DevExpress.XtraBars.Ribbon
Imports System.Drawing.Drawing2D
Imports System.Diagnostics
Imports System.Text.RegularExpressions
Imports System.Runtime.CompilerServices
Imports System.Threading.Tasks
Imports System.Threading
Imports System.ComponentModel
Imports System.Windows.Forms

''' <summary>
''' نموذج Zero Password لاستخراج البريد الإلكتروني من الملفات النصية
''' مطور بخبرة 20 عام في VB.NET و Windows Forms
''' </summary>
Public Class frmZeroPass
    Public Property RelatedRibbonPage As RibbonPage

#Region "Variables and Controls Declaration"

    ' متغيرات التحكم في العمليات
    Private isCancelled As Boolean = False
    Private extractedEmailCount As Integer = 0
    Private fileContent As String = ""

    ' متغيرات لحفظ حالة التوقف والاستكمال
    Private isPaused As Boolean = False
    Private lastProcessedPosition As Integer = 0
    Private tempExtractedEmails As New List(Of String)
    Private processingContent As String = ""

    ' العناصر معرفة في Designer - لا نحتاج لإعادة تعريفها

    ' عناصر إضافية
    Private WithEvents OpenFileDialog1 As OpenFileDialog

    ' BackgroundWorker2 لاستخراج البريد (BackgroundWorker1 معرف في Designer)
    Private WithEvents BackgroundWorker2 As BackgroundWorker

    ' قائمة البريد المستخرج
    Private extractedEmails As New List(Of String)

#End Region

#Region "Form Load and Initialization"

    Private Sub frmZeroPass_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            frmMain.BntStop.Enabled = False
            ' تهيئة النموذج
            InitializeForm()

            ' إنشاء عناصر التحكم
            CreateControls()

            ' تطبيق تصميم Binance
            ApplyBinanceDesign()

            ' تهيئة BackgroundWorkers
            InitializeBackgroundWorkers()

            ' تهيئة الحالة الأولية
            InitializeInitialState()

        Catch ex As Exception
            XtraMessageBox.Show($"Error initializing form: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub InitializeForm()
        ' إعدادات النموذج الأساسية - احترام إعدادات Designer
        Me.Text = "Zero Password - Email Extractor"
        Me.StartPosition = FormStartPosition.CenterScreen

        ' تحسين الأداء
        Me.SetStyle(ControlStyles.AllPaintingInWmPaint Or
                   ControlStyles.UserPaint Or
                   ControlStyles.DoubleBuffer Or
                   ControlStyles.ResizeRedraw, True)
        Me.UpdateStyles()
    End Sub

    Private Sub CreateControls()
        ' العناصر معرفة في Designer - فقط نحدد الخصائص الأساسية

        SetupDisplayControls()
        CreateFileDialogs()
    End Sub



    Private Sub SetupDisplayControls()
        ' العناصر معرفة في Designer - فقط نحدد الخصائص
        Try
            If MemoEdit1 IsNot Nothing Then
                With MemoEdit1.Properties
                    .ReadOnly = False
                    .ScrollBars = ScrollBars.Both
                    .WordWrap = True
                End With
            End If

            If MemoEdit2 IsNot Nothing Then
                With MemoEdit2.Properties
                    .ReadOnly = True
                    .ScrollBars = ScrollBars.Both
                    .WordWrap = False
                End With
            End If

            If ProgressPanel1 IsNot Nothing Then
                ProgressPanel1.Visible = False
                ProgressPanel1.Text = "Processing..."
                ProgressPanel1.Description = "Please wait while extracting emails"
            End If

            If lblNumberofmailextracted IsNot Nothing Then
                lblNumberofmailextracted.Text = "📧 Extracted Emails: 0"
                lblNumberofmailextracted.ForeColor = Color.FromArgb(240, 185, 11)
                lblNumberofmailextracted.Font = New Font("Segoe UI", 12, FontStyle.Bold)
            End If
        Catch ex As Exception
            Debug.WriteLine($"Error setting up display controls: {ex.Message}")
        End Try
    End Sub

    Private Sub CreateFileDialogs()
        OpenFileDialog1 = New OpenFileDialog With {
            .Title = "Select Text File",
            .Filter = "Text Files (*.txt)|*.txt|All Files (*.*)|*.*",
            .InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)
        }
    End Sub

    Private Sub InitializeBackgroundWorkers()
        ' BackgroundWorker1 معرف في Designer - فقط نحدد الخصائص
        If BackgroundWorker1 IsNot Nothing Then
            BackgroundWorker1.WorkerReportsProgress = True
            BackgroundWorker1.WorkerSupportsCancellation = True
        End If

        ' BackgroundWorker2 لاستخراج البريد
        BackgroundWorker2 = New BackgroundWorker With {
            .WorkerReportsProgress = True,
            .WorkerSupportsCancellation = True
        }
    End Sub

    Private Sub InitializeInitialState()
        ' تعيين الحالة الأولية
        extractedEmailCount = 0
        extractedEmails.Clear()
        isCancelled = False
        isPaused = False
        lastProcessedPosition = 0
        tempExtractedEmails.Clear()
        processingContent = ""

        ' تحديث العرض
        UpdateEmailCount()
        HideProgressPanel()
    End Sub

#End Region

#Region "Binance Design Application"

    Private Sub ApplyBinanceDesign()
        ' ألوان Binance الرسمية
        Dim binanceGold As Color = Color.FromArgb(240, 185, 11)
        Dim binanceGreen As Color = Color.FromArgb(14, 203, 129)
        Dim binanceRed As Color = Color.FromArgb(246, 70, 93)
        Dim darkBackground As Color = Color.FromArgb(14, 16, 20)
        Dim cardBackground As Color = Color.FromArgb(24, 26, 32)
        Dim textWhite As Color = Color.FromArgb(234, 236, 239)

        Try
            ' تطبيق التصميم على النموذج
            Me.BackColor = darkBackground


            ' تطبيق التصميم على MemoEdit
            If MemoEdit1 IsNot Nothing Then ApplyMemoEditStyle(MemoEdit1, cardBackground, textWhite)
            If MemoEdit2 IsNot Nothing Then ApplyMemoEditStyle(MemoEdit2, cardBackground, binanceGreen)

            ' تطبيق التصميم على Label
            If lblNumberofmailextracted IsNot Nothing Then
                lblNumberofmailextracted.BackColor = Color.Transparent
                lblNumberofmailextracted.ForeColor = binanceGold
                lblNumberofmailextracted.Font = New Font("Segoe UI", 12, FontStyle.Bold)
            End If

            ' تطبيق التصميم على ProgressPanel1 - استخدام اللون الطبيعي
            If ProgressPanel1 IsNot Nothing Then
                ProgressPanel1.LookAndFeel.UseDefaultLookAndFeel = True
                ProgressPanel1.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Skin
            End If

        Catch ex As Exception
            Debug.WriteLine($"Error applying Binance design: {ex.Message}")
        End Try
    End Sub

    Private Sub ApplyButtonStyle(btn As DevExpress.XtraEditors.SimpleButton, color As Color)
        With btn
            .Appearance.BackColor = color
            .Appearance.ForeColor = Color.Black
            .Appearance.Font = New Font("Segoe UI", 9, FontStyle.Bold)
            .Appearance.Options.UseBackColor = True
            .Appearance.Options.UseForeColor = True
            .Appearance.Options.UseFont = True
            .LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.UltraFlat
            .LookAndFeel.UseDefaultLookAndFeel = False
        End With
    End Sub

    Private Sub ApplyMemoEditStyle(memo As DevExpress.XtraEditors.MemoEdit, backColor As Color, foreColor As Color)
        Try
            With memo.Properties.Appearance
                .BackColor = backColor
                .ForeColor = foreColor
                .Options.UseBackColor = True
                .Options.UseForeColor = True
                .Font = New Font("Consolas", 10, FontStyle.Regular)
                .Options.UseFont = True
            End With

            memo.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple
            memo.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.UltraFlat
            memo.LookAndFeel.UseDefaultLookAndFeel = False
        Catch ex As Exception
            Debug.WriteLine($"Error applying MemoEdit style: {ex.Message}")
        End Try
    End Sub

#End Region

#Region "UI Responsiveness"

    ''' <summary>
    ''' دالة لضمان استجابة الواجهة أثناء العمليات الطويلة
    ''' </summary>
    Private Sub KeepUIResponsive()
        Try
            Application.DoEvents()
            Threading.Thread.Yield()
        Catch ex As Exception
            Debug.WriteLine($"Error keeping UI responsive: {ex.Message}")
        End Try
    End Sub

#End Region

#Region "Button Event Handlers"

    ''' <summary>
    ''' الخطوة 1: رفع ملف نصي باستخدام BackgroundWorker1
    ''' </summary>
    Public Sub UploadeList()
        Try
            If OpenFileDialog1.ShowDialog() = DialogResult.OK Then
                ' التحقق من أن الملف موجود
                If File.Exists(OpenFileDialog1.FileName) Then
                    ' تعطيل الأزرار أثناء التحميل
                    SetButtonsEnabled(False)
                    ShowProgressPanel("Loading File", "Reading file content...")

                    ' بدء تحميل الملف باستخدام BackgroundWorker1
                    BackgroundWorker1.RunWorkerAsync(OpenFileDialog1.FileName)

                Else
                    XtraMessageBox.Show("Selected file does not exist.", "File Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                End If
            End If
        Catch ex As Exception
            XtraMessageBox.Show($"Error selecting file: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            SetButtonsEnabled(True)
            HideProgressPanel()
        End Try
    End Sub

    ''' <summary>
    ''' الخطوة 2: بدء/استكمال استخراج البريد الإلكتروني باستخدام BackgroundWorker2
    ''' </summary>
    Public Sub Startlist()
        Try
            ' التحقق من وجود محتوى في MemoEdit1
            If String.IsNullOrWhiteSpace(MemoEdit1.Text) Then
                XtraMessageBox.Show("Please upload a text file first.", "No Content", MessageBoxButtons.OK, MessageBoxIcon.Information)
                Return
            End If

            ' التحقق من أن BackgroundWorker2 غير مشغول
            If BackgroundWorker2.IsBusy Then
                XtraMessageBox.Show("Email extraction is already in progress.", "Process Running", MessageBoxButtons.OK, MessageBoxIcon.Information)
                Return
            End If

            ' تعطيل الأزرار وتمكين زر الإيقاف
            frmMain.BntStart.Enabled = False
            frmMain.BntStop.Enabled = True
            frmMain.RibbonPageGroup63.Enabled = True
            frmMain.BntSaveList.Enabled = False
            frmMain.BntStop.Enabled = True
            isCancelled = False

            ' تحديد ما إذا كنا نستكمل أم نبدأ من جديد
            If isPaused AndAlso Not String.IsNullOrEmpty(processingContent) Then
                ' استكمال من حيث توقفنا
                ShowProgressPanel("Resuming Email Extraction", $"Continuing from position {lastProcessedPosition:N0}...")
                BackgroundWorker2.RunWorkerAsync(New Object() {processingContent, lastProcessedPosition, tempExtractedEmails})
            Else
                ' بدء جديد
                processingContent = MemoEdit1.Text
                lastProcessedPosition = 0
                tempExtractedEmails.Clear()
                isPaused = False

                ShowProgressPanel("Extracting Emails", "Starting email extraction...")
                BackgroundWorker2.RunWorkerAsync(New Object() {processingContent, 0, New List(Of String)()})
            End If

        Catch ex As Exception
            XtraMessageBox.Show($"Error starting email extraction: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            SetButtonsEnabled(True)
            HideProgressPanel()
        End Try
    End Sub

    ''' <summary>
    ''' الخطوة 3: إيقاف مؤقت لعملية الاستخراج (يمكن الاستكمال لاحقاً)
    ''' </summary>
    Public Sub StopProcess()
        Try
            If BackgroundWorker2.IsBusy Then
                ' طلب إيقاف مؤقت للعملية
                isCancelled = True
                isPaused = True
                BackgroundWorker2.CancelAsync()

                ' تحديث الواجهة
                frmMain.BntStop.Enabled = False
                frmMain.RibbonPageGroup44.Enabled = True
                frmMain.BntSaveList.Enabled = True
                frmMain.RibbonPageGroup45.Enabled = True
                frmMain.BarButtonItem67.Enabled = True
                UpdateProgressPanel("Pausing process...")

                XtraMessageBox.Show("Pause request sent. You can resume later by clicking Start again.", "Pausing", MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If
        Catch ex As Exception
            XtraMessageBox.Show($"Error pausing process: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' الخطوة 4: حفظ البريد المستخرج في مجلد ZeroPass على سطح المكتب
    ''' </summary>
    Public Sub SaveList()
        Try
            If extractedEmails.Count = 0 Then
                XtraMessageBox.Show("No emails to save.", "No Data", MessageBoxButtons.OK, MessageBoxIcon.Information)
                Return
            End If

            ' إنشاء مجلد ZeroPass على سطح المكتب
            Dim desktopPath As String = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)
            Dim zeroPassFolder As String = Path.Combine(desktopPath, "ZeroPass")

            If Not Directory.Exists(zeroPassFolder) Then
                Directory.CreateDirectory(zeroPassFolder)
            End If

            ' حفظ البريد في ملف ZeroPass.txt
            Dim filePath As String = Path.Combine(zeroPassFolder, "ZeroPass.txt")
            File.WriteAllLines(filePath, extractedEmails)

            XtraMessageBox.Show($"Emails saved successfully!{Environment.NewLine}Location: {filePath}{Environment.NewLine}Total emails: {extractedEmails.Count}",
                               "Save Successful", MessageBoxButtons.OK, MessageBoxIcon.Information)
            frmMain.RibbonPageGroup45.Enabled = True
            frmMain.BarButtonItem67.Enabled = True
        Catch ex As Exception
            XtraMessageBox.Show($"Error saving emails: {ex.Message}", "Save Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' الخطوة 5: مسح جميع البيانات
    ''' </summary>
    Public Sub bntClearList()
        Try
            If XtraMessageBox.Show("Are you sure you want to clear all data?", "Confirm Clear", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
                ' مسح المحتوى
                MemoEdit1.Text = ""
                MemoEdit2.Text = ""
                fileContent = ""

                ' مسح البيانات
                extractedEmails.Clear()
                extractedEmailCount = 0

                ' إعادة تعيين حالة الإيقاف والاستكمال
                isPaused = False
                lastProcessedPosition = 0
                tempExtractedEmails.Clear()
                processingContent = ""
                isCancelled = False

                ' تحديث العرض
                UpdateEmailCount()
                HideProgressPanel()

                ' إعادة تعيين حالة الأزرار
                SetButtonsEnabled(True)
                frmMain.BntStart.Enabled = False
                frmMain.BntSaveList.Enabled = False
                frmMain.BntStop.Enabled = False
                frmMain.RibbonPageGroup45.Enabled = False
                frmMain.BarButtonItem67.Enabled = True

            End If
        Catch ex As Exception
            XtraMessageBox.Show($"Error clearing data: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

#End Region

#Region "BackgroundWorker1 Events - File Loading"

    ''' <summary>
    ''' تحميل الملف في الخلفية باستخدام StreamReader
    ''' </summary>
    Private Sub BackgroundWorker1_DoWork(sender As Object, e As DoWorkEventArgs) Handles BackgroundWorker1.DoWork
        Try
            Dim worker As BackgroundWorker = DirectCast(sender, BackgroundWorker)
            Dim filePath As String = DirectCast(e.Argument, String)

            ' قراءة الملف باستخدام StreamReader
            Using reader As New StreamReader(filePath, System.Text.Encoding.UTF8)
                Dim content As String = reader.ReadToEnd()
                e.Result = content
            End Using
            frmMain.RibbonPageGroup45.Enabled = True
            frmMain.BarButtonItem67.Enabled = True
        Catch ex As Exception
            e.Result = ex
        End Try
    End Sub

    ''' <summary>
    ''' معالجة نتيجة تحميل الملف
    ''' </summary>
    Private Sub BackgroundWorker1_RunWorkerCompleted(sender As Object, e As RunWorkerCompletedEventArgs) Handles BackgroundWorker1.RunWorkerCompleted
        Try
            HideProgressPanel()

            If e.Error IsNot Nothing Then
                XtraMessageBox.Show($"Error loading file: {e.Error.Message}", "File Load Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                SetButtonsEnabled(True)
                Return
            End If

            If TypeOf e.Result Is Exception Then
                Dim ex As Exception = DirectCast(e.Result, Exception)
                XtraMessageBox.Show($"Error reading file: {ex.Message}", "File Read Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                SetButtonsEnabled(True)
                Return
            End If

            ' عرض المحتوى في MemoEdit1
            fileContent = DirectCast(e.Result, String)
            MemoEdit1.Text = fileContent

            ' تمكين الأزرار
            SetButtonsEnabled(True)
            frmMain.BntStart.Enabled = True
            frmMain.BntStop.Enabled = False
            XtraMessageBox.Show($"File loaded successfully!{Environment.NewLine}Characters: {fileContent.Length:N0}",
                               "File Loaded", MessageBoxButtons.OK, MessageBoxIcon.Information)

        Catch ex As Exception
            XtraMessageBox.Show($"Error processing file load result: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            SetButtonsEnabled(True)
            HideProgressPanel()
        End Try
    End Sub

#End Region

#Region "BackgroundWorker2 Events - Email Extraction"

    ''' <summary>
    ''' استخراج البريد الإلكتروني في الخلفية باستخدام Regex مع دعم الاستكمال
    ''' </summary>
    Private Sub BackgroundWorker2_DoWork(sender As Object, e As DoWorkEventArgs) Handles BackgroundWorker2.DoWork
        Try
            Dim worker As BackgroundWorker = DirectCast(sender, BackgroundWorker)
            Dim args As Object() = DirectCast(e.Argument, Object())

            Dim content As String = DirectCast(args(0), String)
            Dim startPosition As Integer = DirectCast(args(1), Integer)
            Dim existingEmails As List(Of String) = DirectCast(args(2), List(Of String))

            ' التعبير المنتظم لاستخراج البريد الإلكتروني
            Dim emailPattern As String = "\b[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}\b"
            Dim regex As New Regex(emailPattern, RegexOptions.IgnoreCase)

            ' قائمة لتخزين البريد المستخرج (بدء من البريد الموجود)
            Dim emails As New HashSet(Of String)(StringComparer.OrdinalIgnoreCase)
            For Each email In existingEmails
                emails.Add(email)
            Next

            Dim processedChars As Integer = startPosition
            Dim totalChars As Integer = content.Length

            ' تقسيم المحتوى إلى أجزاء للمعالجة التدريجية
            Dim chunkSize As Integer = 1000
            Dim startChunk As Integer = Math.Floor(startPosition / chunkSize)
            Dim totalChunks As Integer = Math.Ceiling(totalChars / chunkSize)

            For i As Integer = startChunk To totalChunks - 1
                ' التحقق من طلب الإلغاء
                If worker.CancellationPending Then
                    ' حفظ الموقع الحالي للاستكمال لاحقاً
                    Try
                        e.Result = New Object() {emails.ToList(), processedChars, True} ' True = تم الإلغاء
                        e.Cancel = True
                        Return
                    Catch ex As Exception
                        Debug.WriteLine($"Error saving cancellation state: {ex.Message}")
                        ' في حالة فشل حفظ الحالة، قم بالإلغاء العادي
                        e.Result = Nothing
                        e.Cancel = True
                        Return
                    End Try
                End If

                ' استخراج الجزء الحالي
                Dim startIndex As Integer = i * chunkSize
                Dim length As Integer = Math.Min(chunkSize, totalChars - startIndex)
                Dim chunk As String = content.Substring(startIndex, length)

                ' البحث عن البريد في الجزء الحالي
                Dim matches As MatchCollection = regex.Matches(chunk)
                For Each match As Match In matches
                    emails.Add(match.Value.ToLower())
                Next

                processedChars = startIndex + length

                ' تحديث التقدم
                Dim progress As Integer = CInt((processedChars / totalChars) * 100)
                worker.ReportProgress(progress, emails.Count)

                ' إعطاء فرصة للواجهة للتحديث
                Threading.Thread.Sleep(1)
            Next

            ' إرجاع النتيجة النهائية
            Try
                e.Result = New Object() {emails.ToList(), totalChars, False} ' False = اكتمل بنجاح

            Catch ex As Exception
                Debug.WriteLine($"Error creating final result: {ex.Message}")
                e.Result = emails.ToList() ' إرجاع القائمة فقط كبديل
            End Try

        Catch ex As Exception
            e.Result = ex
        End Try
    End Sub

    ''' <summary>
    ''' تحديث التقدم أثناء استخراج البريد
    ''' </summary>
    Private Sub BackgroundWorker2_ProgressChanged(sender As Object, e As ProgressChangedEventArgs) Handles BackgroundWorker2.ProgressChanged
        Try
            Dim currentEmailCount As Integer = CInt(e.UserState)
            UpdateProgressPanel($"Extracting emails... {e.ProgressPercentage}% - Found: {currentEmailCount}")

            ' تحديث العدد المؤقت
            lblNumberofmailextracted.Text = $"📧 Extracted Emails: {currentEmailCount}"
            frmMain.RibbonPageGroup45.Enabled = True
            frmMain.BarButtonItem67.Enabled = True
        Catch ex As Exception
            Debug.WriteLine($"Error updating progress: {ex.Message}")
        End Try
    End Sub

    ''' <summary>
    ''' معالجة نتيجة استخراج البريد مع دعم الإيقاف المؤقت
    ''' </summary>
    Private Sub BackgroundWorker2_RunWorkerCompleted(sender As Object, e As RunWorkerCompletedEventArgs) Handles BackgroundWorker2.RunWorkerCompleted
        Try
            HideProgressPanel()

            ' إعادة تمكين الأزرار
            frmMain.BntStart.Enabled = True
            frmMain.BntStop.Enabled = False

            ' التعامل مع الإلغاء أولاً (قبل التحقق من الأخطاء)
            If e.Cancelled Then
                ' التحقق من وجود نتائج جزئية
                If e.Result IsNot Nothing AndAlso TypeOf e.Result Is Object() Then
                    Try
                        ' تم الإيقاف المؤقت - حفظ الحالة
                        Dim resultArray As Object() = DirectCast(e.Result, Object())
                        tempExtractedEmails = DirectCast(resultArray(0), List(Of String))
                        lastProcessedPosition = DirectCast(resultArray(1), Integer)
                        Dim wasCancelled As Boolean = DirectCast(resultArray(2), Boolean)

                        If wasCancelled Then
                            ' عرض البريد المستخرج حتى الآن
                            extractedEmails = New List(Of String)(tempExtractedEmails)
                            extractedEmailCount = extractedEmails.Count

                            If MemoEdit2 IsNot Nothing Then
                                MemoEdit2.Text = String.Join(Environment.NewLine, extractedEmails)
                            End If

                            UpdateEmailCount()

                            ' تمكين زر الحفظ إذا كان هناك بريد
                            If frmMain.BntSaveList IsNot Nothing Then
                                frmMain.BntSaveList.Enabled = extractedEmailCount > 0
                            End If
                            frmMain.RibbonPageGroup45.Enabled = True
                            frmMain.BarButtonItem67.Enabled = True

                            XtraMessageBox.Show($"Email extraction paused.{Environment.NewLine}Emails found so far: {extractedEmailCount:N0}{Environment.NewLine}Click Start to resume from where you left off.",
                                               "Process Paused", MessageBoxButtons.OK, MessageBoxIcon.Information)
                            Return
                        End If
                    Catch ex As Exception
                        Debug.WriteLine($"Error processing cancelled result: {ex.Message}")
                    End Try
                End If

                ' إلغاء عادي بدون نتائج جزئية
                XtraMessageBox.Show("Email extraction was cancelled.", "Process Cancelled", MessageBoxButtons.OK, MessageBoxIcon.Information)
                Return
            End If

            ' التحقق من الأخطاء بعد التعامل مع الإلغاء
            If e.Error IsNot Nothing Then
                ' تجاهل أخطاء الإلغاء المتوقعة
                If TypeOf e.Error Is OperationCanceledException Then
                    XtraMessageBox.Show("Email extraction was cancelled.", "Process Cancelled", MessageBoxButtons.OK, MessageBoxIcon.Information)
                Else
                    XtraMessageBox.Show($"Error during email extraction: {e.Error.Message}", "Extraction Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                End If
                Return
            End If

            ' التحقق من نوع النتيجة
            If e.Result Is Nothing Then
                XtraMessageBox.Show("No results returned from email extraction.", "No Results", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Return
            End If

            If TypeOf e.Result Is Exception Then
                Dim ex As Exception = DirectCast(e.Result, Exception)
                XtraMessageBox.Show($"Error extracting emails: {ex.Message}", "Extraction Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                Return
            End If

            ' اكتملت العملية بنجاح
            If TypeOf e.Result Is Object() Then
                Dim resultArray As Object() = DirectCast(e.Result, Object())
                extractedEmails = DirectCast(resultArray(0), List(Of String))
                extractedEmailCount = extractedEmails.Count

                ' إعادة تعيين حالة الإيقاف
                isPaused = False
                lastProcessedPosition = 0
                tempExtractedEmails.Clear()
                processingContent = ""
            Else
                ' للتوافق مع النسخة القديمة
                extractedEmails = DirectCast(e.Result, List(Of String))
                extractedEmailCount = extractedEmails.Count
            End If

            ' عرض البريد في MemoEdit2
            If MemoEdit2 IsNot Nothing Then
                MemoEdit2.Text = String.Join(Environment.NewLine, extractedEmails)
            End If

            ' تحديث العدد
            UpdateEmailCount()

            ' تمكين زر الحفظ
            If frmMain.BntSaveList IsNot Nothing Then
                frmMain.BntSaveList.Enabled = extractedEmailCount > 0
            End If

            ' إظهار رسالة النجاح
            XtraMessageBox.Show($"Email extraction completed successfully!{Environment.NewLine}Total emails found: {extractedEmailCount:N0}",
                               "Extraction Complete", MessageBoxButtons.OK, MessageBoxIcon.Information)

        Catch ex As Exception
            XtraMessageBox.Show($"Error processing extraction results: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            SetButtonsEnabled(True)
        End Try
    End Sub

#End Region

#Region "Helper Methods"

    ''' <summary>
    ''' تحديث عدد البريد المستخرج
    ''' </summary>
    Private Sub UpdateEmailCount()
        Try
            lblNumberofmailextracted.Text = $"📧 Extracted Emails: {extractedEmailCount:N0}"
        Catch ex As Exception
            Debug.WriteLine($"Error updating email count: {ex.Message}")
        End Try
    End Sub

    ''' <summary>
    ''' إظهار ProgressPanel1
    ''' </summary>
    Private Sub ShowProgressPanel(text As String, description As String)
        Try
            If ProgressPanel1 IsNot Nothing Then
                ProgressPanel1.Text = text
                ProgressPanel1.Description = description
                ProgressPanel1.Visible = True
                ProgressPanel1.BringToFront()

                '' إخفاء MemoEdit2 أثناء المعالجة
                'If MemoEdit2 IsNot Nothing Then
                '    MemoEdit2.Visible = False
                'End If
            End If
        Catch ex As Exception
            Debug.WriteLine($"Error showing progress panel: {ex.Message}")
        End Try
    End Sub

    ''' <summary>
    ''' إخفاء ProgressPanel1
    ''' </summary>
    Private Sub HideProgressPanel()
        Try
            If ProgressPanel1 IsNot Nothing Then
                ProgressPanel1.Visible = False
            End If

            ' إظهار MemoEdit2 مرة أخرى
            If MemoEdit2 IsNot Nothing Then
                MemoEdit2.Visible = True
            End If
        Catch ex As Exception
            Debug.WriteLine($"Error hiding progress panel: {ex.Message}")
        End Try
    End Sub

    ''' <summary>
    ''' تحديث وصف ProgressPanel1
    ''' </summary>
    Private Sub UpdateProgressPanel(description As String)
        Try
            If ProgressPanel1 IsNot Nothing AndAlso ProgressPanel1.Visible Then
                ProgressPanel1.Description = description
            End If
        Catch ex As Exception
            Debug.WriteLine($"Error updating progress panel: {ex.Message}")
        End Try
    End Sub

    ''' <summary>
    ''' تمكين/تعطيل الأزرار
    ''' </summary>
    Private Sub SetButtonsEnabled(enabled As Boolean)
        Try
            frmMain.BntUploadeList.Enabled = enabled
            If enabled Then
                frmMain.BntStart.Enabled = Not String.IsNullOrWhiteSpace(MemoEdit1.Text)
                frmMain.BntSaveList.Enabled = extractedEmailCount > 0
            Else
                frmMain.BntStart.Enabled = False
                frmMain.BntSaveList.Enabled = False
            End If
            frmMain.BntClear.Enabled = enabled
        Catch ex As Exception
            Debug.WriteLine($"Error setting button states: {ex.Message}")
        End Try
    End Sub

    Private Sub MemoEdit1_EditValueChanged(sender As Object, e As EventArgs) Handles MemoEdit1.EditValueChanged

        ' إعادة تعيين حالة الأزرار
        If String.IsNullOrWhiteSpace(MemoEdit1.Text) Then
            frmMain.RibbonPageGroup45.Enabled = False
            frmMain.BarButtonItem67.Enabled = False
            frmMain.BntStart.Enabled = False
            frmMain.BntStart.Enabled = False
        Else
            frmMain.RibbonPageGroup45.Enabled = True
            frmMain.BarButtonItem67.Enabled = True
            frmMain.BntStart.Enabled = True
            frmMain.BntStart.Enabled = True
        End If
    End Sub


#End Region

#Region "Public Methods for External Access"

#End Region

End Class