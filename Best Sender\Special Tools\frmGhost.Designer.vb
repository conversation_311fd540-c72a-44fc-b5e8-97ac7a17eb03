﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class frmGhost
    Inherits DevExpress.XtraEditors.XtraForm
    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub
    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer
    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.GroupControl2 = New DevExpress.XtraEditors.GroupControl()
        Me.LayoutControl1 = New DevExpress.XtraLayout.LayoutControl()
        Me.cmbEncryptionMode = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.BntEncoded = New DevExpress.XtraEditors.SimpleButton()
        Me.BntSave = New DevExpress.XtraEditors.SimpleButton()
        Me.LetterEncoder_Richtext2 = New System.Windows.Forms.RichTextBox()
        Me.Root = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem1 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem7 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem8 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem6 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.GroupControl1 = New DevExpress.XtraEditors.GroupControl()
        Me.LayoutControl3 = New DevExpress.XtraLayout.LayoutControl()
        Me.bntRest = New DevExpress.XtraEditors.SimpleButton()
        Me.BntUploade = New DevExpress.XtraEditors.SimpleButton()
        Me.LetterEncoder_Richtext = New System.Windows.Forms.RichTextBox()
        Me.LetterEncoder_txtFilepath = New DevExpress.XtraEditors.TextEdit()
        Me.LayoutControlGroup2 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem4 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem5 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem2 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem3 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.EmptySpaceItem1 = New DevExpress.XtraLayout.EmptySpaceItem()
        CType(Me.GroupControl2, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl2.SuspendLayout()
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.LayoutControl1.SuspendLayout()
        CType(Me.cmbEncryptionMode.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Root, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem7, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem8, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem6, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl1.SuspendLayout()
        CType(Me.LayoutControl3, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.LayoutControl3.SuspendLayout()
        CType(Me.LetterEncoder_txtFilepath.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'GroupControl2
        '
        Me.GroupControl2.Controls.Add(Me.LayoutControl1)
        Me.GroupControl2.Location = New System.Drawing.Point(473, 12)
        Me.GroupControl2.Name = "GroupControl2"
        Me.GroupControl2.Size = New System.Drawing.Size(480, 570)
        Me.GroupControl2.TabIndex = 521
        Me.GroupControl2.Text = "Encoded"
        '
        'LayoutControl1
        '
        Me.LayoutControl1.Controls.Add(Me.cmbEncryptionMode)
        Me.LayoutControl1.Controls.Add(Me.BntEncoded)
        Me.LayoutControl1.Controls.Add(Me.BntSave)
        Me.LayoutControl1.Controls.Add(Me.LetterEncoder_Richtext2)
        Me.LayoutControl1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.LayoutControl1.Location = New System.Drawing.Point(2, 29)
        Me.LayoutControl1.Name = "LayoutControl1"
        Me.LayoutControl1.Root = Me.Root
        Me.LayoutControl1.Size = New System.Drawing.Size(476, 539)
        Me.LayoutControl1.TabIndex = 0
        Me.LayoutControl1.Text = "LayoutControl1"
        '
        'cmbEncryptionMode
        '
        Me.cmbEncryptionMode.Cursor = System.Windows.Forms.Cursors.Hand
        Me.cmbEncryptionMode.EditValue = ""
        Me.cmbEncryptionMode.Location = New System.Drawing.Point(389, 16)
        Me.cmbEncryptionMode.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.cmbEncryptionMode.Name = "cmbEncryptionMode"
        Me.cmbEncryptionMode.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.cmbEncryptionMode.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.cmbEncryptionMode.Properties.Appearance.Options.UseBackColor = True
        Me.cmbEncryptionMode.Properties.Appearance.Options.UseFont = True
        Me.cmbEncryptionMode.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cmbEncryptionMode.Properties.Items.AddRange(New Object() {"Encryption Method 1", "Encryption Method 2"})
        Me.cmbEncryptionMode.Size = New System.Drawing.Size(71, 38)
        Me.cmbEncryptionMode.StyleController = Me.LayoutControl1
        Me.cmbEncryptionMode.TabIndex = 522
        '
        'BntEncoded
        '
        Me.BntEncoded.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntEncoded.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntEncoded.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntEncoded.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntEncoded.Appearance.Options.UseBackColor = True
        Me.BntEncoded.Appearance.Options.UseBorderColor = True
        Me.BntEncoded.Appearance.Options.UseFont = True
        Me.BntEncoded.Appearance.Options.UseForeColor = True
        Me.BntEncoded.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntEncoded.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntEncoded.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntEncoded.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntEncoded.AppearanceDisabled.Options.UseBackColor = True
        Me.BntEncoded.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntEncoded.AppearanceDisabled.Options.UseFont = True
        Me.BntEncoded.AppearanceDisabled.Options.UseForeColor = True
        Me.BntEncoded.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntEncoded.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntEncoded.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntEncoded.AppearanceHovered.Options.UseBackColor = True
        Me.BntEncoded.AppearanceHovered.Options.UseBorderColor = True
        Me.BntEncoded.AppearanceHovered.Options.UseForeColor = True
        Me.BntEncoded.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntEncoded.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntEncoded.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntEncoded.AppearancePressed.Options.UseBackColor = True
        Me.BntEncoded.AppearancePressed.Options.UseBorderColor = True
        Me.BntEncoded.AppearancePressed.Options.UseForeColor = True
        Me.BntEncoded.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.EncodedLetter32x32
        Me.BntEncoded.Location = New System.Drawing.Point(16, 16)
        Me.BntEncoded.Name = "BntEncoded"
        Me.BntEncoded.Size = New System.Drawing.Size(109, 38)
        Me.BntEncoded.StyleController = Me.LayoutControl1
        Me.BntEncoded.TabIndex = 521
        Me.BntEncoded.Text = "Encrypt"
        '
        'BntSave
        '
        Me.BntSave.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntSave.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntSave.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntSave.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntSave.Appearance.Options.UseBackColor = True
        Me.BntSave.Appearance.Options.UseBorderColor = True
        Me.BntSave.Appearance.Options.UseFont = True
        Me.BntSave.Appearance.Options.UseForeColor = True
        Me.BntSave.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntSave.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntSave.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntSave.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntSave.AppearanceDisabled.Options.UseBackColor = True
        Me.BntSave.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntSave.AppearanceDisabled.Options.UseFont = True
        Me.BntSave.AppearanceDisabled.Options.UseForeColor = True
        Me.BntSave.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntSave.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntSave.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntSave.AppearanceHovered.Options.UseBackColor = True
        Me.BntSave.AppearanceHovered.Options.UseBorderColor = True
        Me.BntSave.AppearanceHovered.Options.UseForeColor = True
        Me.BntSave.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntSave.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntSave.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntSave.AppearancePressed.Options.UseBackColor = True
        Me.BntSave.AppearancePressed.Options.UseBorderColor = True
        Me.BntSave.AppearancePressed.Options.UseForeColor = True
        Me.BntSave.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.html32x32
        Me.BntSave.Location = New System.Drawing.Point(131, 16)
        Me.BntSave.Name = "BntSave"
        Me.BntSave.Size = New System.Drawing.Size(97, 38)
        Me.BntSave.StyleController = Me.LayoutControl1
        Me.BntSave.TabIndex = 520
        Me.BntSave.Text = "Save"
        '
        'LetterEncoder_Richtext2
        '
        Me.LetterEncoder_Richtext2.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.LetterEncoder_Richtext2.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.LetterEncoder_Richtext2.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.LetterEncoder_Richtext2.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.LetterEncoder_Richtext2.Location = New System.Drawing.Point(16, 60)
        Me.LetterEncoder_Richtext2.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.LetterEncoder_Richtext2.Name = "LetterEncoder_Richtext2"
        Me.LetterEncoder_Richtext2.Size = New System.Drawing.Size(444, 463)
        Me.LetterEncoder_Richtext2.TabIndex = 68
        Me.LetterEncoder_Richtext2.Text = ""
        '
        'Root
        '
        Me.Root.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.Root.GroupBordersVisible = False
        Me.Root.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem1, Me.LayoutControlItem7, Me.LayoutControlItem8, Me.LayoutControlItem6})
        Me.Root.Name = "Root"
        Me.Root.Size = New System.Drawing.Size(476, 539)
        Me.Root.TextVisible = False
        '
        'LayoutControlItem1
        '
        Me.LayoutControlItem1.Control = Me.LetterEncoder_Richtext2
        Me.LayoutControlItem1.Location = New System.Drawing.Point(0, 44)
        Me.LayoutControlItem1.Name = "LayoutControlItem1"
        Me.LayoutControlItem1.Size = New System.Drawing.Size(450, 469)
        Me.LayoutControlItem1.TextVisible = False
        '
        'LayoutControlItem7
        '
        Me.LayoutControlItem7.Control = Me.BntSave
        Me.LayoutControlItem7.Location = New System.Drawing.Point(115, 0)
        Me.LayoutControlItem7.Name = "LayoutControlItem7"
        Me.LayoutControlItem7.Size = New System.Drawing.Size(103, 44)
        Me.LayoutControlItem7.TextVisible = False
        '
        'LayoutControlItem8
        '
        Me.LayoutControlItem8.Control = Me.BntEncoded
        Me.LayoutControlItem8.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem8.Name = "LayoutControlItem8"
        Me.LayoutControlItem8.Size = New System.Drawing.Size(115, 44)
        Me.LayoutControlItem8.TextVisible = False
        '
        'LayoutControlItem6
        '
        Me.LayoutControlItem6.Control = Me.cmbEncryptionMode
        Me.LayoutControlItem6.Location = New System.Drawing.Point(218, 0)
        Me.LayoutControlItem6.Name = "LayoutControlItem6"
        Me.LayoutControlItem6.Size = New System.Drawing.Size(232, 44)
        Me.LayoutControlItem6.Text = "Select Algorithm Method"
        Me.LayoutControlItem6.TextSize = New System.Drawing.Size(139, 18)
        '
        'GroupControl1
        '
        Me.GroupControl1.Controls.Add(Me.LayoutControl3)
        Me.GroupControl1.Location = New System.Drawing.Point(13, 12)
        Me.GroupControl1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.GroupControl1.Name = "GroupControl1"
        Me.GroupControl1.Size = New System.Drawing.Size(454, 570)
        Me.GroupControl1.TabIndex = 520
        Me.GroupControl1.Text = "Normall"
        '
        'LayoutControl3
        '
        Me.LayoutControl3.Controls.Add(Me.bntRest)
        Me.LayoutControl3.Controls.Add(Me.BntUploade)
        Me.LayoutControl3.Controls.Add(Me.LetterEncoder_Richtext)
        Me.LayoutControl3.Controls.Add(Me.LetterEncoder_txtFilepath)
        Me.LayoutControl3.Dock = System.Windows.Forms.DockStyle.Fill
        Me.LayoutControl3.Location = New System.Drawing.Point(2, 29)
        Me.LayoutControl3.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.LayoutControl3.Name = "LayoutControl3"
        Me.LayoutControl3.Root = Me.LayoutControlGroup2
        Me.LayoutControl3.Size = New System.Drawing.Size(450, 539)
        Me.LayoutControl3.TabIndex = 0
        Me.LayoutControl3.Text = "LayoutControl3"
        '
        'bntRest
        '
        Me.bntRest.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntRest.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntRest.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.bntRest.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntRest.Appearance.Options.UseBackColor = True
        Me.bntRest.Appearance.Options.UseBorderColor = True
        Me.bntRest.Appearance.Options.UseFont = True
        Me.bntRest.Appearance.Options.UseForeColor = True
        Me.bntRest.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.bntRest.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.bntRest.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.bntRest.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.bntRest.AppearanceDisabled.Options.UseBackColor = True
        Me.bntRest.AppearanceDisabled.Options.UseBorderColor = True
        Me.bntRest.AppearanceDisabled.Options.UseFont = True
        Me.bntRest.AppearanceDisabled.Options.UseForeColor = True
        Me.bntRest.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.bntRest.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntRest.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.bntRest.AppearanceHovered.Options.UseBackColor = True
        Me.bntRest.AppearanceHovered.Options.UseBorderColor = True
        Me.bntRest.AppearanceHovered.Options.UseForeColor = True
        Me.bntRest.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntRest.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntRest.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.bntRest.AppearancePressed.Options.UseBackColor = True
        Me.bntRest.AppearancePressed.Options.UseBorderColor = True
        Me.bntRest.AppearancePressed.Options.UseForeColor = True
        Me.bntRest.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_32x322
        Me.bntRest.Location = New System.Drawing.Point(334, 16)
        Me.bntRest.Name = "bntRest"
        Me.bntRest.Size = New System.Drawing.Size(100, 38)
        Me.bntRest.StyleController = Me.LayoutControl3
        Me.bntRest.TabIndex = 521
        Me.bntRest.Text = "Reset All"
        '
        'BntUploade
        '
        Me.BntUploade.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntUploade.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntUploade.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntUploade.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntUploade.Appearance.Options.UseBackColor = True
        Me.BntUploade.Appearance.Options.UseBorderColor = True
        Me.BntUploade.Appearance.Options.UseFont = True
        Me.BntUploade.Appearance.Options.UseForeColor = True
        Me.BntUploade.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntUploade.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntUploade.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntUploade.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntUploade.AppearanceDisabled.Options.UseBackColor = True
        Me.BntUploade.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntUploade.AppearanceDisabled.Options.UseFont = True
        Me.BntUploade.AppearanceDisabled.Options.UseForeColor = True
        Me.BntUploade.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntUploade.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntUploade.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntUploade.AppearanceHovered.Options.UseBackColor = True
        Me.BntUploade.AppearanceHovered.Options.UseBorderColor = True
        Me.BntUploade.AppearanceHovered.Options.UseForeColor = True
        Me.BntUploade.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntUploade.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntUploade.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntUploade.AppearancePressed.Options.UseBackColor = True
        Me.BntUploade.AppearancePressed.Options.UseBorderColor = True
        Me.BntUploade.AppearancePressed.Options.UseForeColor = True
        Me.BntUploade.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.add32x32
        Me.BntUploade.Location = New System.Drawing.Point(226, 16)
        Me.BntUploade.Name = "BntUploade"
        Me.BntUploade.Size = New System.Drawing.Size(102, 38)
        Me.BntUploade.StyleController = Me.LayoutControl3
        Me.BntUploade.TabIndex = 520
        Me.BntUploade.Text = "Browse..."
        '
        'LetterEncoder_Richtext
        '
        Me.LetterEncoder_Richtext.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.LetterEncoder_Richtext.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.LetterEncoder_Richtext.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.LetterEncoder_Richtext.ForeColor = System.Drawing.Color.White
        Me.LetterEncoder_Richtext.Location = New System.Drawing.Point(16, 60)
        Me.LetterEncoder_Richtext.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.LetterEncoder_Richtext.Name = "LetterEncoder_Richtext"
        Me.LetterEncoder_Richtext.Size = New System.Drawing.Size(418, 453)
        Me.LetterEncoder_Richtext.TabIndex = 67
        Me.LetterEncoder_Richtext.Text = ""
        '
        'LetterEncoder_txtFilepath
        '
        Me.LetterEncoder_txtFilepath.EditValue = ""
        Me.LetterEncoder_txtFilepath.Location = New System.Drawing.Point(130, 21)
        Me.LetterEncoder_txtFilepath.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.LetterEncoder_txtFilepath.Name = "LetterEncoder_txtFilepath"
        Me.LetterEncoder_txtFilepath.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.LetterEncoder_txtFilepath.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LetterEncoder_txtFilepath.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.LetterEncoder_txtFilepath.Properties.Appearance.Options.UseBackColor = True
        Me.LetterEncoder_txtFilepath.Properties.Appearance.Options.UseFont = True
        Me.LetterEncoder_txtFilepath.Properties.Appearance.Options.UseForeColor = True
        Me.LetterEncoder_txtFilepath.Properties.NullValuePrompt = "Path File HTML..."
        Me.LetterEncoder_txtFilepath.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.LetterEncoder_txtFilepath.Size = New System.Drawing.Size(90, 30)
        Me.LetterEncoder_txtFilepath.StyleController = Me.LayoutControl3
        Me.LetterEncoder_txtFilepath.TabIndex = 66
        '
        'LayoutControlGroup2
        '
        Me.LayoutControlGroup2.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.LayoutControlGroup2.GroupBordersVisible = False
        Me.LayoutControlGroup2.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem4, Me.LayoutControlItem5, Me.LayoutControlItem2, Me.LayoutControlItem3, Me.EmptySpaceItem1})
        Me.LayoutControlGroup2.Name = "LayoutControlGroup2"
        Me.LayoutControlGroup2.Size = New System.Drawing.Size(450, 539)
        Me.LayoutControlGroup2.TextVisible = False
        '
        'LayoutControlItem4
        '
        Me.LayoutControlItem4.Control = Me.LetterEncoder_txtFilepath
        Me.LayoutControlItem4.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem4.Name = "LayoutControlItem4"
        Me.LayoutControlItem4.Size = New System.Drawing.Size(210, 44)
        Me.LayoutControlItem4.Spacing = New DevExpress.XtraLayout.Utils.Padding(0, 0, 5, 0)
        Me.LayoutControlItem4.Text = "File Path ( *.html ) :"
        Me.LayoutControlItem4.TextSize = New System.Drawing.Size(98, 18)
        '
        'LayoutControlItem5
        '
        Me.LayoutControlItem5.Control = Me.LetterEncoder_Richtext
        Me.LayoutControlItem5.Location = New System.Drawing.Point(0, 44)
        Me.LayoutControlItem5.Name = "LayoutControlItem5"
        Me.LayoutControlItem5.Size = New System.Drawing.Size(424, 459)
        Me.LayoutControlItem5.TextVisible = False
        '
        'LayoutControlItem2
        '
        Me.LayoutControlItem2.Control = Me.BntUploade
        Me.LayoutControlItem2.Location = New System.Drawing.Point(210, 0)
        Me.LayoutControlItem2.Name = "LayoutControlItem2"
        Me.LayoutControlItem2.Size = New System.Drawing.Size(108, 44)
        Me.LayoutControlItem2.TextVisible = False
        '
        'LayoutControlItem3
        '
        Me.LayoutControlItem3.Control = Me.bntRest
        Me.LayoutControlItem3.Location = New System.Drawing.Point(318, 0)
        Me.LayoutControlItem3.Name = "LayoutControlItem3"
        Me.LayoutControlItem3.Size = New System.Drawing.Size(106, 44)
        Me.LayoutControlItem3.TextVisible = False
        '
        'EmptySpaceItem1
        '
        Me.EmptySpaceItem1.Location = New System.Drawing.Point(0, 503)
        Me.EmptySpaceItem1.Name = "EmptySpaceItem1"
        Me.EmptySpaceItem1.Size = New System.Drawing.Size(424, 10)
        '
        'frmGhost
        '
        Me.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.Appearance.Options.UseBackColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 18.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(980, 604)
        Me.Controls.Add(Me.GroupControl2)
        Me.Controls.Add(Me.GroupControl1)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle
        Me.IconOptions.Image = Global.Best_Sender.My.Resources.Resources.LogoBSV
        Me.LookAndFeel.SkinName = "WXI"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Margin = New System.Windows.Forms.Padding(4)
        Me.Name = "frmGhost"
        Me.ShowInTaskbar = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "Ghost Letter Encode"
        CType(Me.GroupControl2, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl2.ResumeLayout(False)
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.LayoutControl1.ResumeLayout(False)
        CType(Me.cmbEncryptionMode.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Root, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem7, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem8, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem6, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl1.ResumeLayout(False)
        CType(Me.LayoutControl3, System.ComponentModel.ISupportInitialize).EndInit()
        Me.LayoutControl3.ResumeLayout(False)
        CType(Me.LetterEncoder_txtFilepath.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents GroupControl2 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents LayoutControl1 As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents BntEncoded As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents BntSave As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LetterEncoder_Richtext2 As RichTextBox
    Friend WithEvents Root As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem1 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem7 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem8 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents GroupControl1 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents LayoutControl3 As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents bntRest As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents BntUploade As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LetterEncoder_Richtext As RichTextBox
    Friend WithEvents LetterEncoder_txtFilepath As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LayoutControlGroup2 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem4 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem5 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem2 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem3 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents EmptySpaceItem1 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents cmbEncryptionMode As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents LayoutControlItem6 As DevExpress.XtraLayout.LayoutControlItem
End Class
