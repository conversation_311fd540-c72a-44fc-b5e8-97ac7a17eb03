﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.AppContext</name>
  </assembly>
  <members>
    <member name="T:System.AppContext">
      <summary>Предоставляет элементы для задания и получения данных о контексте приложения. </summary>
    </member>
    <member name="P:System.AppContext.BaseDirectory">
      <summary>Возвращает путь базового каталога, распознаватель сборок производит поиск сборок. </summary>
      <returns>путь базового каталога, распознаватель сборок производит поиск сборок. </returns>
    </member>
    <member name="M:System.AppContext.SetSwitch(System.String,System.Boolean)">
      <summary>Задает значение параметра. </summary>
      <param name="switchName">Имя переключателя. </param>
      <param name="isEnabled">Значение ключа. </param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="switchName" /> имеет значение null. </exception>
      <exception cref="T:System.ArgumentException">Свойство <paramref name="switchName" /> имеет значение <see cref="F:System.String.Empty" />. </exception>
    </member>
    <member name="M:System.AppContext.TryGetSwitch(System.String,System.Boolean@)">
      <summary>Условия для получения значения переключателя. </summary>
      <returns>trueЕсли <paramref name="switchName" /> был установлен и <paramref name="isEnabled" /> аргумент содержит значение параметра; в противном случае — false. </returns>
      <param name="switchName">Имя переключателя. </param>
      <param name="isEnabled">При возвращении этого метода содержит значение <paramref name="switchName" /> Если <paramref name="switchName" /> был найден, или false Если <paramref name="switchName" /> не найден.Этот параметр передается неинициализированным.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="switchName" /> имеет значение null. </exception>
      <exception cref="T:System.ArgumentException">Свойство <paramref name="switchName" /> имеет значение <see cref="F:System.String.Empty" />. </exception>
    </member>
  </members>
</doc>