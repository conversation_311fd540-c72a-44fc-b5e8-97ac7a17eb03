﻿'------------------------------------------------------------------------------
' <auto-generated>
'     This code was generated by a tool.
'     Runtime Version:4.0.30319.42000
'
'     Changes to this file may cause incorrect behavior and will be lost if
'     the code is regenerated.
' </auto-generated>
'------------------------------------------------------------------------------

Option Strict On
Option Explicit On

Imports System

Namespace My.Resources
    
    'This class was auto-generated by the StronglyTypedResourceBuilder
    'class via a tool like ResGen or Visual Studio.
    'To add or remove a member, edit your .ResX file then rerun ResGen
    'with the /str option, or rebuild your VS project.
    '''<summary>
    '''  A strongly-typed resource class, for looking up localized strings, etc.
    '''</summary>
    <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0"),  _
     Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.Runtime.CompilerServices.CompilerGeneratedAttribute(),  _
     Global.Microsoft.VisualBasic.HideModuleNameAttribute()>  _
    Public Module Resources
        
        Private resourceMan As Global.System.Resources.ResourceManager
        
        Private resourceCulture As Global.System.Globalization.CultureInfo
        
        '''<summary>
        '''  Returns the cached ResourceManager instance used by this class.
        '''</summary>
        <Global.System.ComponentModel.EditorBrowsableAttribute(Global.System.ComponentModel.EditorBrowsableState.Advanced)>  _
        Public ReadOnly Property ResourceManager() As Global.System.Resources.ResourceManager
            Get
                If Object.ReferenceEquals(resourceMan, Nothing) Then
                    Dim temp As Global.System.Resources.ResourceManager = New Global.System.Resources.ResourceManager("Best_Sender.Resources", GetType(Resources).Assembly)
                    resourceMan = temp
                End If
                Return resourceMan
            End Get
        End Property
        
        '''<summary>
        '''  Overrides the current thread's CurrentUICulture property for all
        '''  resource lookups using this strongly typed resource class.
        '''</summary>
        <Global.System.ComponentModel.EditorBrowsableAttribute(Global.System.ComponentModel.EditorBrowsableState.Advanced)>  _
        Public Property Culture() As Global.System.Globalization.CultureInfo
            Get
                Return resourceCulture
            End Get
            Set
                resourceCulture = value
            End Set
        End Property
        
        '''<summary>
        '''  Looks up a localized string similar to ‏‏.
        '''</summary>
        Public ReadOnly Property _1() As String
            Get
                Return ResourceManager.GetString("_1", resourceCulture)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property _10MinMail16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("_10MinMail16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property _12616x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("_12616x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property _16316x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("_16316x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property _777() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("_777", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property _New() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("_New", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        '''</summary>
        Public ReadOnly Property actions_add() As DevExpress.Utils.Svg.SvgImage
            Get
                Dim obj As Object = ResourceManager.GetObject("actions_add", resourceCulture)
                Return CType(obj,DevExpress.Utils.Svg.SvgImage)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        '''</summary>
        Public ReadOnly Property actions_removecircled() As DevExpress.Utils.Svg.SvgImage
            Get
                Dim obj As Object = ResourceManager.GetObject("actions_removecircled", resourceCulture)
                Return CType(obj,DevExpress.Utils.Svg.SvgImage)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        '''</summary>
        Public ReadOnly Property actions_trash() As DevExpress.Utils.Svg.SvgImage
            Get
                Dim obj As Object = ResourceManager.GetObject("actions_trash", resourceCulture)
                Return CType(obj,DevExpress.Utils.Svg.SvgImage)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        '''</summary>
        Public ReadOnly Property actions_trash1() As DevExpress.Utils.Svg.SvgImage
            Get
                Dim obj As Object = ResourceManager.GetObject("actions_trash1", resourceCulture)
                Return CType(obj,DevExpress.Utils.Svg.SvgImage)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        '''</summary>
        Public ReadOnly Property actions_trash2() As DevExpress.Utils.Svg.SvgImage
            Get
                Dim obj As Object = ResourceManager.GetObject("actions_trash2", resourceCulture)
                Return CType(obj,DevExpress.Utils.Svg.SvgImage)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property add_16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("add_16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property add_16x161() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("add_16x161", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property add_32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("add_32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property add16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("add16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property add32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("add32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property addfooter_16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("addfooter_16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property addgroupfooter_16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("addgroupfooter_16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property addgroupheader_32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("addgroupheader_32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property addheader_16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("addheader_16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property addheader_32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("addheader_32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property addheader_32x321() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("addheader_32x321", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property addheader_32x322() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("addheader_32x322", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Afterlogic16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Afterlogic16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Airmail16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Airmail16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property alarm() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("alarm", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Alice16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Alice16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        '''</summary>
        Public ReadOnly Property alignfloatingobjectbottomleft() As DevExpress.Utils.Svg.SvgImage
            Get
                Dim obj As Object = ResourceManager.GetObject("alignfloatingobjectbottomleft", resourceCulture)
                Return CType(obj,DevExpress.Utils.Svg.SvgImage)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        '''</summary>
        Public ReadOnly Property alignfloatingobjecttopleft() As DevExpress.Utils.Svg.SvgImage
            Get
                Dim obj As Object = ResourceManager.GetObject("alignfloatingobjecttopleft", resourceCulture)
                Return CType(obj,DevExpress.Utils.Svg.SvgImage)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property alignfloatingobjecttopleft_16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("alignfloatingobjecttopleft_16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Aliyun16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Aliyun16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property AMazon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("AMazon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property amazon16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("amazon16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property anazana16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("anazana16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property AntiBotRedirectPaner() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("AntiBotRedirectPaner", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Anydesk32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Anydesk32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Aol_image() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Aol_image", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Aol16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Aol16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property AolPanel() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("AolPanel", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Apply_32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Apply_32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Apply_32x321() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Apply_32x321", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property aruba16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("aruba16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property AT_T16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("AT_T16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property attach_32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("attach_32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property attached_file() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("attached_file", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property AttachmentPaner() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("AttachmentPaner", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        '''</summary>
        Public ReadOnly Property attachments() As DevExpress.Utils.Svg.SvgImage
            Get
                Dim obj As Object = ResourceManager.GetObject("attachments", resourceCulture)
                Return CType(obj,DevExpress.Utils.Svg.SvgImage)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Attimage() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Attimage", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Authentication__image() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Authentication__image", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Autistici16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Autistici16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property bad_PNG() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("bad_PNG", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Base64_image() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Base64_image", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Base64EN() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Base64EN", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Base64Image() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Base64Image", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Best_Sender_VIP() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Best_Sender_VIP", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property BestTools_Hide() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("BestTools_Hide", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property BestTools_On() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("BestTools_On", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property biglobejp() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("biglobejp", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property BKBSV2025() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("BKBSV2025", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property BkPanelButton() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("BkPanelButton", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property bksender() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("bksender", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property bksenderlast() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("bksenderlast", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property BKSettings() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("BKSettings", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property bluehost16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("bluehost16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property BooCardGold() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("BooCardGold", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property bot16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("bot16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property bot32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("bot32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property BSSenderImage() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("BSSenderImage", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property BSVNewtools() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("BSVNewtools", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property BT16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("BT16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property ButtonEncoded16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("ButtonEncoded16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property ButtonEncoded32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("ButtonEncoded32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Canary16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Canary16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Cancel_32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Cancel_32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Cancel_32x321() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Cancel_32x321", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Caver() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Caver", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Change16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Change16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Change32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Change32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property ChangeLetter() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("ChangeLetter", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property ChangeLetter16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("ChangeLetter16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property ChangeText16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("ChangeText16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property ChangeText32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("ChangeText32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property check() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("check", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property checked_PNG() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("checked_PNG", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property chinaemail16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("chinaemail16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property citadel16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("citadel16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property clear_16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("clear_16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        '''</summary>
        Public ReadOnly Property clearall() As DevExpress.Utils.Svg.SvgImage
            Get
                Dim obj As Object = ResourceManager.GetObject("clearall", resourceCulture)
                Return CType(obj,DevExpress.Utils.Svg.SvgImage)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property cloudflare_image() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("cloudflare_image", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property cloudflare_image1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("cloudflare_image1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Cloudflare_Logo() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Cloudflare_Logo", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property cloudflare16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("cloudflare16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property cloudflare32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("cloudflare32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property ClubInternet16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("ClubInternet16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property cn4e16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("cn4e16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Cock_li16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Cock_li16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Comcast16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Comcast16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property CommuniG16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("CommuniG16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property connect16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("connect16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Connect32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Connect32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property ConvertLinkToAttachment16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("ConvertLinkToAttachment16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property ConvertLinkToAttachment32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("ConvertLinkToAttachment32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property CoolLinkHide() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("CoolLinkHide", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property copy16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("copy16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property copy32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("copy32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property CopyLinkEncode() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("CopyLinkEncode", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property CopyLinkEncodeSender() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("CopyLinkEncodeSender", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property CounterMail16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("CounterMail16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Customized() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Customized", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Dark_image_New() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Dark_image_New", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property DarkEN() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("DarkEN", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property DarkImageLeter() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("DarkImageLeter", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property default16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("default16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property default32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("default32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Delete_duplicate_mail() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Delete_duplicate_mail", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property delete_hyperlink_16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("delete_hyperlink_16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property DeleteDuplicatemail16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("DeleteDuplicatemail16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property DeleteDuplicatemail32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("DeleteDuplicatemail32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property deleteheader_32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("deleteheader_32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Descount() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Descount", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Devl77716x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Devl77716x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Devl77732x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Devl77732x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property discord16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("discord16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property discord32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("discord32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property discordImage() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("discordImage", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property discountbigNew() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("discountbigNew", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Disroot16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Disroot16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        '''</summary>
        Public ReadOnly Property documentpdf() As DevExpress.Utils.Svg.SvgImage
            Get
                Dim obj As Object = ResourceManager.GetObject("documentpdf", resourceCulture)
                Return CType(obj,DevExpress.Utils.Svg.SvgImage)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property domainloca_host16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("domainloca_host16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property DoneBuild() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("DoneBuild", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property download_32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("download_32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Dragon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Dragon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Dragon_Image() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Dragon_Image", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property DragonImageLetter() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("DragonImageLetter", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property DreamHost16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("DreamHost16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Earthlink16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Earthlink16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property edittask_16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("edittask_16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property edittask_32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("edittask_32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property eM_Client16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("eM_Client16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property EmailExtractor16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("EmailExtractor16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property EmailExtractor32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("EmailExtractor32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property EmailFromScanner16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("EmailFromScanner16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property EmailFromScanner32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("EmailFromScanner32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Emailimage() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Emailimage", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property EmailSendingFormat16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("EmailSendingFormat16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property EmailSendingFormat32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("EmailSendingFormat32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property EmailSorter16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("EmailSorter16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property EmailSorter32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("EmailSorter32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property EmailValidation16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("EmailValidation16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property EmailValidation32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("EmailValidation32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property employee_16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("employee_16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property employee_32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("employee_32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property empty_trash() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("empty_trash", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property EN_Att_Pro_Hide() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("EN_Att_Pro_Hide", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property EN_Att_Pro_On() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("EN_Att_Pro_On", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        '''</summary>
        Public ReadOnly Property enablesearch() As DevExpress.Utils.Svg.SvgImage
            Get
                Dim obj As Object = ResourceManager.GetObject("enablesearch", resourceCulture)
                Return CType(obj,DevExpress.Utils.Svg.SvgImage)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Encoded_Link16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Encoded_Link16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Encoded_Link32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Encoded_Link32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property EncodedAttachment16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("EncodedAttachment16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property EncodedAttachment32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("EncodedAttachment32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property EncodedLetter_PNG() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("EncodedLetter_PNG", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property EncodedLetter16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("EncodedLetter16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property EncodedLetter32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("EncodedLetter32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property EncodedLetterIMAGE() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("EncodedLetterIMAGE", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property EncodedLinkImageNew() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("EncodedLinkImageNew", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property EncodedLinkWolfImage() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("EncodedLinkWolfImage", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Error16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Error16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Error32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Error32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property ErrorPayment() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("ErrorPayment", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property existlink_16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("existlink_16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Exit16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Exit16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Exit32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Exit32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property export_16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("export_16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property export_16x161() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("export_16x161", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property export_32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("export_32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        '''</summary>
        Public ReadOnly Property exportfile() As DevExpress.Utils.Svg.SvgImage
            Get
                Dim obj As Object = ResourceManager.GetObject("exportfile", resourceCulture)
                Return CType(obj,DevExpress.Utils.Svg.SvgImage)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        '''</summary>
        Public ReadOnly Property exporttohtml() As DevExpress.Utils.Svg.SvgImage
            Get
                Dim obj As Object = ResourceManager.GetObject("exporttohtml", resourceCulture)
                Return CType(obj,DevExpress.Utils.Svg.SvgImage)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property exporttohtml_16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("exporttohtml_16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property ExtractDomain16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("ExtractDomain16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property ExtractDomain32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("ExtractDomain32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property ezgif_com_crop__2_() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("ezgif_com_crop__2_", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property FakeMail16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("FakeMail16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property FastMail16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("FastMail16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property finding_signatures() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("finding_signatures", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property FirstClass16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("FirstClass16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Freenet16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Freenet16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Frontapp16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Frontapp16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property gaugestylelinearhorizontal_16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("gaugestylelinearhorizontal_16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property gaugestylelinearvertical_16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("gaugestylelinearvertical_16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        '''</summary>
        Public ReadOnly Property gettingstarted() As DevExpress.Utils.Svg.SvgImage
            Get
                Dim obj As Object = ResourceManager.GetObject("gettingstarted", resourceCulture)
                Return CType(obj,DevExpress.Utils.Svg.SvgImage)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        '''</summary>
        Public ReadOnly Property gettingstarted1() As DevExpress.Utils.Svg.SvgImage
            Get
                Dim obj As Object = ResourceManager.GetObject("gettingstarted1", resourceCulture)
                Return CType(obj,DevExpress.Utils.Svg.SvgImage)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        '''</summary>
        Public ReadOnly Property gettingstarted2() As DevExpress.Utils.Svg.SvgImage
            Get
                Dim obj As Object = ResourceManager.GetObject("gettingstarted2", resourceCulture)
                Return CType(obj,DevExpress.Utils.Svg.SvgImage)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Ghost_Image() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Ghost_Image", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property GhostImage() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("GhostImage", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property global_mail16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("global_mail16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property gmail16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("gmail16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property GMX() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("GMX", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property GMX16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("GMX16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Godaddy() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Godaddy", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property GoDaddy16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("GoDaddy16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Good_PNG() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Good_PNG", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Google_Captcha_Logo() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Google_Captcha_Logo", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Google_image() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Google_image", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Google_Workspace16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Google_Workspace16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property googlecaptcha16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("googlecaptcha16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property googlecaptcha32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("googlecaptcha32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Gost() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Gost", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property GroupWise16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("GroupWise16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Guerrilla16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Guerrilla16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Header() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Header", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Hey_com16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Hey_com16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Hi_Net16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Hi_Net16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property HIDE_place_marker_100px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("HIDE_place_marker_100px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property HideLink16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("HideLink16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property HideLink32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("HideLink32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property HideLinkHide() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("HideLinkHide", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property HideLinkOn() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("HideLinkOn", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property HideLinkPaner() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("HideLinkPaner", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property HideRedirect16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("HideRedirect16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property HideRedirect32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("HideRedirect32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property hiworks16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("hiworks16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property home_32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("home_32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Horde16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Horde16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Hotmail16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Hotmail16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property html16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("html16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property html32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("html32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Hushmail16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Hushmail16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        '''</summary>
        Public ReadOnly Property hyperlink() As DevExpress.Utils.Svg.SvgImage
            Get
                Dim obj As Object = ResourceManager.GetObject("hyperlink", resourceCulture)
                Return CType(obj,DevExpress.Utils.Svg.SvgImage)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property hyperlink_16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("hyperlink_16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property hyperlink_32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("hyperlink_32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property hyperlink_32x321() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("hyperlink_32x321", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property IceWarp16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("IceWarp16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property iCloud_Mail16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("iCloud_Mail16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property iinet16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("iinet16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Image_777_Hide() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Image_777_Hide", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property IMAGE_Check_Office365_Hide() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("IMAGE_Check_Office365_Hide", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property IMAGE_Check_Office365_On() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("IMAGE_Check_Office365_On", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property IMAGE_Email_Extractor_Hide() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("IMAGE_Email_Extractor_Hide", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property IMAGE_Email_Extractor_On() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("IMAGE_Email_Extractor_On", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property IMAGE_Email_Extractor_Pro_Hide() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("IMAGE_Email_Extractor_Pro_Hide", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property IMAGE_Email_Extractor_Pro_On() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("IMAGE_Email_Extractor_Pro_On", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property image_Email_From_Scanner_() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("image_Email_From_Scanner_", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property image_Email_From_Scanner_Hide() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("image_Email_From_Scanner_Hide", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property IMAGE_Email_Sorter_Hide() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("IMAGE_Email_Sorter_Hide", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property IMAGE_Email_Sorter_On() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("IMAGE_Email_Sorter_On", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property image_Email_Vaildator_Hide() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("image_Email_Vaildator_Hide", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property image_Email_Vaildator_On() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("image_Email_Vaildator_On", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property IMAGE_Encoded_PHP_Hide() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("IMAGE_Encoded_PHP_Hide", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property IMAGE_Encoded_PHP_On() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("IMAGE_Encoded_PHP_On", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property IMAGE_Extract_Domain_Last_Hide() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("IMAGE_Extract_Domain_Last_Hide", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property IMAGE_Extract_Domain_Last_On() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("IMAGE_Extract_Domain_Last_On", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property IMAGE_HideLink() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("IMAGE_HideLink", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property IMAGE_Letter__Designer_Hide() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("IMAGE_Letter__Designer_Hide", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property IMAGE_Letter__Designer_On() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("IMAGE_Letter__Designer_On", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property image_locked() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("image_locked", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property image_Multi_Linker() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("image_Multi_Linker", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property IMAGE_SMTP_order_Hide() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("IMAGE_SMTP_order_Hide", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property IMAGE_SMTP_order_On() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("IMAGE_SMTP_order_On", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property IMAGE_TGB_BSV_Hide() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("IMAGE_TGB_BSV_Hide", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property IMAGE_TGB_BSV_On() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("IMAGE_TGB_BSV_On", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property image16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("image16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Image32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Image32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property imageAccountTesterHide() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("imageAccountTesterHide", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property imageAccountTesterOn() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("imageAccountTesterOn", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property imageAttachmentHide() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("imageAttachmentHide", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property imageAttachmentOn() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("imageAttachmentOn", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property imageBitcoin() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("imageBitcoin", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property ImageCHeckSMTPHide() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("ImageCHeckSMTPHide", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property ImageCHeckSMTPHideOn() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("ImageCHeckSMTPHideOn", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property imageEncodeLinkHide() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("imageEncodeLinkHide", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property imageEncodeLinkOne() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("imageEncodeLinkOne", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property imageIMAP1MonthHide() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("imageIMAP1MonthHide", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        '''</summary>
        Public ReadOnly Property imageimport() As DevExpress.Utils.Svg.SvgImage
            Get
                Dim obj As Object = ResourceManager.GetObject("imageimport", resourceCulture)
                Return CType(obj,DevExpress.Utils.Svg.SvgImage)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property imageKinkToAttachmentHide() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("imageKinkToAttachmentHide", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property imageKinkToAttachmentOn() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("imageKinkToAttachmentOn", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property imageRemovePassHide() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("imageRemovePassHide", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property imageRemovePassOne() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("imageRemovePassOne", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property imageRisizerHide() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("imageRisizerHide", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property imageRisizerOne() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("imageRisizerOne", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property IMAP_New() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("IMAP_New", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property IMAP_PNG() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("IMAP_PNG", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property IMAPFail() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("IMAPFail", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property IMAPHide() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("IMAPHide", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property IMAPOk() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("IMAPOk", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property IMAPOn() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("IMAPOn", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property inbox_16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("inbox_16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property inbox_32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("inbox_32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property inbox16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("inbox16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property inbox32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("inbox32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        '''</summary>
        Public ReadOnly Property insertlistbox() As DevExpress.Utils.Svg.SvgImage
            Get
                Dim obj As Object = ResourceManager.GetObject("insertlistbox", resourceCulture)
                Return CType(obj,DevExpress.Utils.Svg.SvgImage)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property int_no() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("int_no", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property intetnet_Yes() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("intetnet_Yes", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property ionos() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("ionos", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property ionos16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("ionos16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Kerio_mail16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Kerio_mail16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized string similar to aol.com
        '''att.net
        '''comcast.net
        '''facebook.com
        '''gmail.com
        '''gmx.com
        '''googlemail.com
        '''google.com
        '''hotmail.com
        '''hotmail.co.uk
        '''mac.com
        '''me.com
        '''mail.com
        '''msn.com
        '''live.com
        '''sbcglobal.net
        '''verizon.net
        '''yahoo.com
        '''yahoo.co.uk
        '''email.com
        '''fastmail.fm
        '''games.com
        '''gmx.net
        '''hush.com
        '''hushmail.com
        '''icloud.com
        '''iname.com
        '''inbox.com
        '''lavabit.com
        '''love.com
        '''outlook.com
        '''pobox.com
        '''protonmail.com
        '''rocketmail.com
        '''safe-mail.net
        '''wow.com
        '''ygm.com
        '''ymail.com
        '''zoho.com
        '''yandex.com
        '''bellsouth.net
        '''charter.net
        '''cox.net
        '''earthlink [rest of string was truncated]&quot;;.
        '''</summary>
        Public ReadOnly Property knowndomain() As String
            Get
                Return ResourceManager.GetString("knowndomain", resourceCulture)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized string similar to // 0x00027E96: knowndomain‎ = &quot;aol.com\r\natt.net\r\ncomcast.net\r\nfacebook.com\r\ngmail.com\r\ngmx.com\r\ngooglemail.com\r\ngoogle.com\r\nhotmail.com\r\nhotmail.co.uk\r\nmac.com\r\nme.com\r\nmail.com\r\nmsn.com\r\nlive.com\r\nsbcglobal.net\r\nverizon.net\r\nyahoo.com\r\nyahoo.co.uk\r\nemail.com\r\nfastmail.fm\r\ngames.com\r\ngmx.net\r\nhush.com\r\nhushmail.com\r\nicloud.com\r\niname.com\r\ninbox.com\r\nlavabit.com\r\nlove.com\r\noutlook.com\r\npobox.com\r\nprotonmail.com\r\nrocketmail.com\r\nsafe-mail.net [rest of string was truncated]&quot;;.
        '''</summary>
        Public ReadOnly Property knowndomains() As String
            Get
                Return ResourceManager.GetString("knowndomains", resourceCulture)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized string similar to mx-aol.mail.gm0.yahoodns.net
        '''ff-ip4-mx-vip1.prodigy.net
        '''mx2.comcast.net
        '''msgin.vvv.facebook.com
        '''gmail-smtp-in.l.google.com
        '''mx00.gmx.net
        '''gmail-smtp-in.l.google.com
        '''aspmx.l.google.com
        '''hotmail-com.olc.protection.outlook.com
        '''eur.olc.protection.outlook.com
        '''mx3.mail.icloud.com
        '''mx6.mail.icloud.com
        '''mx00.mail.com
        '''msn-com.olc.protection.outlook.com
        '''live-com.olc.protection.outlook.com
        '''al-ip4-mx-vip2.prodigy.net
        '''mx-aol.mail.gm0.yahoodns.net
        '''mta7.am0.yahoodns.net
        '''mx-eu.mail.am0.yahoodns.net
        '''mx00.mail.c [rest of string was truncated]&quot;;.
        '''</summary>
        Public ReadOnly Property knownmx() As String
            Get
                Return ResourceManager.GetString("knownmx", resourceCulture)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property KolabNow16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("KolabNow16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Laposte16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Laposte16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Lengthlogo() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Lengthlogo", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property letter() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("letter", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Letter_Encoded_Hide() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Letter_Encoded_Hide", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Letter_Encoded_On() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Letter_Encoded_On", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Letter_Maker_PNG() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Letter_Maker_PNG", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Letter16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Letter16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Letter32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Letter32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property LetterHide() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("LetterHide", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property letterOn() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("letterOn", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property LetterPNG() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("LetterPNG", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property LetterSettings16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("LetterSettings16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property LetterSettings32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("LetterSettings32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property LetterTools16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("LetterTools16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property LetterTools32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("LetterTools32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Libero16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Libero16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        '''</summary>
        Public ReadOnly Property Link() As DevExpress.Utils.Svg.SvgImage
            Get
                Dim obj As Object = ResourceManager.GetObject("Link", resourceCulture)
                Return CType(obj,DevExpress.Utils.Svg.SvgImage)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property link_To_Attachment() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("link_To_Attachment", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        '''</summary>
        Public ReadOnly Property link1() As DevExpress.Utils.Svg.SvgImage
            Get
                Dim obj As Object = ResourceManager.GetObject("link1", resourceCulture)
                Return CType(obj,DevExpress.Utils.Svg.SvgImage)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        '''</summary>
        Public ReadOnly Property link2() As DevExpress.Utils.Svg.SvgImage
            Get
                Dim obj As Object = ResourceManager.GetObject("link2", resourceCulture)
                Return CType(obj,DevExpress.Utils.Svg.SvgImage)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property linkCool() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("linkCool", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property LinkDomain16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("LinkDomain16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property LinkDomain32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("LinkDomain32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property linked_file() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("linked_file", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property LinkEN() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("LinkEN", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property LinkEN16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("LinkEN16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property LinkEN32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("LinkEN32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property LinktoAttachmentPaner() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("LinktoAttachmentPaner", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property LinkToAttachmentUpdateHide() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("LinkToAttachmentUpdateHide", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property LinkToAttachmentUpdateOn() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("LinkToAttachmentUpdateOn", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property linkURLSender() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("linkURLSender", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property live_chat() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("live_chat", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Location_Hide() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Location_Hide", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Location_On() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Location_On", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property logo() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("logo", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Logo_Best() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Logo_Best", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Logo_Microsoft() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Logo_Microsoft", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Logo_NewBestSender() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Logo_NewBestSender", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Logo_Puzzle() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Logo_Puzzle", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Logo_VIP_Redirect() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Logo_VIP_Redirect", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property LogoBSV() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("LogoBSV", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property LogoHeader() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("LogoHeader", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Logoicon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Logoicon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property LogoLogin() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("LogoLogin", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property LogoUpdate() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("LogoUpdate", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Lotus_Notes16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Lotus_Notes16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Luxsci16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Luxsci16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Mail_com16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Mail_com16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Mail_ru16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Mail_ru16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Mailbird16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Mailbird16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Mailbox_org16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Mailbox_org16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Mailextractpng() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Mailextractpng", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Mailfence16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Mailfence16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property mailinator16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("mailinator16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        '''</summary>
        Public ReadOnly Property mailmerge() As DevExpress.Utils.Svg.SvgImage
            Get
                Dim obj As Object = ResourceManager.GetObject("mailmerge", resourceCulture)
                Return CType(obj,DevExpress.Utils.Svg.SvgImage)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        '''</summary>
        Public ReadOnly Property mailmerge1() As DevExpress.Utils.Svg.SvgImage
            Get
                Dim obj As Object = ResourceManager.GetObject("mailmerge1", resourceCulture)
                Return CType(obj,DevExpress.Utils.Svg.SvgImage)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Mailplug16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Mailplug16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property mailSort() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("mailSort", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property max_loader() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("max_loader", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property media() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("media", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Menu() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Menu", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Menu16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Menu16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Menu32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Menu32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property microsoft_32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("microsoft_32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Microsoft_36516x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Microsoft_36516x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Microsoft_image() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Microsoft_image", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property MicrosoftPaner() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("MicrosoftPaner", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Mimecast16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Mimecast16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Missive16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Missive16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property money_bag() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("money_bag", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property MS_1_and_1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("MS_1 and 1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property MS_163() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("MS_163", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property MS_263() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("MS_263", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property MS_Aliyun() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("MS_Aliyun", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property MS_Amazon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("MS_Amazon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property MS_Anazana() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("MS_Anazana", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property MS_CoreMail() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("MS_CoreMail", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property MS_EarthLink() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("MS_EarthLink", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property MS_Gmail() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("MS_Gmail", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property MS_Gmx() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("MS_Gmx", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property MS_GoDaddy() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("MS_GoDaddy", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property MS_Hinet() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("MS_Hinet", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property MS_Hotmail() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("MS_Hotmail", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property MS_iCloud() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("MS_iCloud", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property MS_iiNet() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("MS_iiNet", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property MS_Mail_Ru() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("MS_Mail.Ru", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property MS_mimeCast() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("MS_mimeCast", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property MS_nameCheap() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("MS_nameCheap", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property MS_Office_365() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("MS_Office 365", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property MS_Orange() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("MS_Orange", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property MS_Other() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("MS_Other", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property MS_Outlook() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("MS_Outlook", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property MS_ovhcloud() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("MS_ovhcloud", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property MS_QQ() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("MS_QQ", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property MS_RackSpace() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("MS_RackSpace", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property MS_Web_de() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("MS_Web.de", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property MS_Yahoo() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("MS_Yahoo", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property MS_Yandex() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("MS_Yandex", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property MS_ZambriaMail() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("MS_ZambriaMail", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property MS_Zoho() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("MS_Zoho", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property mweb16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("mweb16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Namecheap16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Namecheap16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property naver16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("naver16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property net_framework16x161() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("net_framework16x161", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property net_framework32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("net_framework32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property NetEase16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("NetEase16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property New_Hide() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("New_Hide", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property New_On() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("New_On", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property New16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("New16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property New32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("New32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property NewHide() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("NewHide", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Newton16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Newton16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property notifcationAnobis() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("notifcationAnobis", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Notworking16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Notworking16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Notworking32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Notworking32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Office365() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Office365", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Office365CheckAcccount16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Office365CheckAcccount16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Office365CheckAcccount32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Office365CheckAcccount32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Open_Xchange16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Open_Xchange16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property open16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("open16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        '''</summary>
        Public ReadOnly Property open2() As DevExpress.Utils.Svg.SvgImage
            Get
                Dim obj As Object = ResourceManager.GetObject("open2", resourceCulture)
                Return CType(obj,DevExpress.Utils.Svg.SvgImage)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        '''</summary>
        Public ReadOnly Property open21() As DevExpress.Utils.Svg.SvgImage
            Get
                Dim obj As Object = ResourceManager.GetObject("open21", resourceCulture)
                Return CType(obj,DevExpress.Utils.Svg.SvgImage)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property open32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("open32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        '''</summary>
        Public ReadOnly Property opencalendar() As DevExpress.Utils.Svg.SvgImage
            Get
                Dim obj As Object = ResourceManager.GetObject("opencalendar", resourceCulture)
                Return CType(obj,DevExpress.Utils.Svg.SvgImage)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Openmailbox16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Openmailbox16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Orange_fr16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Orange_fr16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Other16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Other16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Outlook() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Outlook", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Outlook16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Outlook16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property outlook16x161() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("outlook16x161", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property PassImage() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("PassImage", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property PasswordPaner() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("PasswordPaner", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property paste_16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("paste_16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property paste_32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("paste_32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Path16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Path16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Path32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Path32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property pause_16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("pause_16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property PDF16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("PDF16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property PDF32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("PDF32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property pending16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("pending16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property pending32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("pending32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property php16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("php16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property php32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("php32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property phpLaft16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("phpLaft16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property phpLaft32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("phpLaft32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property place_marker_100px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("place_marker_100px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property play_16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("play_16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Plesewait() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Plesewait", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property pngAttachment() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("pngAttachment", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property pngEncodedLink() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("pngEncodedLink", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property pngRidirectLink() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("pngRidirectLink", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property pngRidirectLink1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("pngRidirectLink1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property PNGSMTPHide() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("PNGSMTPHide", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property PNGSMTPOn() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("PNGSMTPOn", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property PNGSMTPONG() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("PNGSMTPONG", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property pngwing_com() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("pngwing_com", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Polarismail16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Polarismail16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Polymail16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Polymail16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Postbox16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Postbox16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Posteo16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Posteo16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Premium() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Premium", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property PrivteToolsBSV() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("PrivteToolsBSV", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property ProgressDownloadMoney() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("ProgressDownloadMoney", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property prossec() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("prossec", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property ProtonMail16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("ProtonMail16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property PuzzelPaner() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("PuzzelPaner", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Puzzle_image() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Puzzle_image", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Puzzle16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Puzzle16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Puzzle32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Puzzle32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property QQ16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("QQ16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property qr_Save() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("qr_Save", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property QrcodeBTN() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("QrcodeBTN", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property QRcodeICQ1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("QRcodeICQ1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property QRcodeSkype1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("QRcodeSkype1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property QRcodeTelegram1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("QRcodeTelegram1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property rackspace() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("rackspace", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Rackspace_16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Rackspace_16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Rainloop16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Rainloop16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property red_flag() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("red_flag", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        '''</summary>
        Public ReadOnly Property reddatabargradient() As DevExpress.Utils.Svg.SvgImage
            Get
                Dim obj As Object = ResourceManager.GetObject("reddatabargradient", resourceCulture)
                Return CType(obj,DevExpress.Utils.Svg.SvgImage)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Rediff16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Rediff16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property RedirctMicrosoftPaner() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("RedirctMicrosoftPaner", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property RedirctMicrosoftPaner1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("RedirctMicrosoftPaner1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Redirect_Link_Hide() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Redirect_Link_Hide", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Redirect_Link_On() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Redirect_Link_On", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Redirect_Logo() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Redirect_Logo", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Redirect_VIP() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Redirect_VIP", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Redirect_VIP_On() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Redirect_VIP_On", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Redirect_with_Pass() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Redirect_with_Pass", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Redirect16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Redirect16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Redirect32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Redirect32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property RedirectBrowser() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("RedirectBrowser", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property refreshpivottable_32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("refreshpivottable_32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property register_it16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("register_it16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        '''</summary>
        Public ReadOnly Property removeheader() As DevExpress.Utils.Svg.SvgImage
            Get
                Dim obj As Object = ResourceManager.GetObject("removeheader", resourceCulture)
                Return CType(obj,DevExpress.Utils.Svg.SvgImage)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property RemoveHide() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("RemoveHide", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property RemoveON() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("RemoveON", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property RemovePassFromEmail16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("RemovePassFromEmail16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property RemovePassFromEmail32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("RemovePassFromEmail32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Reset_Image() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Reset_Image", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property restart() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("restart", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Restart16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Restart16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Restart32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Restart32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property RestartApp() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("RestartApp", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property RestartBSV16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("RestartBSV16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property RestartBSV32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("RestartBSV32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property restartHide() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("restartHide", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Riseup16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Riseup16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Roundcube16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Roundcube16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property RR16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("RR16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        '''</summary>
        Public ReadOnly Property rulerhorizontal() As DevExpress.Utils.Svg.SvgImage
            Get
                Dim obj As Object = ResourceManager.GetObject("rulerhorizontal", resourceCulture)
                Return CType(obj,DevExpress.Utils.Svg.SvgImage)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        '''</summary>
        Public ReadOnly Property rulervertical() As DevExpress.Utils.Svg.SvgImage
            Get
                Dim obj As Object = ResourceManager.GetObject("rulervertical", resourceCulture)
                Return CType(obj,DevExpress.Utils.Svg.SvgImage)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property RunBox16x1616x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("RunBox16x1616x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property RunBox32x3216x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("RunBox32x3216x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property RustDesk32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("RustDesk32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Save_Image() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Save_Image", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property saveall_32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("saveall_32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property scalix16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("scalix16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property ScannerImage() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("ScannerImage", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property search_16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("search_16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property search_32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("search_32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        '''</summary>
        Public ReadOnly Property security_key() As DevExpress.Utils.Svg.SvgImage
            Get
                Dim obj As Object = ResourceManager.GetObject("security_key", resourceCulture)
                Return CType(obj,DevExpress.Utils.Svg.SvgImage)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        '''</summary>
        Public ReadOnly Property security_key1() As DevExpress.Utils.Svg.SvgImage
            Get
                Dim obj As Object = ResourceManager.GetObject("security_key1", resourceCulture)
                Return CType(obj,DevExpress.Utils.Svg.SvgImage)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Send() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Send", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Send_Mail_Hide() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Send_Mail_Hide", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Send_Mail_On() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Send_Mail_On", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Send_PNG() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Send_PNG", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Send16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Send16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        '''</summary>
        Public ReadOnly Property sendbackward() As DevExpress.Utils.Svg.SvgImage
            Get
                Dim obj As Object = ResourceManager.GetObject("sendbackward", resourceCulture)
                Return CType(obj,DevExpress.Utils.Svg.SvgImage)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property sendbackward_16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("sendbackward_16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Sender32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Sender32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property SendMailHide() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("SendMailHide", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property SendMailOn() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("SendMailOn", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        '''</summary>
        Public ReadOnly Property sendmht() As DevExpress.Utils.Svg.SvgImage
            Get
                Dim obj As Object = ResourceManager.GetObject("sendmht", resourceCulture)
                Return CType(obj,DevExpress.Utils.Svg.SvgImage)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property sendpdf_32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("sendpdf_32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Serverdata16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Serverdata16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property servermode_16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("servermode_16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property servermode_32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("servermode_32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Services_Hide() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Services_Hide", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Services_On() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Services_On", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property ServicesBSV() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("ServicesBSV", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property settings16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("settings16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property settings32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("settings32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Seznam16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Seznam16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property shopping() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("shopping", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property shopping_16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("shopping_16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property shopping_32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("shopping_32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property shopping_cart() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("shopping_cart", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property shoppingOn() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("shoppingOn", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        '''</summary>
        Public ReadOnly Property showlegend() As DevExpress.Utils.Svg.SvgImage
            Get
                Dim obj As Object = ResourceManager.GetObject("showlegend", resourceCulture)
                Return CType(obj,DevExpress.Utils.Svg.SvgImage)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property showtestreport_16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("showtestreport_16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property signal() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("signal", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property signal_32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("signal_32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property signal16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("signal16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Sina16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Sina16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Site_Hide() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Site_Hide", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Site_On() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Site_On", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property skull() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("skull", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Skype() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Skype", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Skype_Hide() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Skype_Hide", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property SMTP16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("SMTP16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property SMTP32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("SMTP32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property smtpServer() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("smtpServer", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property smtpserverOlder() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("smtpserverOlder", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property smtpServerOne() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("smtpServerOne", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property smtptestacc() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("smtptestacc", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property smtptestaccount16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("smtptestaccount16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property smtptestaccount32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("smtptestaccount32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property SMTPTester16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("SMTPTester16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property SMTPTester32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("SMTPTester32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        '''</summary>
        Public ReadOnly Property snapinsertfooter() As DevExpress.Utils.Svg.SvgImage
            Get
                Dim obj As Object = ResourceManager.GetObject("snapinsertfooter", resourceCulture)
                Return CType(obj,DevExpress.Utils.Svg.SvgImage)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        '''</summary>
        Public ReadOnly Property snapinsertheader() As DevExpress.Utils.Svg.SvgImage
            Get
                Dim obj As Object = ResourceManager.GetObject("snapinsertheader", resourceCulture)
                Return CType(obj,DevExpress.Utils.Svg.SvgImage)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        '''</summary>
        Public ReadOnly Property snapinsertheader1() As DevExpress.Utils.Svg.SvgImage
            Get
                Dim obj As Object = ResourceManager.GetObject("snapinsertheader1", resourceCulture)
                Return CType(obj,DevExpress.Utils.Svg.SvgImage)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        '''</summary>
        Public ReadOnly Property snapinsertheader2() As DevExpress.Utils.Svg.SvgImage
            Get
                Dim obj As Object = ResourceManager.GetObject("snapinsertheader2", resourceCulture)
                Return CType(obj,DevExpress.Utils.Svg.SvgImage)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        '''</summary>
        Public ReadOnly Property snaptogglefieldhighlighting() As DevExpress.Utils.Svg.SvgImage
            Get
                Dim obj As Object = ResourceManager.GetObject("snaptogglefieldhighlighting", resourceCulture)
                Return CType(obj,DevExpress.Utils.Svg.SvgImage)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Soverin16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Soverin16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Spark_mail16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Spark_mail16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Spider_Redirect() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Spider_Redirect", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property SPiderPanel() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("SPiderPanel", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property SpiderRedirect32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("SpiderRedirect32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Spike16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Spike16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property sql() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("sql", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Squirrelmail16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Squirrelmail16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Start_Image() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Start_Image", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Start_Image16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Start_Image16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Start_Image32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Start_Image32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Start16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Start16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property StartMail16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("StartMail16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property status_16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("status_16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property status_32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("status_32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property stop_16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("stop_16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property stop_32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("stop_32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Stop_Image() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Stop_Image", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Stop16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Stop16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Stop32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Stop32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        '''</summary>
        Public ReadOnly Property StopPoint() As DevExpress.Utils.Svg.SvgImage
            Get
                Dim obj As Object = ResourceManager.GetObject("StopPoint", resourceCulture)
                Return CType(obj,DevExpress.Utils.Svg.SvgImage)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Strato16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Strato16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Superhuman16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Superhuman16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property support_16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("support_16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property support_32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("support_32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Support_Hide() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Support_Hide", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Support_on() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Support_on", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property SupportImage() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("SupportImage", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Synaq16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Synaq16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property T_Online16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("T_Online16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        '''</summary>
        Public ReadOnly Property task() As DevExpress.Utils.Svg.SvgImage
            Get
                Dim obj As Object = ResourceManager.GetObject("task", resourceCulture)
                Return CType(obj,DevExpress.Utils.Svg.SvgImage)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Telegram_Hide() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Telegram_Hide", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Telegram_On() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Telegram_On", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property telegram16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("telegram16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property telegram32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("telegram32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property TelegramImage() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("TelegramImage", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Temp16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Temp16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Terra16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Terra16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Thexyz16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Thexyz16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Thunderbird16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Thunderbird16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property time16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("time16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property time32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("time32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property timeConvert() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("timeConvert", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property timeOut_PNG() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("timeOut_PNG", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        '''</summary>
        Public ReadOnly Property trackingchanges_locktracking() As DevExpress.Utils.Svg.SvgImage
            Get
                Dim obj As Object = ResourceManager.GetObject("trackingchanges_locktracking", resourceCulture)
                Return CType(obj,DevExpress.Utils.Svg.SvgImage)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        '''</summary>
        Public ReadOnly Property trackingchanges_locktracking1() As DevExpress.Utils.Svg.SvgImage
            Get
                Dim obj As Object = ResourceManager.GetObject("trackingchanges_locktracking1", resourceCulture)
                Return CType(obj,DevExpress.Utils.Svg.SvgImage)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        '''</summary>
        Public ReadOnly Property trackingchanges_locktracking2() As DevExpress.Utils.Svg.SvgImage
            Get
                Dim obj As Object = ResourceManager.GetObject("trackingchanges_locktracking2", resourceCulture)
                Return CType(obj,DevExpress.Utils.Svg.SvgImage)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        '''</summary>
        Public ReadOnly Property trackingchanges_locktracking3() As DevExpress.Utils.Svg.SvgImage
            Get
                Dim obj As Object = ResourceManager.GetObject("trackingchanges_locktracking3", resourceCulture)
                Return CType(obj,DevExpress.Utils.Svg.SvgImage)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Transfer16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Transfer16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Transfer32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Transfer32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property trash_16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("trash_16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property trash_16x161() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("trash_16x161", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property trash_16x162() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("trash_16x162", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property trash_16x163() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("trash_16x163", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property trash_16x164() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("trash_16x164", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property trash_16x165() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("trash_16x165", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property trash_32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("trash_32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property trash_32x321() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("trash_32x321", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property trash_32x322() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("trash_32x322", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property trash_32x323() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("trash_32x323", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property trash_Image() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("trash_Image", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property trash16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("trash16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property trash32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("trash32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Tutanota16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Tutanota16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property txtC() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("txtC", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property txtL() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("txtL", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized string similar to &lt;html&gt;
        '''&lt;head&gt;
        '''&lt;meta http-equiv=&quot;Content-Type&quot;
        '''content=&quot;text/html; charset=iso-8859-1&quot;&gt;
        '''&lt;meta name=&quot;Generator&quot; content=&quot;10.70&quot;&gt;
        '''&lt;meta name=&quot;viewport&quot; content=&quot;width=device-width, initial-scale=1&quot;&gt;
        '''&lt;meta name=&quot;x-apple-disable-message-reformatting&quot;&gt;
        '''&lt;meta name=&quot;format-detection&quot; content=&quot;telephone=no&quot;&gt;
        '''&lt;style type=&quot;text/css&quot;&gt;#outlook a { padding:0;}.es-button { mso-style-priority:100!important; text-decoration:none!important;}a[x-apple-data-detectors] { color:inherit!important; text-decoration:none!imp [rest of string was truncated]&quot;;.
        '''</summary>
        Public ReadOnly Property txtLetter() As String
            Get
                Return ResourceManager.GetString("txtLetter", resourceCulture)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property txtR() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("txtR", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        '''</summary>
        Public ReadOnly Property updatetableofcontents() As DevExpress.Utils.Svg.SvgImage
            Get
                Dim obj As Object = ResourceManager.GetObject("updatetableofcontents", resourceCulture)
                Return CType(obj,DevExpress.Utils.Svg.SvgImage)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property upload_16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("upload_16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property User_Hide() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("User_Hide", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property User_On() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("User_On", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property usergroup_16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("usergroup_16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property usergroup_32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("usergroup_32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property usernamee() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("usernamee", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property usernamee2__1_() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("usernamee2__1_", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property UserNameImage() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("UserNameImage", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Utilities_PNG() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Utilities_PNG", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property UtilitiesHide() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("UtilitiesHide", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property UtilitiesOn() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("UtilitiesOn", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Valid16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Valid16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Valid32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Valid32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property VenomEN() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("VenomEN", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property VenomENImage() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("VenomENImage", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property VenomImage() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("VenomImage", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Verizon16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Verizon16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        '''</summary>
        Public ReadOnly Property viewmergeddata() As DevExpress.Utils.Svg.SvgImage
            Get
                Dim obj As Object = ResourceManager.GetObject("viewmergeddata", resourceCulture)
                Return CType(obj,DevExpress.Utils.Svg.SvgImage)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        '''</summary>
        Public ReadOnly Property viewtablegridlines() As DevExpress.Utils.Svg.SvgImage
            Get
                Dim obj As Object = ResourceManager.GetObject("viewtablegridlines", resourceCulture)
                Return CType(obj,DevExpress.Utils.Svg.SvgImage)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property VIP_LEGEND_() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("VIP_LEGEND_", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property VIP_PRIME_PNG() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("VIP_PRIME_PNG", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property VIP_ULTRA_PNG() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("VIP_ULTRA_PNG", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property VIPPaner() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("VIPPaner", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property VIPRedirect16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("VIPRedirect16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property VIPRedirect32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("VIPRedirect32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property virgin16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("virgin16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Vivaldi_Mail16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Vivaldi_Mail16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property walla16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("walla16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Wallet() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Wallet", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property web_de16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("web_de16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property web16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("web16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property web23x23() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("web23x23", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property widthLogo() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("widthLogo", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property withPass16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("withPass16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property withPass32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("withPass32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Wolf_EN() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Wolf_EN", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Wolf_image() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Wolf_image", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Yahoo() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Yahoo", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Yahoo_image() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Yahoo_image", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property yahoo16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("yahoo16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property YahooPanel() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("YahooPanel", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property yandex16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("yandex16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property youtube_16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("youtube_16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property youtube_32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("youtube_32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property YouTube_Hide() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("YouTube_Hide", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property YouTube_On() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("YouTube_On", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property youtube16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("youtube16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property youtube32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("youtube32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property zambrea() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("zambrea", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Ziggo16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Ziggo16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Zimbra16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Zimbra16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Public ReadOnly Property Zoho_Mail16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Zoho_Mail16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
    End Module
End Namespace
