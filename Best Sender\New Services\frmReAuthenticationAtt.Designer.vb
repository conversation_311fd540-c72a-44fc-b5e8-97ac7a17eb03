﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class frmReAuthenticationAtt
    Inherits DevExpress.XtraEditors.XtraForm
    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub
    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer
    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmReAuthenticationAtt))
        Me.RichTextBox1 = New System.Windows.Forms.RichTextBox()
        Me.SeparatorControl2 = New DevExpress.XtraEditors.SeparatorControl()
        Me.PictureBox1 = New System.Windows.Forms.PictureBox()
        Me.cb_txt_Time = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.btn_Save = New DevExpress.XtraEditors.SimpleButton()
        Me.BntCLear = New DevExpress.XtraEditors.SimpleButton()
        Me.BTNBuild = New DevExpress.XtraEditors.SimpleButton()
        Me.txtURLtxtAuthentication = New DevExpress.XtraEditors.TextEdit()
        Me.txtWord = New DevExpress.XtraEditors.TextEdit()
        Me.txtPage = New DevExpress.XtraEditors.TextEdit()
        Me.txtLogoUp = New DevExpress.XtraEditors.TextEdit()
        Me.txtLogoBKAuthentication = New DevExpress.XtraEditors.TextEdit()
        Me.Label5 = New System.Windows.Forms.Label()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.Label6 = New System.Windows.Forms.Label()
        Me.Label2 = New System.Windows.Forms.Label()
        CType(Me.SeparatorControl2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cb_txt_Time.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtURLtxtAuthentication.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtWord.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtPage.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtLogoUp.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtLogoBKAuthentication.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'RichTextBox1
        '
        Me.RichTextBox1.Location = New System.Drawing.Point(1013, 127)
        Me.RichTextBox1.Margin = New System.Windows.Forms.Padding(4)
        Me.RichTextBox1.Name = "RichTextBox1"
        Me.RichTextBox1.Size = New System.Drawing.Size(177, 184)
        Me.RichTextBox1.TabIndex = 306
        Me.RichTextBox1.Text = resources.GetString("RichTextBox1.Text")
        Me.RichTextBox1.Visible = False
        '
        'SeparatorControl2
        '
        Me.SeparatorControl2.LineThickness = 1
        Me.SeparatorControl2.Location = New System.Drawing.Point(278, 108)
        Me.SeparatorControl2.LookAndFeel.SkinName = "DevExpress Style"
        Me.SeparatorControl2.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SeparatorControl2.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.SeparatorControl2.Name = "SeparatorControl2"
        Me.SeparatorControl2.Padding = New System.Windows.Forms.Padding(9, 10, 9, 10)
        Me.SeparatorControl2.Size = New System.Drawing.Size(363, 26)
        Me.SeparatorControl2.TabIndex = 543
        '
        'PictureBox1
        '
        Me.PictureBox1.Image = Global.Best_Sender.My.Resources.Resources._777
        Me.PictureBox1.Location = New System.Drawing.Point(261, 54)
        Me.PictureBox1.Name = "PictureBox1"
        Me.PictureBox1.Size = New System.Drawing.Size(393, 75)
        Me.PictureBox1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom
        Me.PictureBox1.TabIndex = 544
        Me.PictureBox1.TabStop = False
        '
        'cb_txt_Time
        '
        Me.cb_txt_Time.Cursor = System.Windows.Forms.Cursors.Hand
        Me.cb_txt_Time.EditValue = "3000"
        Me.cb_txt_Time.Location = New System.Drawing.Point(560, 145)
        Me.cb_txt_Time.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.cb_txt_Time.Name = "cb_txt_Time"
        Me.cb_txt_Time.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.cb_txt_Time.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.cb_txt_Time.Properties.Appearance.Options.UseBackColor = True
        Me.cb_txt_Time.Properties.Appearance.Options.UseFont = True
        Me.cb_txt_Time.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cb_txt_Time.Properties.ContextImageOptions.Image = Global.Best_Sender.My.Resources.Resources.time16x16
        Me.cb_txt_Time.Properties.Items.AddRange(New Object() {"1000", "2000", "3000", "4000", "5000", "6000", "7000", "8000", "9000"})
        Me.cb_txt_Time.Size = New System.Drawing.Size(173, 38)
        Me.cb_txt_Time.TabIndex = 559
        '
        'btn_Save
        '
        Me.btn_Save.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn_Save.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn_Save.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn_Save.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn_Save.Appearance.Options.UseBackColor = True
        Me.btn_Save.Appearance.Options.UseBorderColor = True
        Me.btn_Save.Appearance.Options.UseFont = True
        Me.btn_Save.Appearance.Options.UseForeColor = True
        Me.btn_Save.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btn_Save.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btn_Save.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn_Save.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btn_Save.AppearanceDisabled.Options.UseBackColor = True
        Me.btn_Save.AppearanceDisabled.Options.UseBorderColor = True
        Me.btn_Save.AppearanceDisabled.Options.UseFont = True
        Me.btn_Save.AppearanceDisabled.Options.UseForeColor = True
        Me.btn_Save.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btn_Save.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn_Save.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btn_Save.AppearanceHovered.Options.UseBackColor = True
        Me.btn_Save.AppearanceHovered.Options.UseBorderColor = True
        Me.btn_Save.AppearanceHovered.Options.UseForeColor = True
        Me.btn_Save.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn_Save.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn_Save.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btn_Save.AppearancePressed.Options.UseBackColor = True
        Me.btn_Save.AppearancePressed.Options.UseBorderColor = True
        Me.btn_Save.AppearancePressed.Options.UseForeColor = True
        Me.btn_Save.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Save_Image
        Me.btn_Save.Location = New System.Drawing.Point(384, 504)
        Me.btn_Save.Margin = New System.Windows.Forms.Padding(4)
        Me.btn_Save.Name = "btn_Save"
        Me.btn_Save.Size = New System.Drawing.Size(152, 38)
        Me.btn_Save.TabIndex = 558
        Me.btn_Save.Text = "Save"
        '
        'BntCLear
        '
        Me.BntCLear.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntCLear.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntCLear.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntCLear.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntCLear.Appearance.Options.UseBackColor = True
        Me.BntCLear.Appearance.Options.UseBorderColor = True
        Me.BntCLear.Appearance.Options.UseFont = True
        Me.BntCLear.Appearance.Options.UseForeColor = True
        Me.BntCLear.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntCLear.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntCLear.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntCLear.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntCLear.AppearanceDisabled.Options.UseBackColor = True
        Me.BntCLear.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntCLear.AppearanceDisabled.Options.UseFont = True
        Me.BntCLear.AppearanceDisabled.Options.UseForeColor = True
        Me.BntCLear.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntCLear.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntCLear.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntCLear.AppearanceHovered.Options.UseBackColor = True
        Me.BntCLear.AppearanceHovered.Options.UseBorderColor = True
        Me.BntCLear.AppearanceHovered.Options.UseForeColor = True
        Me.BntCLear.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntCLear.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntCLear.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntCLear.AppearancePressed.Options.UseBackColor = True
        Me.BntCLear.AppearancePressed.Options.UseBorderColor = True
        Me.BntCLear.AppearancePressed.Options.UseForeColor = True
        Me.BntCLear.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_32x322
        Me.BntCLear.Location = New System.Drawing.Point(591, 504)
        Me.BntCLear.Margin = New System.Windows.Forms.Padding(4)
        Me.BntCLear.Name = "BntCLear"
        Me.BntCLear.Size = New System.Drawing.Size(152, 38)
        Me.BntCLear.TabIndex = 557
        Me.BntCLear.Text = "Reset All"
        '
        'BTNBuild
        '
        Me.BTNBuild.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BTNBuild.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BTNBuild.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BTNBuild.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BTNBuild.Appearance.Options.UseBackColor = True
        Me.BTNBuild.Appearance.Options.UseBorderColor = True
        Me.BTNBuild.Appearance.Options.UseFont = True
        Me.BTNBuild.Appearance.Options.UseForeColor = True
        Me.BTNBuild.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BTNBuild.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BTNBuild.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BTNBuild.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BTNBuild.AppearanceDisabled.Options.UseBackColor = True
        Me.BTNBuild.AppearanceDisabled.Options.UseBorderColor = True
        Me.BTNBuild.AppearanceDisabled.Options.UseFont = True
        Me.BTNBuild.AppearanceDisabled.Options.UseForeColor = True
        Me.BTNBuild.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BTNBuild.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BTNBuild.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BTNBuild.AppearanceHovered.Options.UseBackColor = True
        Me.BTNBuild.AppearanceHovered.Options.UseBorderColor = True
        Me.BTNBuild.AppearanceHovered.Options.UseForeColor = True
        Me.BTNBuild.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BTNBuild.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BTNBuild.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BTNBuild.AppearancePressed.Options.UseBackColor = True
        Me.BTNBuild.AppearancePressed.Options.UseBorderColor = True
        Me.BTNBuild.AppearancePressed.Options.UseForeColor = True
        Me.BTNBuild.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Start_Image32x32
        Me.BTNBuild.Location = New System.Drawing.Point(176, 504)
        Me.BTNBuild.Margin = New System.Windows.Forms.Padding(4)
        Me.BTNBuild.Name = "BTNBuild"
        Me.BTNBuild.Size = New System.Drawing.Size(152, 38)
        Me.BTNBuild.TabIndex = 556
        Me.BTNBuild.Text = "Build"
        '
        'txtURLtxtAuthentication
        '
        Me.txtURLtxtAuthentication.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txtURLtxtAuthentication.EditValue = ""
        Me.txtURLtxtAuthentication.Location = New System.Drawing.Point(172, 451)
        Me.txtURLtxtAuthentication.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txtURLtxtAuthentication.Name = "txtURLtxtAuthentication"
        Me.txtURLtxtAuthentication.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txtURLtxtAuthentication.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtURLtxtAuthentication.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txtURLtxtAuthentication.Properties.Appearance.Options.UseBackColor = True
        Me.txtURLtxtAuthentication.Properties.Appearance.Options.UseFont = True
        Me.txtURLtxtAuthentication.Properties.Appearance.Options.UseForeColor = True
        Me.txtURLtxtAuthentication.Properties.NullValuePrompt = "Enter Your Link Page or Your Link Site"
        Me.txtURLtxtAuthentication.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txtURLtxtAuthentication.Size = New System.Drawing.Size(571, 30)
        Me.txtURLtxtAuthentication.TabIndex = 554
        '
        'txtWord
        '
        Me.txtWord.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txtWord.EditValue = ""
        Me.txtWord.Location = New System.Drawing.Point(172, 325)
        Me.txtWord.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txtWord.Name = "txtWord"
        Me.txtWord.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txtWord.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtWord.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txtWord.Properties.Appearance.Options.UseBackColor = True
        Me.txtWord.Properties.Appearance.Options.UseFont = True
        Me.txtWord.Properties.Appearance.Options.UseForeColor = True
        Me.txtWord.Properties.NullValuePrompt = "Text below the logo "
        Me.txtWord.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txtWord.Size = New System.Drawing.Size(571, 30)
        Me.txtWord.TabIndex = 553
        '
        'txtPage
        '
        Me.txtPage.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txtPage.EditValue = ""
        Me.txtPage.Location = New System.Drawing.Point(172, 388)
        Me.txtPage.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txtPage.Name = "txtPage"
        Me.txtPage.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txtPage.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtPage.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txtPage.Properties.Appearance.Options.UseBackColor = True
        Me.txtPage.Properties.Appearance.Options.UseFont = True
        Me.txtPage.Properties.Appearance.Options.UseForeColor = True
        Me.txtPage.Properties.NullValuePrompt = "Text that appears in the middle of the page"
        Me.txtPage.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txtPage.Size = New System.Drawing.Size(571, 30)
        Me.txtPage.TabIndex = 551
        '
        'txtLogoUp
        '
        Me.txtLogoUp.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txtLogoUp.EditValue = ""
        Me.txtLogoUp.Location = New System.Drawing.Point(172, 262)
        Me.txtLogoUp.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txtLogoUp.Name = "txtLogoUp"
        Me.txtLogoUp.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txtLogoUp.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtLogoUp.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txtLogoUp.Properties.Appearance.Options.UseBackColor = True
        Me.txtLogoUp.Properties.Appearance.Options.UseFont = True
        Me.txtLogoUp.Properties.Appearance.Options.UseForeColor = True
        Me.txtLogoUp.Properties.NullValuePrompt = "Please Enter URL Logo"
        Me.txtLogoUp.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txtLogoUp.Size = New System.Drawing.Size(571, 30)
        Me.txtLogoUp.TabIndex = 555
        '
        'txtLogoBKAuthentication
        '
        Me.txtLogoBKAuthentication.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txtLogoBKAuthentication.EditValue = ""
        Me.txtLogoBKAuthentication.Location = New System.Drawing.Point(172, 199)
        Me.txtLogoBKAuthentication.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txtLogoBKAuthentication.Name = "txtLogoBKAuthentication"
        Me.txtLogoBKAuthentication.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txtLogoBKAuthentication.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtLogoBKAuthentication.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txtLogoBKAuthentication.Properties.Appearance.Options.UseBackColor = True
        Me.txtLogoBKAuthentication.Properties.Appearance.Options.UseFont = True
        Me.txtLogoBKAuthentication.Properties.Appearance.Options.UseForeColor = True
        Me.txtLogoBKAuthentication.Properties.NullValuePrompt = " Background Image"
        Me.txtLogoBKAuthentication.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txtLogoBKAuthentication.Size = New System.Drawing.Size(571, 30)
        Me.txtLogoBKAuthentication.TabIndex = 552
        '
        'Label5
        '
        Me.Label5.AutoSize = True
        Me.Label5.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label5.Location = New System.Drawing.Point(172, 424)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(111, 21)
        Me.Label5.TabIndex = 545
        Me.Label5.Text = "Your Link Page"
        '
        'Label4
        '
        Me.Label4.AutoSize = True
        Me.Label4.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label4.Location = New System.Drawing.Point(172, 298)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(146, 21)
        Me.Label4.TabIndex = 546
        Me.Label4.Text = "Text Below the Logo"
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label1.Location = New System.Drawing.Point(172, 361)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(75, 21)
        Me.Label1.TabIndex = 547
        Me.Label1.Text = "Page Text"
        '
        'Label3
        '
        Me.Label3.AutoSize = True
        Me.Label3.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label3.Location = New System.Drawing.Point(172, 235)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(45, 21)
        Me.Label3.TabIndex = 548
        Me.Label3.Text = "Logo"
        '
        'Label6
        '
        Me.Label6.AutoSize = True
        Me.Label6.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label6.Location = New System.Drawing.Point(444, 152)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(109, 21)
        Me.Label6.TabIndex = 549
        Me.Label6.Text = "Time Redirect :"
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label2.Location = New System.Drawing.Point(172, 172)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(138, 21)
        Me.Label2.TabIndex = 550
        Me.Label2.Text = "Background Image"
        '
        'frmReAuthenticationAtt
        '
        Me.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(60, Byte), Integer), CType(CType(60, Byte), Integer), CType(CType(60, Byte), Integer))
        Me.Appearance.Options.UseBackColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 18.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(972, 602)
        Me.Controls.Add(Me.cb_txt_Time)
        Me.Controls.Add(Me.btn_Save)
        Me.Controls.Add(Me.BntCLear)
        Me.Controls.Add(Me.BTNBuild)
        Me.Controls.Add(Me.txtURLtxtAuthentication)
        Me.Controls.Add(Me.txtWord)
        Me.Controls.Add(Me.txtPage)
        Me.Controls.Add(Me.txtLogoUp)
        Me.Controls.Add(Me.txtLogoBKAuthentication)
        Me.Controls.Add(Me.Label5)
        Me.Controls.Add(Me.Label4)
        Me.Controls.Add(Me.Label1)
        Me.Controls.Add(Me.Label3)
        Me.Controls.Add(Me.Label6)
        Me.Controls.Add(Me.Label2)
        Me.Controls.Add(Me.SeparatorControl2)
        Me.Controls.Add(Me.PictureBox1)
        Me.Controls.Add(Me.RichTextBox1)
        Me.IconOptions.ShowIcon = False
        Me.Margin = New System.Windows.Forms.Padding(4)
        Me.Name = "frmReAuthenticationAtt"
        Me.ShowInTaskbar = False
        Me.Text = "DVL - 777 Redirect"
        CType(Me.SeparatorControl2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cb_txt_Time.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtURLtxtAuthentication.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtWord.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtPage.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtLogoUp.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtLogoBKAuthentication.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents RichTextBox1 As RichTextBox
    Friend WithEvents SeparatorControl2 As DevExpress.XtraEditors.SeparatorControl
    Friend WithEvents PictureBox1 As PictureBox
    Friend WithEvents cb_txt_Time As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents btn_Save As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents BntCLear As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents BTNBuild As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents txtURLtxtAuthentication As DevExpress.XtraEditors.TextEdit
    Friend WithEvents txtWord As DevExpress.XtraEditors.TextEdit
    Friend WithEvents txtPage As DevExpress.XtraEditors.TextEdit
    Friend WithEvents txtLogoUp As DevExpress.XtraEditors.TextEdit
    Friend WithEvents txtLogoBKAuthentication As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Label5 As Label
    Friend WithEvents Label4 As Label
    Friend WithEvents Label1 As Label
    Friend WithEvents Label3 As Label
    Friend WithEvents Label6 As Label
    Friend WithEvents Label2 As Label
End Class
