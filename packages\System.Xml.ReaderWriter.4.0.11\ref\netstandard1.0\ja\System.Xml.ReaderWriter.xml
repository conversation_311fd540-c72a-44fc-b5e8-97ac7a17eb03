﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Xml.ReaderWriter</name>
  </assembly>
  <members>
    <member name="T:System.Xml.ConformanceLevel">
      <summary>
        <see cref="T:System.Xml.XmlReader" /> オブジェクトおよび <see cref="T:System.Xml.XmlWriter" /> オブジェクトで実行する、入力チェックまたは出力チェックの量を指定します。</summary>
    </member>
    <member name="F:System.Xml.ConformanceLevel.Auto">
      <summary>
        <see cref="T:System.Xml.XmlReader" /> オブジェクトまたは <see cref="T:System.Xml.XmlWriter" /> オブジェクトは、ドキュメント レベルのチェックまたはフラグメント レベルのチェックを実行する必要があるかどうかを自動的に検出し、適切なチェックを実行します。別の <see cref="T:System.Xml.XmlReader" /> オブジェクトまたは <see cref="T:System.Xml.XmlWriter" /> オブジェクトをラップしている場合、外側のオブジェクトは追加の準拠のチェックを実行しません。準拠のチェックは、基になるオブジェクトだけで実行されます。準拠レベルの決定方法の詳細については、<see cref="P:System.Xml.XmlReaderSettings.ConformanceLevel" /> プロパティと <see cref="P:System.Xml.XmlWriterSettings.ConformanceLevel" /> プロパティを参照してください。</summary>
    </member>
    <member name="F:System.Xml.ConformanceLevel.Document">
      <summary>XML データは、W3C によって定義された整形式の XML 1.0 ドキュメント のルールに準拠します。</summary>
    </member>
    <member name="F:System.Xml.ConformanceLevel.Fragment">
      <summary>XML データは、W3C によって定義された整形式の XML フラグメントです。</summary>
    </member>
    <member name="T:System.Xml.DtdProcessing">
      <summary>DTD を処理するためのオプションを指定します。<see cref="T:System.Xml.DtdProcessing" /> 列挙体は <see cref="T:System.Xml.XmlReaderSettings" /> クラスによって使用されます。</summary>
    </member>
    <member name="F:System.Xml.DtdProcessing.Ignore">
      <summary>DOCTYPE 要素は無視されます。DTD 処理は発生しません。</summary>
    </member>
    <member name="F:System.Xml.DtdProcessing.Prohibit">
      <summary>DTD を検出したときに、DTD が禁止されていることを示すメッセージと共に <see cref="T:System.Xml.XmlException" /> をスローします。これが既定の動作です。</summary>
    </member>
    <member name="T:System.Xml.IXmlLineInfo">
      <summary>クラスが行情報および位置情報を返せるようにするインターフェイスを提供します。</summary>
    </member>
    <member name="M:System.Xml.IXmlLineInfo.HasLineInfo">
      <summary>クラスが行情報を返すことができるかどうかを示す値を取得します。</summary>
      <returns>
        <see cref="P:System.Xml.IXmlLineInfo.LineNumber" /> および <see cref="P:System.Xml.IXmlLineInfo.LinePosition" /> を提供できる場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="P:System.Xml.IXmlLineInfo.LineNumber">
      <summary>現在の行番号を取得します。</summary>
      <returns>現在の行番号。または行情報が取得できない場合は 0。たとえば、<see cref="M:System.Xml.IXmlLineInfo.HasLineInfo" /> は false を返します。</returns>
    </member>
    <member name="P:System.Xml.IXmlLineInfo.LinePosition">
      <summary>現在の行の位置を取得します。</summary>
      <returns>現在の行の位置。または行情報が取得できない場合は 0。たとえば、<see cref="M:System.Xml.IXmlLineInfo.HasLineInfo" /> は false を返します。</returns>
    </member>
    <member name="T:System.Xml.IXmlNamespaceResolver">
      <summary>プレフィックスと名前空間の一連の割り当てに対する読み取り専用アクセスを提供します。</summary>
    </member>
    <member name="M:System.Xml.IXmlNamespaceResolver.GetNamespacesInScope(System.Xml.XmlNamespaceScope)">
      <summary>現在スコープ内にあるプレフィックスと名前空間の間に定義された割り当てのコレクションを取得します。</summary>
      <returns>現在のスコープ内にある名前空間が格納された <see cref="T:System.Collections.IDictionary" />。</returns>
      <param name="scope">返される名前空間ノードの種類を指定する <see cref="T:System.Xml.XmlNamespaceScope" /> 値。</param>
    </member>
    <member name="M:System.Xml.IXmlNamespaceResolver.LookupNamespace(System.String)">
      <summary>指定したプレフィックスに割り当てられた名前空間 URI を取得します。</summary>
      <returns>プレフィックスに割り当てられている名前空間 URI。このプレフィックスに名前空間 URI が割り当てられていない場合は null。</returns>
      <param name="prefix">検索対象の名前空間 URI を持つプレフィックス。</param>
    </member>
    <member name="M:System.Xml.IXmlNamespaceResolver.LookupPrefix(System.String)">
      <summary>指定した名前空間 URI に割り当てられたプレフィックスを取得します。</summary>
      <returns>名前空間 URI に割り当てられているプレフィックス。この名前空間 URI にプレフィックスが割り当てられていない場合は null。</returns>
      <param name="namespaceName">検索対象のプレフィックスを持つ名前空間 URI。</param>
    </member>
    <member name="T:System.Xml.NamespaceHandling">
      <summary>
        <see cref="T:System.Xml.XmlWriter" /> で重複する名前空間宣言を削除するかどうかを指定します。</summary>
    </member>
    <member name="F:System.Xml.NamespaceHandling.Default">
      <summary>重複する名前空間宣言が削除されないように指定します。</summary>
    </member>
    <member name="F:System.Xml.NamespaceHandling.OmitDuplicates">
      <summary>重複する名前空間宣言を削除するように指定します。重複する名前空間を削除するには、プレフィックスと名前空間が一致している必要があります。</summary>
    </member>
    <member name="T:System.Xml.NameTable">
      <summary>シングルスレッド <see cref="T:System.Xml.XmlNameTable" /> を実装します。</summary>
    </member>
    <member name="M:System.Xml.NameTable.#ctor">
      <summary>NameTable クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Xml.NameTable.Add(System.Char[],System.Int32,System.Int32)">
      <summary>指定した文字列を最小単位に分割し、NameTable に追加します。</summary>
      <returns>最小単位に分割された文字列。または NameTable に既に存在している場合は既存の文字列。<paramref name="len" /> が 0 の場合は、String.Empty が返されます。</returns>
      <param name="key">追加する文字列を格納している文字配列。</param>
      <param name="start">文字列の最初の文字を指定する配列の、0 から始まるインデックス番号。</param>
      <param name="len">文字列の文字数。</param>
      <exception cref="T:System.IndexOutOfRangeException">0 &gt; <paramref name="start" />または<paramref name="start" /> &gt;= <paramref name="key" /> .Lengthまたは<paramref name="len" /> &gt;= <paramref name="key" /> .Length<paramref name="len" /> =0 の場合は、上記の条件によって例外がスローされることはありません。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="len" /> &lt; 0</exception>
    </member>
    <member name="M:System.Xml.NameTable.Add(System.String)">
      <summary>指定した文字列を最小単位に分割し、NameTable に追加します。</summary>
      <returns>最小単位に分割された文字列。NameTable に既に存在している場合は既存の文字列。</returns>
      <param name="key">追加する文字列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> は null なので、</exception>
    </member>
    <member name="M:System.Xml.NameTable.Get(System.Char[],System.Int32,System.Int32)">
      <summary>指定した配列内の指定した範囲の文字と同じ文字を含む、最小単位に分割された文字列を取得します。</summary>
      <returns>最小単位に分割された文字列。文字列がまだ最小単位に分割されていない場合は null。<paramref name="len" /> が 0 の場合は、String.Empty が返されます。</returns>
      <param name="key">検索対象の名前を格納している文字配列。</param>
      <param name="start">名前の最初の文字を指定する配列の、0 から始まるインデックス番号。</param>
      <param name="len">名前の文字数。</param>
      <exception cref="T:System.IndexOutOfRangeException">0 &gt; <paramref name="start" />または<paramref name="start" /> &gt;= <paramref name="key" /> .Lengthまたは<paramref name="len" /> &gt;= <paramref name="key" /> .Length<paramref name="len" /> =0 の場合は、上記の条件によって例外がスローされることはありません。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="len" /> &lt; 0</exception>
    </member>
    <member name="M:System.Xml.NameTable.Get(System.String)">
      <summary>指定した値を持つ最小単位に分割された文字列を取得します。</summary>
      <returns>最小単位に分割された文字列オブジェクト。または文字列がまだ最小単位に分割されていない場合は null。</returns>
      <param name="value">検索対象の名前。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> は null なので、</exception>
    </member>
    <member name="T:System.Xml.NewLineHandling">
      <summary>改行の処理方法を指定します。</summary>
    </member>
    <member name="F:System.Xml.NewLineHandling.Entitize">
      <summary>改行文字をエンティティ化します。この設定では、正規化 <see cref="T:System.Xml.XmlReader" /> で出力を読み取るときにすべての文字が保持されます。</summary>
    </member>
    <member name="F:System.Xml.NewLineHandling.None">
      <summary>改行文字を変更しません。出力は入力と同じになります。</summary>
    </member>
    <member name="F:System.Xml.NewLineHandling.Replace">
      <summary>
        <see cref="P:System.Xml.XmlWriterSettings.NewLineChars" /> プロパティに指定されている文字と一致するように、改行文字を置き換えます。</summary>
    </member>
    <member name="T:System.Xml.ReadState">
      <summary>リーダーの状態を指定します。</summary>
    </member>
    <member name="F:System.Xml.ReadState.Closed">
      <summary>
        <see cref="M:System.Xml.XmlReader.Close" /> メソッドが呼び出されています。</summary>
    </member>
    <member name="F:System.Xml.ReadState.EndOfFile">
      <summary>ファイルの末尾に正常に到達しています。</summary>
    </member>
    <member name="F:System.Xml.ReadState.Error">
      <summary>読み取り操作を継続できないようにするエラーが発生しました。</summary>
    </member>
    <member name="F:System.Xml.ReadState.Initial">
      <summary>Read メソッドが呼び出されていません。</summary>
    </member>
    <member name="F:System.Xml.ReadState.Interactive">
      <summary>Read メソッドが呼び出されています。リーダーで追加のメソッドが呼び出される場合があります。</summary>
    </member>
    <member name="T:System.Xml.WriteState">
      <summary>
        <see cref="T:System.Xml.XmlWriter" /> の状態を指定します。</summary>
    </member>
    <member name="F:System.Xml.WriteState.Attribute">
      <summary>属性値が書き込まれていることを示します。</summary>
    </member>
    <member name="F:System.Xml.WriteState.Closed">
      <summary>
        <see cref="M:System.Xml.XmlWriter.Close" /> メソッドが呼び出されていることを示します。</summary>
    </member>
    <member name="F:System.Xml.WriteState.Content">
      <summary>要素の内容が書き込まれていることを示します。</summary>
    </member>
    <member name="F:System.Xml.WriteState.Element">
      <summary>要素開始タグが書き込まれていることを示します。</summary>
    </member>
    <member name="F:System.Xml.WriteState.Error">
      <summary>例外がスローされ、<see cref="T:System.Xml.XmlWriter" /> が無効な状態になっています。<see cref="M:System.Xml.XmlWriter.Close" /> メソッドを呼び出すと、<see cref="T:System.Xml.XmlWriter" /> を <see cref="F:System.Xml.WriteState.Closed" /> 状態にできます。それ以外の <see cref="T:System.Xml.XmlWriter" /> メソッドを呼び出した場合、<see cref="T:System.InvalidOperationException" /> が発生します。</summary>
    </member>
    <member name="F:System.Xml.WriteState.Prolog">
      <summary>プロローグが書き込まれていることを示します。</summary>
    </member>
    <member name="F:System.Xml.WriteState.Start">
      <summary>Write メソッドがまだ呼び出されていないことを示します。</summary>
    </member>
    <member name="T:System.Xml.XmlConvert">
      <summary>XML 名をエンコードおよびデコードし、共通言語ランタイム型と XML スキーマ定義言語 (XSD) 型との間で変換を実行するメソッドを提供します。データ型を変換する場合、返される値はロケールには依存しません。</summary>
    </member>
    <member name="M:System.Xml.XmlConvert.DecodeName(System.String)">
      <summary>名前をデコードします。このメソッドは、<see cref="M:System.Xml.XmlConvert.EncodeName(System.String)" /> メソッドおよび <see cref="M:System.Xml.XmlConvert.EncodeLocalName(System.String)" /> メソッドの変換を元に戻します。</summary>
      <returns>デコードされた名前。</returns>
      <param name="name">変換対象の名前。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.EncodeLocalName(System.String)">
      <summary>名前を有効な XML ローカル名に変換します。</summary>
      <returns>エンコードされた名前。</returns>
      <param name="name">エンコードする名前。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.EncodeName(System.String)">
      <summary>名前を有効な XML 名に変換します。</summary>
      <returns>無効な文字をエスケープ文字列で置換した名前を返します。</returns>
      <param name="name">変換する対象の名前。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.EncodeNmToken(System.String)">
      <summary>XML 仕様に従って有効な名前であることを検証します。</summary>
      <returns>エンコードされた名前。</returns>
      <param name="name">エンコードする名前。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToBoolean(System.String)">
      <summary>
        <see cref="T:System.String" /> を等価の <see cref="T:System.Boolean" /> に変換します。</summary>
      <returns>Boolean 値。つまり true または false。</returns>
      <param name="s">変換する文字列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> does not represent a Boolean value. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToByte(System.String)">
      <summary>
        <see cref="T:System.String" /> を等価の <see cref="T:System.Byte" /> に変換します。</summary>
      <returns>文字列と等価の Byte。</returns>
      <param name="s">変換する文字列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Byte.MinValue" /> or greater than <see cref="F:System.Byte.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToChar(System.String)">
      <summary>
        <see cref="T:System.String" /> を等価の <see cref="T:System.Char" /> に変換します。</summary>
      <returns>単一の文字を表す Char。</returns>
      <param name="s">変換する単一の文字を含んでいる文字列。</param>
      <exception cref="T:System.ArgumentNullException">The value of the <paramref name="s" /> parameter is null. </exception>
      <exception cref="T:System.FormatException">The <paramref name="s" /> parameter contains more than one character. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDateTime(System.String,System.Xml.XmlDateTimeSerializationMode)">
      <summary>指定された <see cref="T:System.Xml.XmlDateTimeSerializationMode" /> を使用して、<see cref="T:System.String" /> を <see cref="T:System.DateTime" /> に変換します</summary>
      <returns>
        <see cref="T:System.String" /> と等価の <see cref="T:System.DateTime" />。</returns>
      <param name="s">変換する <see cref="T:System.String" /> 値。</param>
      <param name="dateTimeOption">世界協定時刻 (UTC) 日付を使用している場合に、日付を現地時間に変換するか、または UTC のままにするかを指定する <see cref="T:System.Xml.XmlDateTimeSerializationMode" /> 値の 1 つ。</param>
      <exception cref="T:System.NullReferenceException">
        <paramref name="s" /> is null.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="dateTimeOption" /> value is null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is an empty string or is not in a valid format.</exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDateTimeOffset(System.String)">
      <summary>指定した <see cref="T:System.String" /> を等価の <see cref="T:System.DateTimeOffset" /> に変換します。</summary>
      <returns>指定した文字列と等価の <see cref="T:System.DateTimeOffset" />。</returns>
      <param name="s">変換する文字列。メモ   文字列は、W3C 勧告の XML dateTime 型のサブセットに準拠している必要があります。詳細については、http://www.w3.org/TR/xmlschema-2/#dateTime を参照してください。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The argument passed to this method is outside the range of allowable values.For information about allowable values, see <see cref="T:System.DateTimeOffset" />.</exception>
      <exception cref="T:System.FormatException">The argument passed to this method does not conform to a subset of the W3C Recommendations for the XML dateTime type.For more information see http://www.w3.org/TR/xmlschema-2/#dateTime.</exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDateTimeOffset(System.String,System.String)">
      <summary>指定した <see cref="T:System.String" /> を等価の <see cref="T:System.DateTimeOffset" /> に変換します。</summary>
      <returns>指定した文字列と等価の <see cref="T:System.DateTimeOffset" />。</returns>
      <param name="s">変換する文字列。</param>
      <param name="format">変換前の <paramref name="s" /> の形式。フォーマット パラメーターには、W3C 勧告の XML dateTime 型の任意のサブセットを指定できます。(詳細については、http://www.w3.org/TR/xmlschema-2/#dateTime を参照してください)。 文字列 <paramref name="s" /> はこの形式に対して妥当性が検査されます。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> or <paramref name="format" /> is an empty string or is not in the specified format.</exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDateTimeOffset(System.String,System.String[])">
      <summary>指定した <see cref="T:System.String" /> を等価の <see cref="T:System.DateTimeOffset" /> に変換します。</summary>
      <returns>指定した文字列と等価の <see cref="T:System.DateTimeOffset" />。</returns>
      <param name="s">変換する文字列。</param>
      <param name="formats">
        <paramref name="s" /> に変換可能な形式の配列。<paramref name="formats" /> の各形式には、W3C 勧告の XML dateTime 型の任意のサブセットを指定できます。(詳細については、http://www.w3.org/TR/xmlschema-2/#dateTime を参照してください)。 文字列 <paramref name="s" /> は、これらの形式のいずれかに対して妥当性が検査されます。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDecimal(System.String)">
      <summary>
        <see cref="T:System.String" /> を等価の <see cref="T:System.Decimal" /> に変換します。</summary>
      <returns>文字列と等価の Decimal。</returns>
      <param name="s">変換する文字列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Decimal.MinValue" /> or greater than <see cref="F:System.Decimal.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDouble(System.String)">
      <summary>
        <see cref="T:System.String" /> を等価の <see cref="T:System.Double" /> に変換します。</summary>
      <returns>文字列と等価の Double。</returns>
      <param name="s">変換する文字列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Double.MinValue" /> or greater than <see cref="F:System.Double.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToGuid(System.String)">
      <summary>
        <see cref="T:System.String" /> を等価の <see cref="T:System.Guid" /> に変換します。</summary>
      <returns>文字列と等価の Guid。</returns>
      <param name="s">変換する文字列。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToInt16(System.String)">
      <summary>
        <see cref="T:System.String" /> を等価の <see cref="T:System.Int16" /> に変換します。</summary>
      <returns>文字列と等価の Int16。</returns>
      <param name="s">変換する文字列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Int16.MinValue" /> or greater than <see cref="F:System.Int16.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToInt32(System.String)">
      <summary>
        <see cref="T:System.String" /> を等価の <see cref="T:System.Int32" /> に変換します。</summary>
      <returns>文字列と等価の Int32。</returns>
      <param name="s">変換する文字列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Int32.MinValue" /> or greater than <see cref="F:System.Int32.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToInt64(System.String)">
      <summary>
        <see cref="T:System.String" /> を等価の <see cref="T:System.Int64" /> に変換します。</summary>
      <returns>文字列と等価の Int64。</returns>
      <param name="s">変換する文字列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Int64.MinValue" /> or greater than <see cref="F:System.Int64.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToSByte(System.String)">
      <summary>
        <see cref="T:System.String" /> を等価の <see cref="T:System.SByte" /> に変換します。</summary>
      <returns>文字列と等価の SByte。</returns>
      <param name="s">変換する文字列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.SByte.MinValue" /> or greater than <see cref="F:System.SByte.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToSingle(System.String)">
      <summary>
        <see cref="T:System.String" /> を等価の <see cref="T:System.Single" /> に変換します。</summary>
      <returns>文字列と等価の Single。</returns>
      <param name="s">変換する文字列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Single.MinValue" /> or greater than <see cref="F:System.Single.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Boolean)">
      <summary>
        <see cref="T:System.Boolean" /> を <see cref="T:System.String" /> に変換します。</summary>
      <returns>Boolean の文字列形式。つまり "true" または "false"。</returns>
      <param name="value">変換する値。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Byte)">
      <summary>
        <see cref="T:System.Byte" /> を <see cref="T:System.String" /> に変換します。</summary>
      <returns>Byte の文字列形式。</returns>
      <param name="value">変換する値。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Char)">
      <summary>
        <see cref="T:System.Char" /> を <see cref="T:System.String" /> に変換します。</summary>
      <returns>Char の文字列形式。</returns>
      <param name="value">変換する値。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.DateTime,System.Xml.XmlDateTimeSerializationMode)">
      <summary>指定された <see cref="T:System.Xml.XmlDateTimeSerializationMode" /> を使用して、<see cref="T:System.DateTime" /> を <see cref="T:System.String" /> に変換します。</summary>
      <returns>
        <see cref="T:System.DateTime" /> と等価の <see cref="T:System.String" />。</returns>
      <param name="value">変換する <see cref="T:System.DateTime" /> 値。</param>
      <param name="dateTimeOption">
        <see cref="T:System.DateTime" /> 値を処理する方法を指定する <see cref="T:System.Xml.XmlDateTimeSerializationMode" /> 値の 1 つ。</param>
      <exception cref="T:System.ArgumentException">The <paramref name="dateTimeOption" /> value is not valid.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> or <paramref name="dateTimeOption" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.DateTimeOffset)">
      <summary>指定した <see cref="T:System.DateTimeOffset" /> を <see cref="T:System.String" /> に変換します。</summary>
      <returns>指定した <see cref="T:System.DateTimeOffset" /> の <see cref="T:System.String" /> 表現。</returns>
      <param name="value">変換される <see cref="T:System.DateTimeOffset" />。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.DateTimeOffset,System.String)">
      <summary>指定した <see cref="T:System.DateTimeOffset" /> を指定した形式の <see cref="T:System.String" /> に変換します。</summary>
      <returns>指定した <see cref="T:System.DateTimeOffset" /> の指定した形式での <see cref="T:System.String" /> 表現。</returns>
      <param name="value">変換される <see cref="T:System.DateTimeOffset" />。</param>
      <param name="format">変換後の <paramref name="s" /> の形式。フォーマット パラメーターには、W3C 勧告の XML dateTime 型の任意のサブセットを指定できます。(詳細については、http://www.w3.org/TR/xmlschema-2/#dateTime を参照してください)。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Decimal)">
      <summary>
        <see cref="T:System.Decimal" /> を <see cref="T:System.String" /> に変換します。</summary>
      <returns>Decimal の文字列形式。</returns>
      <param name="value">変換する値。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Double)">
      <summary>
        <see cref="T:System.Double" /> を <see cref="T:System.String" /> に変換します。</summary>
      <returns>Double の文字列形式。</returns>
      <param name="value">変換する値。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Guid)">
      <summary>
        <see cref="T:System.Guid" /> を <see cref="T:System.String" /> に変換します。</summary>
      <returns>Guid の文字列形式。</returns>
      <param name="value">変換する値。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Int16)">
      <summary>
        <see cref="T:System.Int16" /> を <see cref="T:System.String" /> に変換します。</summary>
      <returns>Int16 の文字列形式。</returns>
      <param name="value">変換する値。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Int32)">
      <summary>
        <see cref="T:System.Int32" /> を <see cref="T:System.String" /> に変換します。</summary>
      <returns>Int32 の文字列形式。</returns>
      <param name="value">変換する値。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Int64)">
      <summary>
        <see cref="T:System.Int64" /> を <see cref="T:System.String" /> に変換します。</summary>
      <returns>Int64 の文字列形式。</returns>
      <param name="value">変換する値。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.SByte)">
      <summary>
        <see cref="T:System.SByte" /> を <see cref="T:System.String" /> に変換します。</summary>
      <returns>SByte の文字列形式。</returns>
      <param name="value">変換する値。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Single)">
      <summary>
        <see cref="T:System.Single" /> を <see cref="T:System.String" /> に変換します。</summary>
      <returns>Single の文字列形式。</returns>
      <param name="value">変換する値。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.TimeSpan)">
      <summary>
        <see cref="T:System.TimeSpan" /> を <see cref="T:System.String" /> に変換します。</summary>
      <returns>TimeSpan の文字列形式。</returns>
      <param name="value">変換する値。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.UInt16)">
      <summary>
        <see cref="T:System.UInt16" /> を <see cref="T:System.String" /> に変換します。</summary>
      <returns>UInt16 の文字列形式。</returns>
      <param name="value">変換する値。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.UInt32)">
      <summary>
        <see cref="T:System.UInt32" /> を <see cref="T:System.String" /> に変換します。</summary>
      <returns>UInt32 の文字列形式。</returns>
      <param name="value">変換する値。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.UInt64)">
      <summary>
        <see cref="T:System.UInt64" /> を <see cref="T:System.String" /> に変換します。</summary>
      <returns>UInt64 の文字列形式。</returns>
      <param name="value">変換する値。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToTimeSpan(System.String)">
      <summary>
        <see cref="T:System.String" /> を等価の <see cref="T:System.TimeSpan" /> に変換します。</summary>
      <returns>文字列と等価の TimeSpan。</returns>
      <param name="s">変換する文字列。文字列の形式は、W3C『XML Schema Part 2: Datatypes』の期間に関する勧告に準拠している必要があります。</param>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in correct format to represent a TimeSpan value. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToUInt16(System.String)">
      <summary>
        <see cref="T:System.String" /> を等価の <see cref="T:System.UInt16" /> に変換します。</summary>
      <returns>文字列と等価の UInt16。</returns>
      <param name="s">変換する文字列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.UInt16.MinValue" /> or greater than <see cref="F:System.UInt16.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToUInt32(System.String)">
      <summary>
        <see cref="T:System.String" /> を等価の <see cref="T:System.UInt32" /> に変換します。</summary>
      <returns>文字列と等価の UInt32。</returns>
      <param name="s">変換する文字列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.UInt32.MinValue" /> or greater than <see cref="F:System.UInt32.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToUInt64(System.String)">
      <summary>
        <see cref="T:System.String" /> を等価の <see cref="T:System.UInt64" /> に変換します。</summary>
      <returns>文字列と等価の UInt64。</returns>
      <param name="s">変換する文字列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.UInt64.MinValue" /> or greater than <see cref="F:System.UInt64.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyName(System.String)">
      <summary>W3C 勧告『Extended Markup Language』に照らし合わせて、名前が有効な名前であることを検証します。</summary>
      <returns>有効な XML 名の場合は、その名前。</returns>
      <param name="name">検証対象となる名前。</param>
      <exception cref="T:System.Xml.XmlException">
        <paramref name="name" /> is not a valid XML name. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is null or String.Empty. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyNCName(System.String)">
      <summary>W3C 勧告『Extended Markup Language』に照らし合わせて、名前が有効な NCName であることを検証します。NCName は、コロンを入れることができない名前です。</summary>
      <returns>有効な NCName の場合は、その名前。</returns>
      <param name="name">検証対象となる名前。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is null or String.Empty. </exception>
      <exception cref="T:System.Xml.XmlException">
        <paramref name="name" /> is not a valid non-colon name. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyNMTOKEN(System.String)">
      <summary>W3C 勧告『XML Schema Part 2: Datatypes』に照らし合わせて、文字列が有効な NMTOKEN であることを検証します。</summary>
      <returns>有効な NMTOKEN の場合は、名前トークン。</returns>
      <param name="name">検証する文字列。</param>
      <exception cref="T:System.Xml.XmlException">The string is not a valid name token.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is null.</exception>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyPublicId(System.String)">
      <summary>文字列引数のすべての文字が有効な公開識別子の文字の場合、渡された文字列インスタンスを返します。</summary>
      <returns>引数のすべての文字が有効な公開識別子の文字の場合、渡された文字列を返します。</returns>
      <param name="publicId">検証対象の識別子が格納されている <see cref="T:System.String" />。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyWhitespace(System.String)">
      <summary>文字列引数のすべての文字が有効な空白文字の場合、渡された文字列インスタンスを返します。</summary>
      <returns>文字列引数のすべての文字が有効な空白文字の場合は渡された文字列インスタンスを返し、それ以外の場合は null を返します。</returns>
      <param name="content">検証する <see cref="T:System.String" />。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyXmlChars(System.String)">
      <summary>文字列引数の中にあるすべての文字とサロゲート ペア文字が有効な XML 文字である場合は、渡された文字列が返されます。それ以外の場合は、見つかった最初の無効な文字に関する情報を含む XmlException がスローされます。</summary>
      <returns>文字列引数の中にあるすべての文字とサロゲート ペア文字が有効な XML 文字である場合は、渡された文字列が返されます。それ以外の場合は、見つかった最初の無効な文字に関する情報を含む XmlException がスローされます。</returns>
      <param name="content">検証対象の文字が格納されている <see cref="T:System.String" />。</param>
    </member>
    <member name="T:System.Xml.XmlDateTimeSerializationMode">
      <summary>文字列と <see cref="T:System.DateTime" /> の間で変換を行うときに、時刻の値をどのように処理するかを指定します。</summary>
    </member>
    <member name="F:System.Xml.XmlDateTimeSerializationMode.Local">
      <summary>現地時刻として処理します。<see cref="T:System.DateTime" /> オブジェクトが世界協定時刻 (UTC: Coordinated Universal Time) を表す場合、これを現地時刻に変換します。</summary>
    </member>
    <member name="F:System.Xml.XmlDateTimeSerializationMode.RoundtripKind">
      <summary>変換を行うときに、タイム ゾーン情報が保持されます。</summary>
    </member>
    <member name="F:System.Xml.XmlDateTimeSerializationMode.Unspecified">
      <summary>
        <see cref="T:System.DateTime" /> を文字列に変換する場合は、現地時刻として処理します。</summary>
    </member>
    <member name="F:System.Xml.XmlDateTimeSerializationMode.Utc">
      <summary>UTC として処理します。<see cref="T:System.DateTime" /> オブジェクトが現地時刻を表す場合は、UTC に変換します。</summary>
    </member>
    <member name="T:System.Xml.XmlException">
      <summary>最後の例外に関する詳細情報を返します。</summary>
    </member>
    <member name="M:System.Xml.XmlException.#ctor">
      <summary>XmlException クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Xml.XmlException.#ctor(System.String)">
      <summary>指定したエラー メッセージを使用して、XmlException クラスの新しいインスタンスを初期化します。</summary>
      <param name="message">エラーの説明。</param>
    </member>
    <member name="M:System.Xml.XmlException.#ctor(System.String,System.Exception)">
      <summary>XmlException クラスの新しいインスタンスを初期化します。</summary>
      <param name="message">エラー状態の説明。</param>
      <param name="innerException">XmlException をスローした <see cref="T:System.Exception" /> (存在する場合)。この値は、null の場合もあります。</param>
    </member>
    <member name="M:System.Xml.XmlException.#ctor(System.String,System.Exception,System.Int32,System.Int32)">
      <summary>指定したメッセージ、内部例外、行番号、行の位置を使用して、XmlException クラスの新しいインスタンスを初期化します。</summary>
      <param name="message">エラーの説明。</param>
      <param name="innerException">現在の例外の原因である例外。この値は、null の場合もあります。</param>
      <param name="lineNumber">エラーの発生場所を示す行番号。</param>
      <param name="linePosition">エラーの発生場所を示す行の位置。</param>
    </member>
    <member name="P:System.Xml.XmlException.LineNumber">
      <summary>エラーの発生場所を示す行番号を取得します。</summary>
      <returns>エラーの発生場所を示す行番号。</returns>
    </member>
    <member name="P:System.Xml.XmlException.LinePosition">
      <summary>エラーの発生場所を示す行の位置を取得します。</summary>
      <returns>エラーの発生場所を示す行の位置。</returns>
    </member>
    <member name="P:System.Xml.XmlException.Message">
      <summary>現在の例外を説明するメッセージを取得します。</summary>
      <returns>例外の原因を説明するエラー メッセージ。</returns>
    </member>
    <member name="T:System.Xml.XmlNamespaceManager">
      <summary>名前空間を解決し、コレクションに追加および削除して、これらの名前空間に対するスコープ管理を提供します。</summary>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.#ctor(System.Xml.XmlNameTable)">
      <summary>
        <see cref="T:System.Xml.XmlNameTable" /> を指定して、<see cref="T:System.Xml.XmlNamespaceManager" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="nameTable">使用する <see cref="T:System.Xml.XmlNameTable" />。</param>
      <exception cref="T:System.NullReferenceException">null is passed to the constructor </exception>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.AddNamespace(System.String,System.String)">
      <summary>指定した名前空間をコレクションに追加します。</summary>
      <param name="prefix">追加する名前空間に関連付けるプリフィックス。String.Empty を使用して、既定の名前空間を追加します。メモ XML Path Language (XPath) 式の名前空間の解決に <see cref="T:System.Xml.XmlNamespaceManager" /> を使用する場合は、プレフィックスを指定する必要があります。XPath 式にプレフィックスが含まれていない場合、名前空間 URI (Uniform Resource Identifier) は、空の名前空間であると見なされます。XPath 式および <see cref="T:System.Xml.XmlNamespaceManager" /> の詳細については、<see cref="M:System.Xml.XmlNode.SelectNodes(System.String)" /> メソッドおよび <see cref="M:System.Xml.XPath.XPathExpression.SetContext(System.Xml.XmlNamespaceManager)" /> メソッドの説明を参照してください。</param>
      <param name="uri">追加する名前空間。</param>
      <exception cref="T:System.ArgumentException">The value for <paramref name="prefix" /> is "xml" or "xmlns". </exception>
      <exception cref="T:System.ArgumentNullException">The value for <paramref name="prefix" /> or <paramref name="uri" /> is null. </exception>
    </member>
    <member name="P:System.Xml.XmlNamespaceManager.DefaultNamespace">
      <summary>既定の名前空間の名前空間 URI を取得します。</summary>
      <returns>既定の名前空間の名前空間 URI を返します。既定の名前空間がない場合は String.Empty を返します。</returns>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.GetEnumerator">
      <summary>
        <see cref="T:System.Xml.XmlNamespaceManager" /> 内の名前空間を反復処理するために使用する列挙子を返します。</summary>
      <returns>
        <see cref="T:System.Xml.XmlNamespaceManager" /> によって格納されているプレフィックスを含む <see cref="T:System.Collections.IEnumerator" />。</returns>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.GetNamespacesInScope(System.Xml.XmlNamespaceScope)">
      <summary>現在スコープ内にある名前空間を列挙するために使用できる、プレフィックスをキーとした、名前空間の名前のコレクションを取得します。</summary>
      <returns>現在スコープ内にある名前空間とプレフィックスのペアのコレクション。</returns>
      <param name="scope">返される名前空間ノードの種類を指定する列挙値。</param>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.HasNamespace(System.String)">
      <summary>提供されたプリフィックスに現在のプッシュされたスコープに対して定義された名前空間があるかどうかを示す値を取得します。</summary>
      <returns>定義された名前空間がある場合は true。それ以外の場合は false。</returns>
      <param name="prefix">検索する対象の名前空間のプリフィックス。 </param>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.LookupNamespace(System.String)">
      <summary>指定したプリフィックスの名前空間 URI を取得します。</summary>
      <returns>
        <paramref name="prefix" /> の名前空間 URI を返します。マップされた名前空間がない場合は null を返します。返される文字列は最小単位に分割されます。最小単位に分割された文字列の詳細については、<see cref="T:System.Xml.XmlNameTable" /> クラスを参照してください。</returns>
      <param name="prefix">解決する対象となる名前空間 URI を持つプリフィックス。既定の名前空間に一致するようにするには、String.Empty を渡します。</param>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.LookupPrefix(System.String)">
      <summary>指定した名前空間 URI に対して宣言されたプリフィックスを検索します。</summary>
      <returns>一致するプリフィックス。割り当てられたプリフィックスがない場合、メソッドは String.Empty を返します。null 値を指定した場合、null が返されます。</returns>
      <param name="uri">プリフィックスに対して解決する名前空間。</param>
    </member>
    <member name="P:System.Xml.XmlNamespaceManager.NameTable">
      <summary>このオブジェクトに関連付けられている <see cref="T:System.Xml.XmlNameTable" /> を取得します。</summary>
      <returns>このオブジェクトが使用する <see cref="T:System.Xml.XmlNameTable" />。</returns>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.PopScope">
      <summary>名前空間スコープをスタックからポップします。</summary>
      <returns>スタックに名前空間スコープが残されている場合は true。ポップする名前空間がそれ以上ない場合は false。</returns>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.PushScope">
      <summary>名前空間スコープをスタックにプッシュします。</summary>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.RemoveNamespace(System.String,System.String)">
      <summary>指定したプリフィックスの指定した名前空間を削除します。</summary>
      <param name="prefix">名前空間のプリフィックス。</param>
      <param name="uri">指定したプリフィックスに対して削除する名前空間。削除された名前空間は、現在の名前空間スコープに由来しています。現在のスコープ外の名前空間は無視されます。</param>
      <exception cref="T:System.ArgumentNullException">The value of <paramref name="prefix" /> or <paramref name="uri" /> is null. </exception>
    </member>
    <member name="T:System.Xml.XmlNamespaceScope">
      <summary>名前空間スコープを定義します。</summary>
    </member>
    <member name="F:System.Xml.XmlNamespaceScope.All">
      <summary>現在のノードのスコープに定義されているすべての名前空間。この名前空間には、常に暗黙的に宣言される xmlns:xml 名前空間が含まれます。返される名前空間の順序は定義されません。</summary>
    </member>
    <member name="F:System.Xml.XmlNamespaceScope.ExcludeXml">
      <summary>常に暗黙的に宣言される xmlns:xml 名前空間を除く、現在のノードのスコープに定義されているすべての名前空間。返される名前空間の順序は定義されません。</summary>
    </member>
    <member name="F:System.Xml.XmlNamespaceScope.Local">
      <summary>現在のノードでローカルに定義されているすべての名前空間。</summary>
    </member>
    <member name="T:System.Xml.XmlNameTable">
      <summary>最小単位に分割された文字列オブジェクトのテーブル。</summary>
    </member>
    <member name="M:System.Xml.XmlNameTable.#ctor">
      <summary>
        <see cref="T:System.Xml.XmlNameTable" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Xml.XmlNameTable.Add(System.Char[],System.Int32,System.Int32)">
      <summary>派生クラスでオーバーライドされると、指定した文字列を最小単位に分割し、XmlNameTable に追加します。</summary>
      <returns>新しく最小単位に分割された文字列。既に存在している場合は既存の文字列。長さが 0 の場合は、String.Empty が返されます。</returns>
      <param name="array">追加する名前を格納している文字配列。</param>
      <param name="offset">名前の最初の文字を指定する配列の、0 から始まるインデックス番号。</param>
      <param name="length">名前の文字数。</param>
      <exception cref="T:System.IndexOutOfRangeException">0 &gt; <paramref name="offset" />または<paramref name="offset" /> &gt;= <paramref name="array" />.Lengthまたは<paramref name="length" /> &gt; <paramref name="array" />.Length<paramref name="length" /> =0 の場合は、上記の条件によって例外がスローされることはありません。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="length" /> &lt; 0</exception>
    </member>
    <member name="M:System.Xml.XmlNameTable.Add(System.String)">
      <summary>派生クラスでオーバーライドされると、指定した文字列を最小単位に分割し、XmlNameTable に追加します。</summary>
      <returns>新しく最小単位に分割された文字列。既に存在している場合は既存の文字列。</returns>
      <param name="array">追加する名前。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> は null なので、</exception>
    </member>
    <member name="M:System.Xml.XmlNameTable.Get(System.Char[],System.Int32,System.Int32)">
      <summary>派生クラスでオーバーライドされると、指定した配列内の指定した範囲の文字と同じ文字を含む、最小単位に分割された文字列を取得します。</summary>
      <returns>最小単位に分割された文字列。文字列がまだ最小単位に分割されていない場合は null。<paramref name="length" /> が 0 の場合は、String.Empty が返されます。</returns>
      <param name="array">検索対象の名前を格納している文字配列。</param>
      <param name="offset">名前の最初の文字を指定する配列の、0 から始まるインデックス番号。</param>
      <param name="length">名前の文字数。</param>
      <exception cref="T:System.IndexOutOfRangeException">0 &gt; <paramref name="offset" />または<paramref name="offset" /> &gt;= <paramref name="array" />.Lengthまたは<paramref name="length" /> &gt; <paramref name="array" />.Length<paramref name="length" /> =0 の場合は、上記の条件によって例外がスローされることはありません。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="length" /> &lt; 0</exception>
    </member>
    <member name="M:System.Xml.XmlNameTable.Get(System.String)">
      <summary>派生クラスでオーバーライドされると、指定した文字列と同じ値を含む最小単位に分割された文字列を取得します。</summary>
      <returns>最小単位に分割された文字列。文字列がまだ最小単位に分割されていない場合は null。</returns>
      <param name="array">検索する名前。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> は null なので、</exception>
    </member>
    <member name="T:System.Xml.XmlNodeType">
      <summary>ノードの型を指定します。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Attribute">
      <summary>属性 (例 : id='123')。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.CDATA">
      <summary>CDATA セクション (例 : &lt;![CDATA[my escaped text]]&gt;)。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Comment">
      <summary>コメント (例 : &lt;!-- my comment --&gt;)。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Document">
      <summary>ドキュメント ツリーのルートして、XML ドキュメント全体へのアクセスを実現するドキュメント オブジェクト。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.DocumentFragment">
      <summary>ドキュメント フラグメント。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.DocumentType">
      <summary>次のようなタグで示されるドキュメント型宣言 (例 : &lt;!DOCTYPE...&gt;)。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Element">
      <summary>要素 (例 : &lt;item&gt;)。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.EndElement">
      <summary>終了要素タグ (例 : &lt;/item&gt;)。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.EndEntity">
      <summary>
        <see cref="M:System.Xml.XmlReader.ResolveEntity" /> を呼び出した結果、XmlReader がエンティティ置換の末尾に到達したときに返されます。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Entity">
      <summary>エンティティ宣言 (例 : &lt;!ENTITY...&gt;)。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.EntityReference">
      <summary>エンティティへの参照 (例 : &amp;num;)。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.None">
      <summary>Read メソッドが呼び出されなかった場合に、<see cref="T:System.Xml.XmlReader" /> によって返されます。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Notation">
      <summary>ドキュメント型宣言内の表記 (例 : &lt;!NOTATION...&gt;)。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.ProcessingInstruction">
      <summary>処理命令 (例 : &lt;?pi test?&gt;)。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.SignificantWhitespace">
      <summary>混合コンテンツ モデル内のマークアップ間にある空白、または xml:space="preserve" スコープ内の空白。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Text">
      <summary>ノードのテキストの内容。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Whitespace">
      <summary>マークアップ間の空白。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.XmlDeclaration">
      <summary>XML 宣言 (例 : &lt;?xml version='1.0'?&gt;)。</summary>
    </member>
    <member name="T:System.Xml.XmlParserContext">
      <summary>XML フラグメントを解析するために <see cref="T:System.Xml.XmlReader" /> が必要とするコンテキスト情報をすべて提供します。</summary>
    </member>
    <member name="M:System.Xml.XmlParserContext.#ctor(System.Xml.XmlNameTable,System.Xml.XmlNamespaceManager,System.String,System.String,System.String,System.String,System.String,System.String,System.Xml.XmlSpace)">
      <summary>
        <see cref="T:System.Xml.XmlNameTable" />、<see cref="T:System.Xml.XmlNamespaceManager" />、ベース URI、xml:lang、xml:space、ドキュメント型のそれぞれの値を指定して、XmlParserContext クラスの新しいインスタンスを初期化します。</summary>
      <param name="nt">文字列を最小単位に分割するために使用する <see cref="T:System.Xml.XmlNameTable" />。このパラメーターが null の場合は、<paramref name="nsMgr" /> を構築するために使用される名前テーブルが代わりに使用されます。最小単位に分割された文字列の詳細については、<see cref="T:System.Xml.XmlNameTable" /> のトピックを参照してください。</param>
      <param name="nsMgr">名前空間情報を検索するために使用する <see cref="T:System.Xml.XmlNamespaceManager" />。または null。</param>
      <param name="docTypeName">ドキュメント型宣言の名前。</param>
      <param name="pubId">パブリック識別子。</param>
      <param name="sysId">システム識別子。</param>
      <param name="internalSubset">内部 DTD サブセット。DTD サブセットはエンティティ解決に使用され、ドキュメント検証には使用されません。</param>
      <param name="baseURI">XML フラグメントのベース URI (フラグメントの読み込み元の場所)。</param>
      <param name="xmlLang">xml:lang スコープ。</param>
      <param name="xmlSpace">xml:space スコープを示す <see cref="T:System.Xml.XmlSpace" /> 値。</param>
      <exception cref="T:System.Xml.XmlException">
        <paramref name="nt" /> が、<paramref name="nsMgr" /> を構築するために使用される XmlNameTable と異なります。</exception>
    </member>
    <member name="M:System.Xml.XmlParserContext.#ctor(System.Xml.XmlNameTable,System.Xml.XmlNamespaceManager,System.String,System.String,System.String,System.String,System.String,System.String,System.Xml.XmlSpace,System.Text.Encoding)">
      <summary>
        <see cref="T:System.Xml.XmlNameTable" />、<see cref="T:System.Xml.XmlNamespaceManager" />、ベース URI、xml:lang、xml:space、エンコーディング、およびドキュメント型のそれぞれの値を指定して、XmlParserContext クラスの新しいインスタンスを初期化します。</summary>
      <param name="nt">文字列を最小単位に分割するために使用する <see cref="T:System.Xml.XmlNameTable" />。このパラメーターが null の場合は、<paramref name="nsMgr" /> を構築するために使用される名前テーブルが代わりに使用されます。最小単位に分割された文字列の詳細については、<see cref="T:System.Xml.XmlNameTable" /> のトピックを参照してください。</param>
      <param name="nsMgr">名前空間情報を検索するために使用する <see cref="T:System.Xml.XmlNamespaceManager" />。または null。</param>
      <param name="docTypeName">ドキュメント型宣言の名前。</param>
      <param name="pubId">パブリック識別子。</param>
      <param name="sysId">システム識別子。</param>
      <param name="internalSubset">内部 DTD サブセット。DTD はエンティティ解決に使用され、ドキュメント検証には使用されません。</param>
      <param name="baseURI">XML フラグメントのベース URI (フラグメントの読み込み元の場所)。</param>
      <param name="xmlLang">xml:lang スコープ。</param>
      <param name="xmlSpace">xml:space スコープを示す <see cref="T:System.Xml.XmlSpace" /> 値。</param>
      <param name="enc">エンコーディングの設定を示す <see cref="T:System.Text.Encoding" /> オブジェクト。</param>
      <exception cref="T:System.Xml.XmlException">
        <paramref name="nt" /> が、<paramref name="nsMgr" /> を構築するために使用される XmlNameTable と異なります。</exception>
    </member>
    <member name="M:System.Xml.XmlParserContext.#ctor(System.Xml.XmlNameTable,System.Xml.XmlNamespaceManager,System.String,System.Xml.XmlSpace)">
      <summary>
        <see cref="T:System.Xml.XmlNameTable" />、<see cref="T:System.Xml.XmlNamespaceManager" />、xml:lang、および xml:space のそれぞれの値を指定して、XmlParserContext クラスの新しいインスタンスを初期化します。</summary>
      <param name="nt">文字列を最小単位に分割するために使用する <see cref="T:System.Xml.XmlNameTable" />。このパラメーターが null の場合は、<paramref name="nsMgr" /> を構築するために使用される名前テーブルが代わりに使用されます。最小単位に分割された文字列の詳細については、<see cref="T:System.Xml.XmlNameTable" /> のトピックを参照してください。</param>
      <param name="nsMgr">名前空間情報を検索するために使用する <see cref="T:System.Xml.XmlNamespaceManager" />。または null。</param>
      <param name="xmlLang">xml:lang スコープ。</param>
      <param name="xmlSpace">xml:space スコープを示す <see cref="T:System.Xml.XmlSpace" /> 値。</param>
      <exception cref="T:System.Xml.XmlException">
        <paramref name="nt" /> が、<paramref name="nsMgr" /> を構築するために使用される XmlNameTable と異なります。</exception>
    </member>
    <member name="M:System.Xml.XmlParserContext.#ctor(System.Xml.XmlNameTable,System.Xml.XmlNamespaceManager,System.String,System.Xml.XmlSpace,System.Text.Encoding)">
      <summary>
        <see cref="T:System.Xml.XmlNameTable" />、<see cref="T:System.Xml.XmlNamespaceManager" />、xml:lang、xml:space、およびエンコーディングを指定して、XmlParserContext クラスの新しいインスタンスを初期化します。</summary>
      <param name="nt">文字列を最小単位に分割するために使用する <see cref="T:System.Xml.XmlNameTable" />。このパラメーターが null の場合は、<paramref name="nsMgr" /> を構築するために使用される名前テーブルが代わりに使用されます。最小単位に分割された文字列の詳細については、<see cref="T:System.Xml.XmlNameTable" /> のトピックを参照してください。</param>
      <param name="nsMgr">名前空間情報を検索するために使用する <see cref="T:System.Xml.XmlNamespaceManager" />。または null。</param>
      <param name="xmlLang">xml:lang スコープ。</param>
      <param name="xmlSpace">xml:space スコープを示す <see cref="T:System.Xml.XmlSpace" /> 値。</param>
      <param name="enc">エンコーディングの設定を示す <see cref="T:System.Text.Encoding" /> オブジェクト。</param>
      <exception cref="T:System.Xml.XmlException">
        <paramref name="nt" /> が、<paramref name="nsMgr" /> を構築するために使用される XmlNameTable と異なります。</exception>
    </member>
    <member name="P:System.Xml.XmlParserContext.BaseURI">
      <summary>ベース URI を取得または設定します。</summary>
      <returns>DTD ファイルを解決するために使用するベース URI。</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.DocTypeName">
      <summary>ドキュメント型宣言の名前を取得または設定します。</summary>
      <returns>ドキュメント型宣言の名前。</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.Encoding">
      <summary>エンコーディングの種類を取得または設定します。</summary>
      <returns>エンコーディングの種類を示す <see cref="T:System.Text.Encoding" /> オブジェクト。</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.InternalSubset">
      <summary>内部 DTD サブセットを取得または設定します。</summary>
      <returns>内部 DTD サブセット。たとえば、このプロパティは、&lt;!DOCTYPE doc [...]&gt; の角かっこの中のすべての内容を返します。</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.NamespaceManager">
      <summary>
        <see cref="T:System.Xml.XmlNamespaceManager" /> を取得または設定します。</summary>
      <returns>XmlNamespaceManager。</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.NameTable">
      <summary>文字列を最小単位に分割するために使用される <see cref="T:System.Xml.XmlNameTable" /> を取得します。最小単位に分割された文字列の詳細については、<see cref="T:System.Xml.XmlNameTable" /> のトピックを参照してください。</summary>
      <returns>XmlNameTable。</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.PublicId">
      <summary>パブリック識別子を取得または設定します。</summary>
      <returns>パブリック識別子。</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.SystemId">
      <summary>システム識別子を取得または設定します。</summary>
      <returns>システム識別子。</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.XmlLang">
      <summary>現在の xml:lang スコープを取得または設定します。</summary>
      <returns>現在の xml:lang スコープ。スコープ内に xml:lang がない場合は、String.Empty が返されます。</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.XmlSpace">
      <summary>現在の xml:space スコープを取得または設定します。</summary>
      <returns>xml:space スコープを示す <see cref="T:System.Xml.XmlSpace" /> 値。</returns>
    </member>
    <member name="T:System.Xml.XmlQualifiedName">
      <summary>XML 限定名を表します。</summary>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.#ctor">
      <summary>
        <see cref="T:System.Xml.XmlQualifiedName" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.#ctor(System.String)">
      <summary>指定した名前を使用して、<see cref="T:System.Xml.XmlQualifiedName" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="name">
        <see cref="T:System.Xml.XmlQualifiedName" /> オブジェクトの名前として使用するローカル名。</param>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.#ctor(System.String,System.String)">
      <summary>指定した名前と名前空間を使用して、<see cref="T:System.Xml.XmlQualifiedName" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="name">
        <see cref="T:System.Xml.XmlQualifiedName" /> オブジェクトの名前として使用するローカル名。</param>
      <param name="ns">
        <see cref="T:System.Xml.XmlQualifiedName" /> オブジェクトの名前空間。</param>
    </member>
    <member name="F:System.Xml.XmlQualifiedName.Empty">
      <summary>空の <see cref="T:System.Xml.XmlQualifiedName" /> を提供します。</summary>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.Equals(System.Object)">
      <summary>指定した <see cref="T:System.Xml.XmlQualifiedName" /> オブジェクトが、現在の <see cref="T:System.Xml.XmlQualifiedName" /> オブジェクトと等しいかどうかを判断します。</summary>
      <returns>2 つのオブジェクトが同じインスタンス オブジェクトである場合は true。それ以外の場合は false。</returns>
      <param name="other">比較対象の <see cref="T:System.Xml.XmlQualifiedName" />。</param>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.GetHashCode">
      <summary>
        <see cref="T:System.Xml.XmlQualifiedName" /> のハッシュ コードを返します。</summary>
      <returns>このオブジェクトのハッシュ コード。</returns>
    </member>
    <member name="P:System.Xml.XmlQualifiedName.IsEmpty">
      <summary>
        <see cref="T:System.Xml.XmlQualifiedName" /> が空かどうかを示す値を取得します。</summary>
      <returns>名前と名前空間が空の文字列である場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="P:System.Xml.XmlQualifiedName.Name">
      <summary>
        <see cref="T:System.Xml.XmlQualifiedName" /> の限定名の文字列形式を取得します。</summary>
      <returns>限定名の文字列形式。オブジェクトに対して名前が定義されていない場合は String.Empty。</returns>
    </member>
    <member name="P:System.Xml.XmlQualifiedName.Namespace">
      <summary>
        <see cref="T:System.Xml.XmlQualifiedName" /> の名前空間の文字列形式を取得します。</summary>
      <returns>名前空間の文字列形式。オブジェクトに対して名前空間が定義されていない場合は String.Empty。</returns>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.op_Equality(System.Xml.XmlQualifiedName,System.Xml.XmlQualifiedName)">
      <summary>2 つの <see cref="T:System.Xml.XmlQualifiedName" /> オブジェクトを比較します。</summary>
      <returns>2 つのオブジェクトの名前の値および名前空間の値が同じである場合は true。それ以外の場合は false。</returns>
      <param name="a">比較対象の <see cref="T:System.Xml.XmlQualifiedName" />。</param>
      <param name="b">比較対象の <see cref="T:System.Xml.XmlQualifiedName" />。</param>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.op_Inequality(System.Xml.XmlQualifiedName,System.Xml.XmlQualifiedName)">
      <summary>2 つの <see cref="T:System.Xml.XmlQualifiedName" /> オブジェクトを比較します。</summary>
      <returns>2 つのオブジェクトの名前の値および名前空間の値が異なっている場合は true。それ以外の場合は false。</returns>
      <param name="a">比較対象の <see cref="T:System.Xml.XmlQualifiedName" />。</param>
      <param name="b">比較対象の <see cref="T:System.Xml.XmlQualifiedName" />。</param>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.ToString">
      <summary>
        <see cref="T:System.Xml.XmlQualifiedName" /> の文字列値を返します。</summary>
      <returns>namespace:localname の形式の <see cref="T:System.Xml.XmlQualifiedName" /> の文字列値。オブジェクトに名前空間が定義されていない場合、このメソッドはローカル名だけを返します。</returns>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.ToString(System.String,System.String)">
      <summary>
        <see cref="T:System.Xml.XmlQualifiedName" /> の文字列値を返します。</summary>
      <returns>namespace:localname の形式の <see cref="T:System.Xml.XmlQualifiedName" /> の文字列値。オブジェクトに名前空間が定義されていない場合、このメソッドはローカル名だけを返します。</returns>
      <param name="name">オブジェクトの名前です。</param>
      <param name="ns">オブジェクトの名前空間。</param>
    </member>
    <member name="T:System.Xml.XmlReader">
      <summary>XML データへの高速で非キャッシュの前方向アクセスを提供するリーダーを表します。この種類の .NET Framework ソース コードを参照して、次を参照してください。、参照ソースです。</summary>
    </member>
    <member name="M:System.Xml.XmlReader.#ctor">
      <summary>XmlReader クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="P:System.Xml.XmlReader.AttributeCount">
      <summary>派生クラスでオーバーライドされると、現在のノードの属性数を取得します。</summary>
      <returns>現在のノードにある属性の数。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.BaseURI">
      <summary>派生クラスでオーバーライドされると、現在のノードのベース URI を取得します。</summary>
      <returns>現在のノードのベース URI。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.CanReadBinaryContent">
      <summary>
        <see cref="T:System.Xml.XmlReader" /> がバイナリ コンテンツ用の読み取りメソッドを実装するかどうかを示す値を取得します。</summary>
      <returns>バイナリ コンテンツ用の読み取りメソッドを実装する場合は true。それ以外の場合は false。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.CanReadValueChunk">
      <summary>
        <see cref="T:System.Xml.XmlReader" /> が <see cref="M:System.Xml.XmlReader.ReadValueChunk(System.Char[],System.Int32,System.Int32)" /> メソッドを実装しているかどうかを示す値を取得します。</summary>
      <returns>true if the <see cref="T:System.Xml.XmlReader" /> implements the <see cref="M:System.Xml.XmlReader.ReadValueChunk(System.Char[],System.Int32,System.Int32)" /> method; otherwise false.</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.CanResolveEntity">
      <summary>このリーダーがエンティティを解析および解決できるかどうかを示す値を取得します。</summary>
      <returns>リーダーがエンティティを解析および解決できる場合は true。それ以外の場合は false。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.Stream)">
      <summary>新たに作成<see cref="T:System.Xml.XmlReader" />インスタンスの既定の設定で指定されたストリームを使用します。</summary>
      <returns>ストリーム内の XML データの読み取りに使用するオブジェクト。</returns>
      <param name="input">XML データを格納しているストリーム。<see cref="T:System.Xml.XmlReader" /> は、バイト順マークや、エンコードに関するその他の記号を探すため、ストリームの先頭バイトをスキャンします。エンコーディングが確認された場合、そのエンコーディングを使用してストリームの読み込みを続行し、入力を (Unicode) 文字のストリームとして解析する処理を継続します。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 値が null です。</exception>
      <exception cref="T:System.Security.SecurityException">
        <see cref="T:System.Xml.XmlReader" /> には、XML データの場所へのアクセスに必要なアクセス許可がありません。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.Stream,System.Xml.XmlReaderSettings)">
      <summary>新たに作成<see cref="T:System.Xml.XmlReader" />インスタンスは、指定したストリームおよび設定を使用します。</summary>
      <returns>ストリーム内の XML データの読み取りに使用するオブジェクト。</returns>
      <param name="input">XML データを格納しているストリーム。<see cref="T:System.Xml.XmlReader" /> は、バイト順マークや、エンコードに関するその他の記号を探すため、ストリームの先頭バイトをスキャンします。エンコーディングが確認された場合、そのエンコーディングを使用してストリームの読み込みを続行し、入力を (Unicode) 文字のストリームとして解析する処理を継続します。</param>
      <param name="settings">新しい設定<see cref="T:System.Xml.XmlReader" />インスタンス。この値は、null の場合もあります。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 値が null です。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.Stream,System.Xml.XmlReaderSettings,System.Xml.XmlParserContext)">
      <summary>新たに作成<see cref="T:System.Xml.XmlReader" />インスタンスを解析するための指定したストリーム、設定、およびコンテキスト情報を使用しています。</summary>
      <returns>ストリーム内の XML データの読み取りに使用するオブジェクト。</returns>
      <param name="input">XML データを格納しているストリーム。<see cref="T:System.Xml.XmlReader" /> は、バイト順マークや、エンコードに関するその他の記号を探すため、ストリームの先頭バイトをスキャンします。エンコーディングが確認された場合、そのエンコーディングを使用してストリームの読み込みを続行し、入力を (Unicode) 文字のストリームとして解析する処理を継続します。</param>
      <param name="settings">新しい設定<see cref="T:System.Xml.XmlReader" />インスタンス。この値は、null の場合もあります。</param>
      <param name="inputContext">XML フラグメントの解析に必要なコンテキスト情報。コンテキスト情報には、エンコーディング、名前空間スコープ、現在の xml:lang スコープと xml:space スコープ、ベース URI、および文書型定義に使用する <see cref="T:System.Xml.XmlNameTable" /> を格納できます。この値は、null の場合もあります。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 値が null です。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.TextReader)">
      <summary>新たに作成<see cref="T:System.Xml.XmlReader" />、指定されたテキスト リーダーを使用してインスタンス。</summary>
      <returns>ストリーム内の XML データの読み取りに使用するオブジェクト。</returns>
      <param name="input">XML データの読み出し元のテキスト リーダー。テキスト リーダーは Unicode 文字のストリームを返すため、XML リーダーはデータ ストリームのデコードに XML 宣言に指定されたエンコーディングを使用しません。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 値が null です。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.TextReader,System.Xml.XmlReaderSettings)">
      <summary>新たに作成<see cref="T:System.Xml.XmlReader" />、指定されたテキスト リーダーと設定を使用してインスタンス。</summary>
      <returns>ストリーム内の XML データの読み取りに使用するオブジェクト。</returns>
      <param name="input">XML データの読み出し元のテキスト リーダー。テキスト リーダーは Unicode 文字のストリームを返すため、XML リーダーはデータ ストリームのデコードに XML 宣言に指定されたエンコーディングを使用しません。</param>
      <param name="settings">新しい設定<see cref="T:System.Xml.XmlReader" />です。この値は、null の場合もあります。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 値が null です。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.TextReader,System.Xml.XmlReaderSettings,System.Xml.XmlParserContext)">
      <summary>新たに作成<see cref="T:System.Xml.XmlReader" />を解析するための指定されたテキスト リーダー、設定、およびコンテキスト情報を使用してインスタンス。</summary>
      <returns>ストリーム内の XML データの読み取りに使用するオブジェクト。</returns>
      <param name="input">XML データの読み出し元のテキスト リーダー。テキスト リーダーは Unicode 文字のストリームを返すため、XML リーダーはデータ ストリームのデコードに XML 宣言に指定されたエンコーディングを使用しません。</param>
      <param name="settings">新しい設定<see cref="T:System.Xml.XmlReader" />インスタンス。この値は、null の場合もあります。</param>
      <param name="inputContext">XML フラグメントの解析に必要なコンテキスト情報。コンテキスト情報には、エンコーディング、名前空間スコープ、現在の xml:lang スコープと xml:space スコープ、ベース URI、および文書型定義に使用する <see cref="T:System.Xml.XmlNameTable" /> を格納できます。この値は、null の場合もあります。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 値が null です。</exception>
      <exception cref="T:System.ArgumentException">
        <see cref="P:System.Xml.XmlReaderSettings.NameTable" /> プロパティと <see cref="P:System.Xml.XmlParserContext.NameTable" /> プロパティの両方に値が設定されています(これらの NameTable プロパティのいずれか 1 つだけを設定して使用できます)。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.String)">
      <summary>指定された URI で新しい <see cref="T:System.Xml.XmlReader" /> インスタンスを作成します。</summary>
      <returns>ストリーム内の XML データの読み取りに使用するオブジェクト。</returns>
      <param name="inputUri">XML データを格納しているファイルの URI。<see cref="T:System.Xml.XmlUrlResolver" /> クラスは、パスを正規データ形式に変換するときに使用されます。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inputUri" /> 値が null です。</exception>
      <exception cref="T:System.Security.SecurityException">
        <see cref="T:System.Xml.XmlReader" /> には、XML データの場所へのアクセスに必要なアクセス許可がありません。</exception>
      <exception cref="T:System.IO.FileNotFoundException">URI で指定されたファイルが存在しません。</exception>
      <exception cref="T:System.UriFormatException">Windows ストア アプリのための .NET または汎用性のあるクラス ライブラリで、基本クラスの例外 <see cref="T:System.FormatException" /> を代わりにキャッチします。URI 形式が正しくありません。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.String,System.Xml.XmlReaderSettings)">
      <summary>新たに作成<see cref="T:System.Xml.XmlReader" />指定された URI および設定を使用してインスタンス。</summary>
      <returns>ストリーム内の XML データの読み取りに使用するオブジェクト。</returns>
      <param name="inputUri">XML データを格納しているファイルの URI。<see cref="T:System.Xml.XmlReaderSettings" /> オブジェクトの <see cref="T:System.Xml.XmlResolver" /> オブジェクトは、パスを正規データ形式に変換するときに使用されます。<see cref="P:System.Xml.XmlReaderSettings.XmlResolver" /> が null の場合は、新しい <see cref="T:System.Xml.XmlUrlResolver" /> オブジェクトが使用されます。</param>
      <param name="settings">新しい設定<see cref="T:System.Xml.XmlReader" />インスタンス。この値は、null の場合もあります。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inputUri" /> 値が null です。</exception>
      <exception cref="T:System.IO.FileNotFoundException">URI で指定されたファイルが見つかりません。</exception>
      <exception cref="T:System.UriFormatException">Windows ストア アプリのための .NET または汎用性のあるクラス ライブラリで、基本クラスの例外 <see cref="T:System.FormatException" /> を代わりにキャッチします。URI 形式が正しくありません。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.Xml.XmlReader,System.Xml.XmlReaderSettings)">
      <summary>新たに作成<see cref="T:System.Xml.XmlReader" />、指定した XML リーダーと設定を使用してインスタンス。</summary>
      <returns>ラップされているオブジェクトには、指定された<see cref="T:System.Xml.XmlReader" />オブジェクトです。</returns>
      <param name="reader">基になる XML リーダーとして使用するオブジェクト。</param>
      <param name="settings">新しい設定<see cref="T:System.Xml.XmlReader" />インスタンス。<see cref="T:System.Xml.XmlReaderSettings" /> オブジェクトの準拠レベルは、基になるリーダーの準拠レベルと一致するか、または <see cref="F:System.Xml.ConformanceLevel.Auto" /> に設定する必要があります。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="reader" /> 値が null です。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReaderSettings" /> オブジェクトが、基になるリーダーの準拠レベルと一致しない準拠レベルを指定しています。または基になる <see cref="T:System.Xml.XmlReader" /> が <see cref="F:System.Xml.ReadState.Error" /> の状態または <see cref="F:System.Xml.ReadState.Closed" /> の状態にあります。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Depth">
      <summary>派生クラスでオーバーライドされると、XML ドキュメント内の現在のノードの深さを取得します。</summary>
      <returns>XML ドキュメント内の現在のノードの深さ。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Dispose">
      <summary>
        <see cref="T:System.Xml.XmlReader" /> クラスの現在のインスタンスによって使用されているすべてのリソースを解放します。</summary>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Dispose(System.Boolean)">
      <summary>
        <see cref="T:System.Xml.XmlReader" /> によって使用されているアンマネージ リソースを解放し、オプションでマネージ リソースも解放します。</summary>
      <param name="disposing">マネージ リソースとアンマネージ リソースの両方を解放する場合は true。アンマネージ リソースだけを解放する場合は false。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.EOF">
      <summary>派生クラスでオーバーライドされると、リーダーがストリームの末尾に配置されているかどうかを示す値を取得します。</summary>
      <returns>ストリームの末尾にリーダーが配置されている場合は true。それ以外の場合は false。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.GetAttribute(System.Int32)">
      <summary>派生クラスでオーバーライドされると、指定したインデックスの属性の値を取得します。</summary>
      <returns>指定した属性の値。このメソッドは、リーダーを移動しません。</returns>
      <param name="i">属性のインデックス。インデックスの値は、0 から始まります。最初の属性のインデックスは 0 です。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">該当する <paramref name="i" /> がありません。負の値以外で、属性コレクションのサイズよりも小さくなければなりません。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.GetAttribute(System.String)">
      <summary>派生クラスでオーバーライドされると、指定した <see cref="P:System.Xml.XmlReader.Name" /> の属性の値を取得します。</summary>
      <returns>指定した属性の値。属性が見つからないか、値が String.Empty の場合、null が返されます。</returns>
      <param name="name">属性の限定名。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> は null です。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.GetAttribute(System.String,System.String)">
      <summary>派生クラスでオーバーライドされると、指定した <see cref="P:System.Xml.XmlReader.LocalName" /> および <see cref="P:System.Xml.XmlReader.NamespaceURI" /> の属性の値を取得します。</summary>
      <returns>指定した属性の値。属性が見つからないか、値が String.Empty の場合、null が返されます。このメソッドは、リーダーを移動しません。</returns>
      <param name="name">属性のローカル名。</param>
      <param name="namespaceURI">属性の名前空間 URI。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> は null です。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.GetValueAsync">
      <summary>現在のノードのテキスト値を非同期に取得します。</summary>
      <returns>現在のノードの値。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 非同期メソッドは、<see cref="P:System.Xml.XmlReaderSettings.Async" /> フラグを true に設定せずに呼び出されました。この場合、非同期メソッドを使用するには XmlReaderSettings.Async を true に設定する必要があることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.HasAttributes">
      <summary>現在のノードに属性があるかどうかを示す値を取得します。</summary>
      <returns>現在のノードが属性を持っている場合は true。それ以外の場合は false。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.HasValue">
      <summary>派生クラスでオーバーライドされると、現在のノードが <see cref="P:System.Xml.XmlReader.Value" /> を持つことができるかどうかを示す値を取得します。</summary>
      <returns>リーダーが現在配置されているノードが Value を持つことができる場合は true。それ以外の場合は false。false の場合、ノードは String.Empty の値を持ちます。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.IsDefault">
      <summary>派生クラスでオーバーライドされると、現在のノードが DTD またはスキーマで定義された既定値から生成された属性かどうかを示す値を取得します。</summary>
      <returns>現在のノードが、DTD またはスキーマで定義された既定値から生成された値を持つ属性である場合は true。属性値が明示的に設定された場合は false。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.IsEmptyElement">
      <summary>派生クラスでオーバーライドされると、現在のノードが空の要素 (&lt;MyElement/&gt; など) かどうかを示す値を取得します。</summary>
      <returns>現在のノードが /&gt; で終わる要素である (<see cref="P:System.Xml.XmlReader.NodeType" /> が XmlNodeType.Element に等しい) 場合は true。それ以外の場合は false。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.IsName(System.String)">
      <summary>文字列引数が有効な XML 名かどうかを示す値を返します。</summary>
      <returns>名前が有効な場合は true。それ以外の場合は false。</returns>
      <param name="str">検証対象の名前。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="str" /> 値が null です。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.IsNameToken(System.String)">
      <summary>文字列引数が有効な XML 名トークンかどうかを示す値を返します。</summary>
      <returns>有効な名前トークンの場合は true。それ以外の場合は false。</returns>
      <param name="str">検証対象の名前トークン。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="str" /> 値が null です。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.IsStartElement">
      <summary>
        <see cref="M:System.Xml.XmlReader.MoveToContent" /> を呼び出し、現在のコンテンツ ノードが開始タグまたは空の要素タグかどうかをテストします。</summary>
      <returns>
        <see cref="M:System.Xml.XmlReader.MoveToContent" /> が開始タグまたは空の要素タグを見つけた場合は true。XmlNodeType.Element 以外のノード型が見つかった場合は false。</returns>
      <exception cref="T:System.Xml.XmlException">入力ストリームで、正しくない XML が検出されました。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.IsStartElement(System.String)">
      <summary>
        <see cref="M:System.Xml.XmlReader.MoveToContent" /> を呼び出し、現在のコンテンツ ノードが開始タグまたは空の要素タグかどうか、また、見つかった要素の <see cref="P:System.Xml.XmlReader.Name" /> プロパティが、指定した引数と一致するかどうかをテストします。</summary>
      <returns>見つかったノードが要素であり、Name プロパティが指定した文字列と一致する場合は true。XmlNodeType.Element 以外のノード型が見つかった場合、または要素の Name プロパティが指定した文字列と一致しない場合は false。</returns>
      <param name="name">見つかった要素の Name プロパティと一致する文字列。</param>
      <exception cref="T:System.Xml.XmlException">入力ストリームで、正しくない XML が検出されました。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.IsStartElement(System.String,System.String)">
      <summary>
        <see cref="M:System.Xml.XmlReader.MoveToContent" /> を呼び出し、現在のコンテンツ ノードが開始タグまたは空の要素タグかどうか、また、見つかった要素の <see cref="P:System.Xml.XmlReader.LocalName" /> プロパティと <see cref="P:System.Xml.XmlReader.NamespaceURI" /> プロパティが、指定した文字列と一致するかどうかをテストします。</summary>
      <returns>見つかったノードが要素の場合は true。XmlNodeType.Element 以外のノード型が見つかった場合、または要素の LocalName および NamespaceURI プロパティが指定した文字列と一致しない場合は false。</returns>
      <param name="localname">見つかった要素の LocalName プロパティと一致する文字列。</param>
      <param name="ns">見つかった要素の NamespaceURI プロパティと一致する文字列。</param>
      <exception cref="T:System.Xml.XmlException">入力ストリームで、正しくない XML が検出されました。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Item(System.Int32)">
      <summary>派生クラスでオーバーライドされると、指定したインデックスの属性の値を取得します。</summary>
      <returns>指定した属性の値。</returns>
      <param name="i">属性のインデックス。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Item(System.String)">
      <summary>派生クラスでオーバーライドされると、指定した <see cref="P:System.Xml.XmlReader.Name" /> の属性の値を取得します。</summary>
      <returns>指定した属性の値。指定した属性が見つからない場合は null が返されます。</returns>
      <param name="name">属性の限定名。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Item(System.String,System.String)">
      <summary>派生クラスでオーバーライドされると、指定した <see cref="P:System.Xml.XmlReader.LocalName" /> および <see cref="P:System.Xml.XmlReader.NamespaceURI" /> の属性の値を取得します。</summary>
      <returns>指定した属性の値。指定した属性が見つからない場合は null が返されます。</returns>
      <param name="name">属性のローカル名。</param>
      <param name="namespaceURI">属性の名前空間 URI。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.LocalName">
      <summary>派生クラスでオーバーライドされると、現在のノードのローカル名を取得します。</summary>
      <returns>プリフィックスを削除した現在のノードの名前。たとえば、LocalName は、要素 &lt;bk:book&gt; の book です。名前を持たないノード型 (Text、Comment など) の場合、このプロパティは String.Empty を返します。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.LookupNamespace(System.String)">
      <summary>派生クラスでオーバーライドされると、現在の要素のスコープの名前空間プリフィックスを解決します。</summary>
      <returns>プリフィックスの割り当て先の名前空間 URI。条件に合うプリフィックスが見つからない場合は null。</returns>
      <param name="prefix">解決する対象となる名前空間 URI を持つプリフィックス。既定の名前空間と一致させるには、空の文字列を渡します。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToAttribute(System.Int32)">
      <summary>派生クラスでオーバーライドされると、指定したインデックスの属性に移動します。</summary>
      <param name="i">属性のインデックス。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">パラメーターの値が負数です。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToAttribute(System.String)">
      <summary>派生クラスでオーバーライドされると、指定した <see cref="P:System.Xml.XmlReader.Name" /> の属性に移動します。</summary>
      <returns>属性が見つかった場合は true。それ以外の場合は false。false の場合、リーダーの位置は変更されません。</returns>
      <param name="name">属性の限定名。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
      <exception cref="T:System.ArgumentException">パラメーターは空の文字列です。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToAttribute(System.String,System.String)">
      <summary>派生クラスでオーバーライドされると、指定した <see cref="P:System.Xml.XmlReader.LocalName" /> および <see cref="P:System.Xml.XmlReader.NamespaceURI" /> の属性に移動します。</summary>
      <returns>属性が見つかった場合は true。それ以外の場合は false。false の場合、リーダーの位置は変更されません。</returns>
      <param name="name">属性のローカル名。</param>
      <param name="ns">属性の名前空間 URI。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
      <exception cref="T:System.ArgumentNullException">両方のパラメーター値が null です。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToContent">
      <summary>現在のノードがコンテンツ (空白でないテキスト、CDATA、Element、EndElement、EntityReference、または EndEntity) ノードかどうかを確認します。ノードがコンテンツ ノードでない場合、リーダーは、次のコンテンツ ノードまたはファイルの末尾までスキップします。リーダーは、ProcessingInstruction、DocumentType、Comment、Whitespace、または SignificantWhitespace の型のノードをスキップします。</summary>
      <returns>メソッドが見つけた現在のノードの <see cref="P:System.Xml.XmlReader.NodeType" />。リーダーが入力ストリームの末尾に到達した場合は XmlNodeType.None。</returns>
      <exception cref="T:System.Xml.XmlException">入力ストリームで検出された正しくない XML。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToContentAsync">
      <summary>現在のノードがコンテンツ ノードであるかどうかを非同期的に確認します。ノードがコンテンツ ノードでない場合、リーダーは、次のコンテンツ ノードまたはファイルの末尾までスキップします。</summary>
      <returns>メソッドが見つけた現在のノードの <see cref="P:System.Xml.XmlReader.NodeType" />。リーダーが入力ストリームの末尾に到達した場合は XmlNodeType.None。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 非同期メソッドは、<see cref="P:System.Xml.XmlReaderSettings.Async" /> フラグを true に設定せずに呼び出されました。この場合、非同期メソッドを使用するには XmlReaderSettings.Async を true に設定する必要があることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToElement">
      <summary>派生クラスでオーバーライドされると、現在の属性ノードを含む要素に移動します。</summary>
      <returns>リーダーが属性の位置に配置されている場合は true で、属性を所有している要素の位置にリーダーが移動します。リーダーが属性の位置に配置されていない場合は false で、リーダーの位置が変更されません。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToFirstAttribute">
      <summary>派生クラスでオーバーライドされると、最初の属性に移動します。</summary>
      <returns>属性が存在する場合は true で、リーダーが最初の属性へ移動します。それ以外の場合は false で、リーダーの位置が変更されません。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToNextAttribute">
      <summary>派生クラスでオーバーライドされると、次の属性に移動します。</summary>
      <returns>次の属性が存在する場合は true。それ以上、属性が存在しない場合は false。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Name">
      <summary>派生クラスでオーバーライドされると、現在のノードの限定名を取得します。</summary>
      <returns>現在のノードの限定名。たとえば、Name は、要素 &lt;bk:book&gt; の bk:book です。返される名前は、ノードの <see cref="P:System.Xml.XmlReader.NodeType" /> によって異なります。リストされた値を返すノード型を次に示します。その他のすべてのノード型は、空の文字列を返します。ノード型名前 Attribute属性の名前。 DocumentTypeドキュメントの種類の名前。 Elementタグ名。 EntityReference参照されたエンティティの名前。 ProcessingInstruction処理命令の対象。 XmlDeclarationリテラル文字列 xml。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.NamespaceURI">
      <summary>派生クラスでオーバーライドされると、リーダーが配置されているノードの名前空間 URI (W3C の名前空間の仕様における定義に準拠) を取得します。</summary>
      <returns>現在のノードの名前空間 URI。それ以外の場合は空の文字列。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.NameTable">
      <summary>派生クラスでオーバーライドされると、この実装に関連付けられている <see cref="T:System.Xml.XmlNameTable" /> を取得します。</summary>
      <returns>ノード内の最小単位に分割された文字列を取得できる XmlNameTable。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.NodeType">
      <summary>派生クラスでオーバーライドされると、現在のノードの型を取得します。</summary>
      <returns>現在のノードの型を指定する列挙値の 1 つ。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Prefix">
      <summary>派生クラスでオーバーライドされると、現在のノードに関連付けられている名前空間プリフィックスを取得します。</summary>
      <returns>現在のノードに関連付けられた名前空間プリフィックス。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Read">
      <summary>派生クラスでオーバーライドされると、ストリームから次のノードを読み取ります。</summary>
      <returns>true次のノードが正常に読み取られた場合それ以外の場合、falseです。</returns>
      <exception cref="T:System.Xml.XmlException">XML の解析中にエラーが発生しました。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadAsync">
      <summary>ストリームから次のノードを非同期に読み取ります。</summary>
      <returns>次のノードが正常に読み取られた場合は true。それ以上読み取る対象となるノードが存在しない場合は false。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 非同期メソッドは、<see cref="P:System.Xml.XmlReaderSettings.Async" /> フラグを true に設定せずに呼び出されました。この場合、非同期メソッドを使用するには XmlReaderSettings.Async を true に設定する必要があることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadAttributeValue">
      <summary>派生クラスでオーバーライドされると、属性値を解析して、1 つ以上の Text、EntityReference、または EndEntity の各ノードに格納します。</summary>
      <returns>返すノードがある場合は true。初めて呼び出すときにリーダーの位置が属性ノード上にない場合、またはすべての属性値が読み込まれている場合は、false。misc="" などの空の属性は、値 true を持つ単一のノードと一緒に String.Empty を返します。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAs(System.Type,System.Xml.IXmlNamespaceResolver)">
      <summary>指定された型のオブジェクトとして内容を読み取ります。</summary>
      <returns>要求された型に変換された、連結されたテキストの内容または属性値。</returns>
      <param name="returnType">返される値の型。メモ   .NET Framework 3.5 のリリースでは、<paramref name="returnType" /> パラメーターの値に <see cref="T:System.DateTimeOffset" /> 型を指定できるようになりました。</param>
      <param name="namespaceResolver">型変換に関連する名前空間プレフィックスの解決に使用される <see cref="T:System.Xml.IXmlNamespaceResolver" /> オブジェクト。たとえば、<see cref="T:System.Xml.XmlQualifiedName" /> オブジェクトを xs:string に変換するときにこれを使用できます。この値は、null の場合もあります。</param>
      <exception cref="T:System.FormatException">内容が、指定した型の正しい形式になっていません。</exception>
      <exception cref="T:System.InvalidCastException">試行されたキャストが無効です。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="returnType" /> 値が null です。</exception>
      <exception cref="T:System.InvalidOperationException">現在のノードは、サポートされているノード型ではありません。詳細については、次の表を参照してください。</exception>
      <exception cref="T:System.OverflowException">Decimal.MaxValue を読み取りました。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsAsync(System.Type,System.Xml.IXmlNamespaceResolver)">
      <summary>指定された型のオブジェクトとして内容を非同期に読み取ります。</summary>
      <returns>要求された型に変換された、連結されたテキストの内容または属性値。</returns>
      <param name="returnType">返される値の型。</param>
      <param name="namespaceResolver">型変換に関連する名前空間プレフィックスの解決に使用される <see cref="T:System.Xml.IXmlNamespaceResolver" /> オブジェクト。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 非同期メソッドは、<see cref="P:System.Xml.XmlReaderSettings.Async" /> フラグを true に設定せずに呼び出されました。この場合、非同期メソッドを使用するには XmlReaderSettings.Async を true に設定する必要があることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsBase64(System.Byte[],System.Int32,System.Int32)">
      <summary>コンテンツを読み取り、Base64 でデコードされたバイナリ バイトを返します。</summary>
      <returns>バッファーに書き込まれたバイト数。</returns>
      <param name="buffer">結果として得られるテキストのコピー先のバッファー。この値を null にすることはできません。</param>
      <param name="index">バッファー内の結果のコピー開始位置を示すオフセット。</param>
      <param name="count">バッファーにコピーする最大バイト数。コピーされた実際のバイト数は、このメソッドから返されます。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> 値が null です。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.Xml.XmlReader.ReadContentAsBase64(System.Byte[],System.Int32,System.Int32)" /> は、現在のノードではサポートされていません。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">バッファー内のインデックス、またはインデックスとカウントの合計値が、割り当てられているバッファー サイズを超えています。</exception>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.XmlReader" /> 実装が、このメソッドをサポートしていません。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsBase64Async(System.Byte[],System.Int32,System.Int32)">
      <summary>コンテンツを非同期に読み取り、Base64 でデコードされたバイナリ バイトを返します。</summary>
      <returns>バッファーに書き込まれたバイト数。</returns>
      <param name="buffer">結果として得られるテキストのコピー先のバッファー。この値を null にすることはできません。</param>
      <param name="index">バッファー内の結果のコピー開始位置を示すオフセット。</param>
      <param name="count">バッファーにコピーする最大バイト数。コピーされた実際のバイト数は、このメソッドから返されます。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 非同期メソッドは、<see cref="P:System.Xml.XmlReaderSettings.Async" /> フラグを true に設定せずに呼び出されました。この場合、非同期メソッドを使用するには XmlReaderSettings.Async を true に設定する必要があることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsBinHex(System.Byte[],System.Int32,System.Int32)">
      <summary>コンテンツを読み取り、BinHex でデコードされたバイナリ バイトを返します。</summary>
      <returns>バッファーに書き込まれたバイト数。</returns>
      <param name="buffer">結果として得られるテキストのコピー先のバッファー。この値を null にすることはできません。</param>
      <param name="index">バッファー内の結果のコピー開始位置を示すオフセット。</param>
      <param name="count">バッファーにコピーする最大バイト数。コピーされた実際のバイト数は、このメソッドから返されます。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> 値が null です。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.Xml.XmlReader.ReadContentAsBinHex(System.Byte[],System.Int32,System.Int32)" /> は、現在のノードではサポートされていません。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">バッファー内のインデックス、またはインデックスとカウントの合計値が、割り当てられているバッファー サイズを超えています。</exception>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.XmlReader" /> 実装が、このメソッドをサポートしていません。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsBinHexAsync(System.Byte[],System.Int32,System.Int32)">
      <summary>コンテンツを非同期に読み取り、BinHex でデコードされたバイナリ バイトを返します。</summary>
      <returns>バッファーに書き込まれたバイト数。</returns>
      <param name="buffer">結果として得られるテキストのコピー先のバッファー。この値を null にすることはできません。</param>
      <param name="index">バッファー内の結果のコピー開始位置を示すオフセット。</param>
      <param name="count">バッファーにコピーする最大バイト数。コピーされた実際のバイト数は、このメソッドから返されます。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 非同期メソッドは、<see cref="P:System.Xml.XmlReaderSettings.Async" /> フラグを true に設定せずに呼び出されました。この場合、非同期メソッドを使用するには XmlReaderSettings.Async を true に設定する必要があることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsBoolean">
      <summary>現在の位置でテキストの内容を Boolean として読み取ります。</summary>
      <returns>
        <see cref="T:System.Boolean" /> オブジェクトとしてのテキストの内容。</returns>
      <exception cref="T:System.InvalidCastException">試行されたキャストが無効です。</exception>
      <exception cref="T:System.FormatException">文字列書式が無効です。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsDateTimeOffset">
      <summary>現在の位置でテキストの内容を <see cref="T:System.DateTimeOffset" /> オブジェクトとして読み取ります。</summary>
      <returns>
        <see cref="T:System.DateTimeOffset" /> オブジェクトとしてのテキストの内容。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsDecimal">
      <summary>現在の位置でテキストの内容を <see cref="T:System.Decimal" /> オブジェクトとして読み取ります。</summary>
      <returns>現在の位置における <see cref="T:System.Decimal" /> オブジェクトとしてのテキストの内容。</returns>
      <exception cref="T:System.InvalidCastException">試行されたキャストが無効です。</exception>
      <exception cref="T:System.FormatException">文字列書式が無効です。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsDouble">
      <summary>現在の位置のテキストの内容を、倍精度浮動小数点数として読み取ります。</summary>
      <returns>倍精度浮動小数点数としてのテキストの内容。</returns>
      <exception cref="T:System.InvalidCastException">試行されたキャストが無効です。</exception>
      <exception cref="T:System.FormatException">文字列書式が無効です。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsFloat">
      <summary>現在の位置のテキストの内容を、単精度浮動小数点数として読み取ります。</summary>
      <returns>現在の位置における単精度浮動小数点数としてのテキストの内容。</returns>
      <exception cref="T:System.InvalidCastException">試行されたキャストが無効です。</exception>
      <exception cref="T:System.FormatException">文字列書式が無効です。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsInt">
      <summary>現在の位置でテキストの内容を 32 ビット符号付き整数として読み取ります。</summary>
      <returns>32 ビット符号付き整数としてのテキストの内容。</returns>
      <exception cref="T:System.InvalidCastException">試行されたキャストが無効です。</exception>
      <exception cref="T:System.FormatException">文字列書式が無効です。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsLong">
      <summary>現在の位置でテキストの内容を 64 ビット符号付き整数として読み取ります。</summary>
      <returns>64 ビット符号付き整数としてのテキストの内容。</returns>
      <exception cref="T:System.InvalidCastException">試行されたキャストが無効です。</exception>
      <exception cref="T:System.FormatException">文字列書式が無効です。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsObject">
      <summary>現在の位置でテキストの内容を <see cref="T:System.Object" /> として読み取ります。</summary>
      <returns>最も適切な共通言語ランタイム (CLR) オブジェクトとしてのテキストの内容。</returns>
      <exception cref="T:System.InvalidCastException">試行されたキャストが無効です。</exception>
      <exception cref="T:System.FormatException">文字列書式が無効です。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsObjectAsync">
      <summary>現在の位置でテキストの内容を <see cref="T:System.Object" /> として非同期に読み取ります。</summary>
      <returns>最も適切な共通言語ランタイム (CLR) オブジェクトとしてのテキストの内容。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 非同期メソッドは、<see cref="P:System.Xml.XmlReaderSettings.Async" /> フラグを true に設定せずに呼び出されました。この場合、非同期メソッドを使用するには XmlReaderSettings.Async を true に設定する必要があることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsString">
      <summary>現在の位置でテキストの内容を <see cref="T:System.String" /> オブジェクトとして読み取ります。</summary>
      <returns>
        <see cref="T:System.String" /> オブジェクトとしてのテキストの内容。</returns>
      <exception cref="T:System.InvalidCastException">試行されたキャストが無効です。</exception>
      <exception cref="T:System.FormatException">文字列書式が無効です。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsStringAsync">
      <summary>現在の位置でテキストの内容を <see cref="T:System.String" /> オブジェクトとして非同期に読み取ります。</summary>
      <returns>
        <see cref="T:System.String" /> オブジェクトとしてのテキストの内容。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 非同期メソッドは、<see cref="P:System.Xml.XmlReaderSettings.Async" /> フラグを true に設定せずに呼び出されました。この場合、非同期メソッドを使用するには XmlReaderSettings.Async を true に設定する必要があることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAs(System.Type,System.Xml.IXmlNamespaceResolver)">
      <summary>要素の内容を要求された型として返します。</summary>
      <returns>要求された型のオブジェクトに変換された要素の内容。</returns>
      <param name="returnType">返される値の型。メモ   .NET Framework 3.5 のリリースでは、<paramref name="returnType" /> パラメーターの値に <see cref="T:System.DateTimeOffset" /> 型を指定できるようになりました。</param>
      <param name="namespaceResolver">型変換に関連する名前空間プレフィックスの解決に使用される <see cref="T:System.Xml.IXmlNamespaceResolver" /> オブジェクト。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> が要素に配置されません。</exception>
      <exception cref="T:System.Xml.XmlException">現在の要素には、子要素が含まれています。または要素の内容を要求された型に変換できません。</exception>
      <exception cref="T:System.ArgumentNullException">引数に null を渡してメソッドが呼び出されました。</exception>
      <exception cref="T:System.OverflowException">Decimal.MaxValue を読み取りました。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAs(System.Type,System.Xml.IXmlNamespaceResolver,System.String,System.String)">
      <summary>指定されたローカル名と名前空間 URI が現在の要素のものと一致することを確認し、要素の内容を要求された型として読み込みます。</summary>
      <returns>要求された型のオブジェクトに変換された要素の内容。</returns>
      <param name="returnType">返される値の型。メモ   .NET Framework 3.5 のリリースでは、<paramref name="returnType" /> パラメーターの値に <see cref="T:System.DateTimeOffset" /> 型を指定できるようになりました。</param>
      <param name="namespaceResolver">型変換に関連する名前空間プレフィックスの解決に使用される <see cref="T:System.Xml.IXmlNamespaceResolver" /> オブジェクト。</param>
      <param name="localName">要素のローカル名。</param>
      <param name="namespaceURI">要素の名前空間 URI。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> が要素に配置されません。</exception>
      <exception cref="T:System.Xml.XmlException">現在の要素には、子要素が含まれています。または要素の内容を要求された型に変換できません。</exception>
      <exception cref="T:System.ArgumentNullException">引数に null を渡してメソッドが呼び出されました。</exception>
      <exception cref="T:System.ArgumentException">指定されたローカル名と名前空間 URI は、現在読み取り中の要素と一致しません。</exception>
      <exception cref="T:System.OverflowException">Decimal.MaxValue を読み取りました。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsAsync(System.Type,System.Xml.IXmlNamespaceResolver)">
      <summary>要素の内容を要求された型として非同期に読み取ります。</summary>
      <returns>要求された型のオブジェクトに変換された要素の内容。</returns>
      <param name="returnType">返される値の型。</param>
      <param name="namespaceResolver">型変換に関連する名前空間プレフィックスの解決に使用される <see cref="T:System.Xml.IXmlNamespaceResolver" /> オブジェクト。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 非同期メソッドは、<see cref="P:System.Xml.XmlReaderSettings.Async" /> フラグを true に設定せずに呼び出されました。この場合、非同期メソッドを使用するには XmlReaderSettings.Async を true に設定する必要があることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBase64(System.Byte[],System.Int32,System.Int32)">
      <summary>要素を読み取り、Base64 の内容をデコードします。</summary>
      <returns>バッファーに書き込まれたバイト数。</returns>
      <param name="buffer">結果として得られるテキストのコピー先のバッファー。この値を null にすることはできません。</param>
      <param name="index">バッファー内の結果のコピー開始位置を示すオフセット。</param>
      <param name="count">バッファーにコピーする最大バイト数。コピーされた実際のバイト数は、このメソッドから返されます。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> 値が null です。</exception>
      <exception cref="T:System.InvalidOperationException">現在のノードは要素ノードではありません。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">バッファー内のインデックス、またはインデックスとカウントの合計値が、割り当てられているバッファー サイズを超えています。</exception>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.XmlReader" /> 実装が、このメソッドをサポートしていません。</exception>
      <exception cref="T:System.Xml.XmlException">要素には混合コンテンツが含まれます。</exception>
      <exception cref="T:System.FormatException">コンテンツを要求された型に変換できません。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBase64Async(System.Byte[],System.Int32,System.Int32)">
      <summary>要素を非同期に読み取り、Base64 の内容をデコードします。</summary>
      <returns>バッファーに書き込まれたバイト数。</returns>
      <param name="buffer">結果として得られるテキストのコピー先のバッファー。この値を null にすることはできません。</param>
      <param name="index">バッファー内の結果のコピー開始位置を示すオフセット。</param>
      <param name="count">バッファーにコピーする最大バイト数。コピーされた実際のバイト数は、このメソッドから返されます。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 非同期メソッドは、<see cref="P:System.Xml.XmlReaderSettings.Async" /> フラグを true に設定せずに呼び出されました。この場合、非同期メソッドを使用するには XmlReaderSettings.Async を true に設定する必要があることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBinHex(System.Byte[],System.Int32,System.Int32)">
      <summary>要素を読み取り、BinHex の内容をデコードします。</summary>
      <returns>バッファーに書き込まれたバイト数。</returns>
      <param name="buffer">結果として得られるテキストのコピー先のバッファー。この値を null にすることはできません。</param>
      <param name="index">バッファー内の結果のコピー開始位置を示すオフセット。</param>
      <param name="count">バッファーにコピーする最大バイト数。コピーされた実際のバイト数は、このメソッドから返されます。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> 値が null です。</exception>
      <exception cref="T:System.InvalidOperationException">現在のノードは要素ノードではありません。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">バッファー内のインデックス、またはインデックスとカウントの合計値が、割り当てられているバッファー サイズを超えています。</exception>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.XmlReader" /> 実装が、このメソッドをサポートしていません。</exception>
      <exception cref="T:System.Xml.XmlException">要素には混合コンテンツが含まれます。</exception>
      <exception cref="T:System.FormatException">コンテンツを要求された型に変換できません。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBinHexAsync(System.Byte[],System.Int32,System.Int32)">
      <summary>要素を非同期に読み取り、BinHex の内容をデコードします。</summary>
      <returns>バッファーに書き込まれたバイト数。</returns>
      <param name="buffer">結果として得られるテキストのコピー先のバッファー。この値を null にすることはできません。</param>
      <param name="index">バッファー内の結果のコピー開始位置を示すオフセット。</param>
      <param name="count">バッファーにコピーする最大バイト数。コピーされた実際のバイト数は、このメソッドから返されます。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 非同期メソッドは、<see cref="P:System.Xml.XmlReaderSettings.Async" /> フラグを true に設定せずに呼び出されました。この場合、非同期メソッドを使用するには XmlReaderSettings.Async を true に設定する必要があることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBoolean">
      <summary>現在の要素を読み取り、その内容を <see cref="T:System.Boolean" /> オブジェクトとして返します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> オブジェクトとしての要素の内容。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> が要素に配置されません。</exception>
      <exception cref="T:System.Xml.XmlException">現在の要素には、子要素が含まれています。または要素の内容を <see cref="T:System.Boolean" /> オブジェクトに変換できません。</exception>
      <exception cref="T:System.ArgumentNullException">引数に null を渡してメソッドが呼び出されました。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBoolean(System.String,System.String)">
      <summary>指定されたローカル名と名前空間 URI が現在の要素のものと一致することを確認し、現在の要素を読み取って、内容を <see cref="T:System.Boolean" /> オブジェクトとして返します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> オブジェクトとしての要素の内容。</returns>
      <param name="localName">要素のローカル名。</param>
      <param name="namespaceURI">要素の名前空間 URI。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> が要素に配置されません。</exception>
      <exception cref="T:System.Xml.XmlException">現在の要素には、子要素が含まれています。または要素の内容を要求された型に変換できません。</exception>
      <exception cref="T:System.ArgumentNullException">引数に null を渡してメソッドが呼び出されました。</exception>
      <exception cref="T:System.ArgumentException">指定されたローカル名と名前空間 URI は、現在読み取り中の要素と一致しません。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsDecimal">
      <summary>現在の要素を読み取り、その内容を <see cref="T:System.Decimal" /> オブジェクトとして返します。</summary>
      <returns>
        <see cref="T:System.Decimal" /> オブジェクトとしての要素の内容。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> が要素に配置されません。</exception>
      <exception cref="T:System.Xml.XmlException">現在の要素には、子要素が含まれています。または要素の内容を <see cref="T:System.Decimal" /> に変換できません。</exception>
      <exception cref="T:System.ArgumentNullException">引数に null を渡してメソッドが呼び出されました。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsDecimal(System.String,System.String)">
      <summary>指定されたローカル名と名前空間 URI が現在の要素のものと一致することを確認し、現在の要素を読み取って、内容を <see cref="T:System.Decimal" /> オブジェクトとして返します。</summary>
      <returns>
        <see cref="T:System.Decimal" /> オブジェクトとしての要素の内容。</returns>
      <param name="localName">要素のローカル名。</param>
      <param name="namespaceURI">要素の名前空間 URI。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> が要素に配置されません。</exception>
      <exception cref="T:System.Xml.XmlException">現在の要素には、子要素が含まれています。または要素の内容を <see cref="T:System.Decimal" /> に変換できません。</exception>
      <exception cref="T:System.ArgumentNullException">引数に null を渡してメソッドが呼び出されました。</exception>
      <exception cref="T:System.ArgumentException">指定されたローカル名と名前空間 URI は、現在読み取り中の要素と一致しません。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsDouble">
      <summary>現在の要素を読み込み、その内容を倍精度浮動小数点数として返します。</summary>
      <returns>倍精度浮動小数点数としての要素の内容。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> が要素に配置されません。</exception>
      <exception cref="T:System.Xml.XmlException">現在の要素には、子要素が含まれています。または要素の内容を倍精度浮動小数点数に変換できません。</exception>
      <exception cref="T:System.ArgumentNullException">引数に null を渡してメソッドが呼び出されました。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsDouble(System.String,System.String)">
      <summary>指定されたローカル名と名前空間 URI が現在の要素のものと一致することを確認し、現在の要素を読み込んで内容を倍精度浮動小数点数として返します。</summary>
      <returns>倍精度浮動小数点数としての要素の内容。</returns>
      <param name="localName">要素のローカル名。</param>
      <param name="namespaceURI">要素の名前空間 URI。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> が要素に配置されません。</exception>
      <exception cref="T:System.Xml.XmlException">現在の要素には、子要素が含まれています。または要素の内容を要求された型に変換できません。</exception>
      <exception cref="T:System.ArgumentNullException">引数に null を渡してメソッドが呼び出されました。</exception>
      <exception cref="T:System.ArgumentException">指定されたローカル名と名前空間 URI は、現在読み取り中の要素と一致しません。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsFloat">
      <summary>現在の要素を読み込み、その内容を単精度浮動小数点数として返します。</summary>
      <returns>単精度浮動小数点数としての要素の内容。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> が要素に配置されません。</exception>
      <exception cref="T:System.Xml.XmlException">現在の要素には、子要素が含まれています。または要素の内容を単精度浮動小数点数に変換できません。</exception>
      <exception cref="T:System.ArgumentNullException">引数に null を渡してメソッドが呼び出されました。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsFloat(System.String,System.String)">
      <summary>指定されたローカル名と名前空間 URI が現在の要素のものと一致することを確認し、現在の要素を読み込んで内容を単精度浮動小数点数として返します。</summary>
      <returns>単精度浮動小数点数としての要素の内容。</returns>
      <param name="localName">要素のローカル名。</param>
      <param name="namespaceURI">要素の名前空間 URI。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> が要素に配置されません。</exception>
      <exception cref="T:System.Xml.XmlException">現在の要素には、子要素が含まれています。または要素の内容を単精度浮動小数点数に変換できません。</exception>
      <exception cref="T:System.ArgumentNullException">引数に null を渡してメソッドが呼び出されました。</exception>
      <exception cref="T:System.ArgumentException">指定されたローカル名と名前空間 URI は、現在読み取り中の要素と一致しません。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsInt">
      <summary>現在の要素を読み取り、その内容を 32 ビット符号付き整数として返します。</summary>
      <returns>32 ビット符号付き整数としての要素の内容。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> が要素に配置されません。</exception>
      <exception cref="T:System.Xml.XmlException">現在の要素には、子要素が含まれています。または要素の内容を 32 ビット符号付き整数に変換できません。</exception>
      <exception cref="T:System.ArgumentNullException">引数に null を渡してメソッドが呼び出されました。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsInt(System.String,System.String)">
      <summary>指定されたローカル名と名前空間 URI が現在の要素のものと一致することを確認し、現在の要素を読み込んで内容を 32 ビット符号付き整数として返します。</summary>
      <returns>32 ビット符号付き整数としての要素の内容。</returns>
      <param name="localName">要素のローカル名。</param>
      <param name="namespaceURI">要素の名前空間 URI。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> が要素に配置されません。</exception>
      <exception cref="T:System.Xml.XmlException">現在の要素には、子要素が含まれています。または要素の内容を 32 ビット符号付き整数に変換できません。</exception>
      <exception cref="T:System.ArgumentNullException">引数に null を渡してメソッドが呼び出されました。</exception>
      <exception cref="T:System.ArgumentException">指定されたローカル名と名前空間 URI は、現在読み取り中の要素と一致しません。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsLong">
      <summary>現在の要素を読み取り、その内容を 64 ビット符号付き整数として返します。</summary>
      <returns>64 ビット符号付き整数としての要素の内容。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> が要素に配置されません。</exception>
      <exception cref="T:System.Xml.XmlException">現在の要素には、子要素が含まれています。または要素の内容を 64 ビット符号付き整数に変換できません。</exception>
      <exception cref="T:System.ArgumentNullException">引数に null を渡してメソッドが呼び出されました。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsLong(System.String,System.String)">
      <summary>指定されたローカル名と名前空間 URI が現在の要素のものと一致することを確認し、現在の要素を読み込んで内容を 64 ビット符号付き整数として返します。</summary>
      <returns>64 ビット符号付き整数としての要素の内容。</returns>
      <param name="localName">要素のローカル名。</param>
      <param name="namespaceURI">要素の名前空間 URI。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> が要素に配置されません。</exception>
      <exception cref="T:System.Xml.XmlException">現在の要素には、子要素が含まれています。または要素の内容を 64 ビット符号付き整数に変換できません。</exception>
      <exception cref="T:System.ArgumentNullException">引数に null を渡してメソッドが呼び出されました。</exception>
      <exception cref="T:System.ArgumentException">指定されたローカル名と名前空間 URI は、現在読み取り中の要素と一致しません。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsObject">
      <summary>現在の要素を読み込み、その内容を <see cref="T:System.Object" /> として返します。</summary>
      <returns>最も適切な型のボックス化された共通言語ランタイム (CLR) オブジェクト。<see cref="P:System.Xml.XmlReader.ValueType" /> プロパティは、適切な CLR 型を判断します。内容がリスト型として型指定されている場合、このメソッドは適切な型のボックス化されたオブジェクトの配列を返します。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> が要素に配置されません。</exception>
      <exception cref="T:System.Xml.XmlException">現在の要素には、子要素が含まれています。または要素の内容を要求された型に変換できません。</exception>
      <exception cref="T:System.ArgumentNullException">引数に null を渡してメソッドが呼び出されました。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsObject(System.String,System.String)">
      <summary>指定されたローカル名と名前空間 URI が現在の要素のものと一致することを確認し、現在の要素を読み込んで内容を <see cref="T:System.Object" /> として返します。</summary>
      <returns>最も適切な型のボックス化された共通言語ランタイム (CLR) オブジェクト。<see cref="P:System.Xml.XmlReader.ValueType" /> プロパティは、適切な CLR 型を判断します。内容がリスト型として型指定されている場合、このメソッドは適切な型のボックス化されたオブジェクトの配列を返します。</returns>
      <param name="localName">要素のローカル名。</param>
      <param name="namespaceURI">要素の名前空間 URI。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> が要素に配置されません。</exception>
      <exception cref="T:System.Xml.XmlException">現在の要素には、子要素が含まれています。または要素の内容を要求された型に変換できません。</exception>
      <exception cref="T:System.ArgumentNullException">引数に null を渡してメソッドが呼び出されました。</exception>
      <exception cref="T:System.ArgumentException">指定されたローカル名と名前空間 URI は、現在読み取り中の要素と一致しません。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsObjectAsync">
      <summary>現在の要素を非同期に読み取り、その内容を <see cref="T:System.Object" /> として返します。</summary>
      <returns>最も適切な型のボックス化された共通言語ランタイム (CLR) オブジェクト。<see cref="P:System.Xml.XmlReader.ValueType" /> プロパティは、適切な CLR 型を判断します。内容がリスト型として型指定されている場合、このメソッドは適切な型のボックス化されたオブジェクトの配列を返します。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 非同期メソッドは、<see cref="P:System.Xml.XmlReaderSettings.Async" /> フラグを true に設定せずに呼び出されました。この場合、非同期メソッドを使用するには XmlReaderSettings.Async を true に設定する必要があることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsString">
      <summary>現在の要素を読み取り、その内容を <see cref="T:System.String" /> オブジェクトとして返します。</summary>
      <returns>
        <see cref="T:System.String" /> オブジェクトとしての要素の内容。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> が要素に配置されません。</exception>
      <exception cref="T:System.Xml.XmlException">現在の要素には、子要素が含まれています。または要素の内容を <see cref="T:System.String" /> オブジェクトに変換できません。</exception>
      <exception cref="T:System.ArgumentNullException">引数に null を渡してメソッドが呼び出されました。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsString(System.String,System.String)">
      <summary>指定されたローカル名と名前空間 URI が現在の要素のものと一致することを確認し、現在の要素を読み取って、内容を <see cref="T:System.String" /> オブジェクトとして返します。</summary>
      <returns>
        <see cref="T:System.String" /> オブジェクトとしての要素の内容。</returns>
      <param name="localName">要素のローカル名。</param>
      <param name="namespaceURI">要素の名前空間 URI。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> が要素に配置されません。</exception>
      <exception cref="T:System.Xml.XmlException">現在の要素には、子要素が含まれています。または要素の内容を <see cref="T:System.String" /> オブジェクトに変換できません。</exception>
      <exception cref="T:System.ArgumentNullException">引数に null を渡してメソッドが呼び出されました。</exception>
      <exception cref="T:System.ArgumentException">指定されたローカル名と名前空間 URI は、現在読み取り中の要素と一致しません。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsStringAsync">
      <summary>現在の要素を非同期に読み取り、その内容を <see cref="T:System.String" /> オブジェクトとして返します。</summary>
      <returns>
        <see cref="T:System.String" /> オブジェクトとしての要素の内容。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 非同期メソッドは、<see cref="P:System.Xml.XmlReaderSettings.Async" /> フラグを true に設定せずに呼び出されました。この場合、非同期メソッドを使用するには XmlReaderSettings.Async を true に設定する必要があることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadEndElement">
      <summary>現在のコンテンツ ノードが終了タグで、リーダーを次のノードに進めることを確認します。</summary>
      <exception cref="T:System.Xml.XmlException">現在のノードが終了タグでないか、入力ストリームで正しくない XML が検出されました。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadInnerXml">
      <summary>派生クラスでオーバーライドされると、マークアップを含むすべての内容を文字列として読み取ります。</summary>
      <returns>現在のノード内の、マークアップを含むすべての XML の内容。現在のノードが子を持っていない場合は、空の文字列が返されます。現在のノードが要素でも属性でもない場合は、空の文字列が返されます。</returns>
      <exception cref="T:System.Xml.XmlException">XML が整形式ではありませんでした。または、XML の解析中にエラーが発生しました。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadInnerXmlAsync">
      <summary>マークアップを含むすべてのコンテンツを文字列として非同期に読み取ります。</summary>
      <returns>現在のノード内の、マークアップを含むすべての XML の内容。現在のノードが子を持っていない場合は、空の文字列が返されます。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 非同期メソッドは、<see cref="P:System.Xml.XmlReaderSettings.Async" /> フラグを true に設定せずに呼び出されました。この場合、非同期メソッドを使用するには XmlReaderSettings.Async を true に設定する必要があることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadOuterXml">
      <summary>派生クラスでオーバーライドされると、このノードとそのすべての子を表す内容 (マークアップを含む) を読み取ります。</summary>
      <returns>リーダーが要素ノードまたは属性ノードに配置されている場合、このメソッドは、現在のノードおよびそのすべての子の、マークアップを含む、XML の内容をすべて返します。それ以外の場合は、空の文字列を返します。</returns>
      <exception cref="T:System.Xml.XmlException">XML が整形式ではありませんでした。または、XML の解析中にエラーが発生しました。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadOuterXmlAsync">
      <summary>このノードとその子を表すコンテンツをマークアップを含めて非同期に読み取ります。</summary>
      <returns>リーダーが要素ノードまたは属性ノードに配置されている場合、このメソッドは、現在のノードおよびそのすべての子の、マークアップを含む、XML の内容をすべて返します。それ以外の場合は、空の文字列を返します。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 非同期メソッドは、<see cref="P:System.Xml.XmlReaderSettings.Async" /> フラグを true に設定せずに呼び出されました。この場合、非同期メソッドを使用するには XmlReaderSettings.Async を true に設定する必要があることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadStartElement">
      <summary>現在のノードが要素であるか調べ、リーダーを次のノードに進めます。</summary>
      <exception cref="T:System.Xml.XmlException">入力ストリームで、正しくない XML が検出されました。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadStartElement(System.String)">
      <summary>現在のコンテンツ ノードが、指定した <see cref="P:System.Xml.XmlReader.Name" /> を持つ要素で、リーダーを次のノードに進めることを確認します。</summary>
      <param name="name">要素の限定名。</param>
      <exception cref="T:System.Xml.XmlException">入力ストリームで、正しくない XML が検出されました。または要素の <see cref="P:System.Xml.XmlReader.Name" /> が指定した <paramref name="name" /> と一致しません。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadStartElement(System.String,System.String)">
      <summary>現在のコンテンツ ノードが、指定した <see cref="P:System.Xml.XmlReader.LocalName" /> と <see cref="P:System.Xml.XmlReader.NamespaceURI" /> を持つ要素で、リーダーを次のノードに進めることを確認します。</summary>
      <param name="localname">要素のローカル名。</param>
      <param name="ns">要素の名前空間 URI。</param>
      <exception cref="T:System.Xml.XmlException">入力ストリームで、正しくない XML が検出されました。または見つかった要素の <see cref="P:System.Xml.XmlReader.LocalName" /> プロパティと <see cref="P:System.Xml.XmlReader.NamespaceURI" /> プロパティが指定した引数と一致しません。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.ReadState">
      <summary>派生クラスでオーバーライドされると、リーダーの状態を取得します。</summary>
      <returns>リーダーの状態を指定する列挙値の 1 つ。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadSubtree">
      <summary>現在のノードおよびそのすべての子孫ノードを読み取るために使用できる、新しい XmlReader インスタンスを返します。</summary>
      <returns>新しい XML リーダー インスタンスの設定<see cref="F:System.Xml.ReadState.Initial" />です。呼び出す、<see cref="M:System.Xml.XmlReader.Read" />メソッド呼び出しの前に現在のノードで、新しいリーダーを配置する、<see cref="M:System.Xml.XmlReader.ReadSubtree" />メソッドです。</returns>
      <exception cref="T:System.InvalidOperationException">XML リーダーではありません、このメソッドが呼び出されると、要素に配置されます。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToDescendant(System.String)">
      <summary>指定された修飾名を使用して <see cref="T:System.Xml.XmlReader" /> を次の子孫要素に進めます。</summary>
      <returns>一致する子孫要素が見つかった場合は true。それ以外の場合は false。一致する子孫要素が見つからない場合、要素の終了タグ (<see cref="P:System.Xml.XmlReader.NodeType" /> が XmlNodeType.EndElement) に <see cref="T:System.Xml.XmlReader" /> が配置されます。<see cref="M:System.Xml.XmlReader.ReadToDescendant(System.String)" /> が呼び出されたときに <see cref="T:System.Xml.XmlReader" /> が要素に配置されていない場合、このメソッドは false を返し、<see cref="T:System.Xml.XmlReader" /> の位置を変更しません。</returns>
      <param name="name">移動先となる要素の修飾名。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
      <exception cref="T:System.ArgumentException">パラメーターは空の文字列です。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToDescendant(System.String,System.String)">
      <summary>指定されたローカル名と名前空間 URI を使用して <see cref="T:System.Xml.XmlReader" /> を次の子孫要素に進めます。</summary>
      <returns>一致する子孫要素が見つかった場合は true。それ以外の場合は false。一致する子孫要素が見つからない場合、要素の終了タグ (<see cref="P:System.Xml.XmlReader.NodeType" /> が XmlNodeType.EndElement) に <see cref="T:System.Xml.XmlReader" /> が配置されます。If the <see cref="T:System.Xml.XmlReader" /> is not positioned on an element when <see cref="M:System.Xml.XmlReader.ReadToDescendant(System.String,System.String)" /> was called, this method returns false and the position of the <see cref="T:System.Xml.XmlReader" /> is not changed.</returns>
      <param name="localName">移動先となる要素のローカル名。</param>
      <param name="namespaceURI">移動先となる要素の名前空間 URI。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
      <exception cref="T:System.ArgumentNullException">両方のパラメーター値が null です。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToFollowing(System.String)">
      <summary>指定された修飾名の要素が見つかるまで読み込みます。</summary>
      <returns>一致する要素が見つかる場合は true。それ以外の場合は false になり、<see cref="T:System.Xml.XmlReader" /> がファイルの末尾に置かれます。</returns>
      <param name="name">要素の限定名。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
      <exception cref="T:System.ArgumentException">パラメーターは空の文字列です。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToFollowing(System.String,System.String)">
      <summary>指定されたローカル名と名前空間 URI が見つかるまで要素を読み込みます。</summary>
      <returns>一致する要素が見つかる場合は true。それ以外の場合は false になり、<see cref="T:System.Xml.XmlReader" /> がファイルの末尾に置かれます。</returns>
      <param name="localName">要素のローカル名。</param>
      <param name="namespaceURI">要素の名前空間 URI。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
      <exception cref="T:System.ArgumentNullException">両方のパラメーター値が null です。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToNextSibling(System.String)">
      <summary>指定された修飾名を使用して XmlReader を次の兄弟要素に進めます。</summary>
      <returns>一致する兄弟要素が見つかった場合は true。それ以外の場合は false。一致する兄弟要素が見つからない場合、親要素の終了タグ (<see cref="P:System.Xml.XmlReader.NodeType" /> が XmlNodeType.EndElement) に XmlReader が配置されます。</returns>
      <param name="name">移動先となる兄弟要素の修飾名。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
      <exception cref="T:System.ArgumentException">パラメーターは空の文字列です。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToNextSibling(System.String,System.String)">
      <summary>指定されたローカル名と名前空間 URI を使用して XmlReader を次の兄弟要素に進めます。</summary>
      <returns>一致する兄弟要素が見つかった場合は true。それ以外の場合は false。一致する兄弟要素が見つからない場合、親要素の終了タグ (<see cref="P:System.Xml.XmlReader.NodeType" /> が XmlNodeType.EndElement) に XmlReader が配置されます。</returns>
      <param name="localName">移動先となる兄弟要素のローカル名。</param>
      <param name="namespaceURI">移動先となる兄弟要素の名前空間 URI。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
      <exception cref="T:System.ArgumentNullException">両方のパラメーター値が null です。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadValueChunk(System.Char[],System.Int32,System.Int32)">
      <summary>XML ドキュメントに埋め込まれたテキストの大量のストリームを読み込みます。</summary>
      <returns>バッファー内へ読み取られた文字数。それ以上テキストの内容がない場合は、値として 0 が返されます。</returns>
      <param name="buffer">テキストの内容が書き込まれるバッファーとして機能する文字の配列。この値を null にすることはできません。</param>
      <param name="index">
        <see cref="T:System.Xml.XmlReader" /> が結果のコピーを開始できる、バッファー内のオフセット。</param>
      <param name="count">バッファーにコピーする最大文字数。コピーされた実際の文字数は、このメソッドから返されます。</param>
      <exception cref="T:System.InvalidOperationException">現在のノードに値がありません (<see cref="P:System.Xml.XmlReader.HasValue" /> が false)。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> 値が null です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">バッファー内のインデックス、またはインデックスとカウントの合計値が、割り当てられているバッファー サイズを超えています。</exception>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.XmlReader" /> 実装が、このメソッドをサポートしていません。</exception>
      <exception cref="T:System.Xml.XmlException">XML データは、整形式ではありません。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadValueChunkAsync(System.Char[],System.Int32,System.Int32)">
      <summary>XML ドキュメントに埋め込まれたテキストの大量のストリームを非同期に読み取ります。</summary>
      <returns>バッファー内へ読み取られた文字数。それ以上テキストの内容がない場合は、値として 0 が返されます。</returns>
      <param name="buffer">テキストの内容が書き込まれるバッファーとして機能する文字の配列。この値を null にすることはできません。</param>
      <param name="index">
        <see cref="T:System.Xml.XmlReader" /> が結果のコピーを開始できる、バッファー内のオフセット。</param>
      <param name="count">バッファーにコピーする最大文字数。コピーされた実際の文字数は、このメソッドから返されます。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 非同期メソッドは、<see cref="P:System.Xml.XmlReaderSettings.Async" /> フラグを true に設定せずに呼び出されました。この場合、非同期メソッドを使用するには XmlReaderSettings.Async を true に設定する必要があることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ResolveEntity">
      <summary>派生クラスでオーバーライドされると、EntityReference ノードのエンティティ参照を解決します。</summary>
      <exception cref="T:System.InvalidOperationException">リーダーが EntityReference ノードに配置されていません。つまり、このリーダーの実装では、エンティティを解決できません。<see cref="P:System.Xml.XmlReader.CanResolveEntity" /> は false を返します。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Settings">
      <summary>Gets the <see cref="T:System.Xml.XmlReaderSettings" /> object used to create this <see cref="T:System.Xml.XmlReader" /> instance.</summary>
      <returns>このリーダーのインスタンスを作成するために使用した <see cref="T:System.Xml.XmlReaderSettings" /> オブジェクト。<see cref="Overload:System.Xml.XmlReader.Create" /> メソッドを使用しないでこのリーダーを作成した場合、このプロパティは null を返します。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Skip">
      <summary>現在のノードの子をスキップします。</summary>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.SkipAsync">
      <summary>現在のノードの子を非同期にスキップします。</summary>
      <returns>現在のノード。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 非同期メソッドは、<see cref="P:System.Xml.XmlReaderSettings.Async" /> フラグを true に設定せずに呼び出されました。この場合、非同期メソッドを使用するには XmlReaderSettings.Async を true に設定する必要があることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Value">
      <summary>派生クラスでオーバーライドされると、現在のノードのテキスト値を取得します。</summary>
      <returns>返される値は、ノードの <see cref="P:System.Xml.XmlReader.NodeType" /> によって異なります。返す値を持つノード型の一覧を次の表に示します。これ以外のノード型はすべて String.Empty を返します。ノード型値 Attribute属性の値。 CDATACDATA セクションの内容。 Commentコメントの内容。 DocumentType内部サブセット。 ProcessingInstructionターゲットを含まない、全体の内容。 SignificantWhitespace混合コンテンツ モデル内のマークアップ間の空白。 Textテキスト ノードの内容。 Whitespaceマークアップ間の空白。 XmlDeclaration宣言の内容。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.ValueType">
      <summary>現在のノードの共通言語ランタイム (CLR) 型を取得します。</summary>
      <returns>ノードの型指定された値に対応する CLR 型。既定値は、System.String です。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.XmlLang">
      <summary>派生クラスでオーバーライドされると、現在の xml:lang スコープを取得します。</summary>
      <returns>現在の xml:lang スコープ。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.XmlSpace">
      <summary>派生クラスでオーバーライドされると、現在の xml:space スコープを取得します。</summary>
      <returns>
        <see cref="T:System.Xml.XmlSpace" /> 値のいずれか。xml:space スコープが存在しない場合、このプロパティは既定の XmlSpace.None に設定されます。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> メソッドは、前の非同期操作が終了する前に呼び出されました。この場合、非同期操作が既に進行中であることを示すメッセージと共に <see cref="T:System.InvalidOperationException" /> がスローされます。</exception>
    </member>
    <member name="T:System.Xml.XmlReaderSettings">
      <summary>
        <see cref="Overload:System.Xml.XmlReader.Create" /> メソッドで作成された <see cref="T:System.Xml.XmlReader" /> オブジェクトでサポートする一連の機能を指定します。</summary>
    </member>
    <member name="M:System.Xml.XmlReaderSettings.#ctor">
      <summary>
        <see cref="T:System.Xml.XmlReaderSettings" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.Async">
      <summary>非同期 <see cref="T:System.Xml.XmlReader" /> メソッドを <see cref="T:System.Xml.XmlReader" /> の特定のインスタンスで使用できるかどうかを取得または設定します。</summary>
      <returns>非同期メソッドを使用できる場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.CheckCharacters">
      <summary>文字のチェックを行うかどうかを示す値を取得または設定します。</summary>
      <returns>文字をチェックする場合は true。それ以外の場合は false。既定値は、true です。メモ<see cref="T:System.Xml.XmlReader" /> がテキスト データの処理を行う場合は、プロパティの設定に関係なく、XML 名とテキストの内容が有効であることを常にチェックします。<see cref="P:System.Xml.XmlReaderSettings.CheckCharacters" /> を false に設定すると、文字エンティティ参照に対する文字のチェック機能がオフになります。</returns>
    </member>
    <member name="M:System.Xml.XmlReaderSettings.Clone">
      <summary>
        <see cref="T:System.Xml.XmlReaderSettings" /> インスタンスのコピーを作成します。</summary>
      <returns>複製された <see cref="T:System.Xml.XmlReaderSettings" /> オブジェクト。</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.CloseInput">
      <summary>リーダーを閉じるときに基になるストリームまたは <see cref="T:System.IO.TextReader" /> を閉じる必要があるかどうかを示す値を取得または設定します。</summary>
      <returns>リーダーを閉じるときに基になるストリームまたは <see cref="T:System.IO.TextReader" /> を閉じる場合は true。それ以外の場合は false。既定値は、false です。</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.ConformanceLevel">
      <summary>
        <see cref="T:System.Xml.XmlReader" /> が従う準拠のレベルを取得または設定します。</summary>
      <returns>XML リーダーが適用する準拠のレベルを指定する列挙値のいずれか。既定値は、<see cref="F:System.Xml.ConformanceLevel.Document" /> です。</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.DtdProcessing">
      <summary>DTD の処理を決定する値を取得または設定します。</summary>
      <returns>DTD の処理を決定する列挙値の 1 つ。既定値は、<see cref="F:System.Xml.DtdProcessing.Prohibit" /> です。</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.IgnoreComments">
      <summary>コメントを無視するかどうかを示す値を取得または設定します。</summary>
      <returns>コメントを無視する場合は true。それ以外の場合は false。既定値は、false です。</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.IgnoreProcessingInstructions">
      <summary>処理命令を無視するかどうかを示す値を取得または設定します。</summary>
      <returns>処理命令を無視する場合は true。それ以外の場合は false。既定値は、false です。</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.IgnoreWhitespace">
      <summary>意味のない空白を無視するかどうかを示す値を取得または設定します。</summary>
      <returns>空白を無視する場合は true。それ以外の場合は false。既定値は、false です。</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.LineNumberOffset">
      <summary>
        <see cref="T:System.Xml.XmlReader" /> オブジェクトの行番号オフセットを取得または設定します。</summary>
      <returns>行番号オフセット。既定値は 0 です。</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.LinePositionOffset">
      <summary>
        <see cref="T:System.Xml.XmlReader" /> オブジェクトの行番号オフセットを取得または設定します。</summary>
      <returns>ラインの位置のオフセット。既定値は 0 です。</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.MaxCharactersFromEntities">
      <summary>エンティティの展開時に許容されるドキュメント内の最大文字数を示す値を取得または設定します。</summary>
      <returns>エンティティの展開時に許容される最大文字数。既定値は 0 です。</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.MaxCharactersInDocument">
      <summary>XML ドキュメントの最大文字数を示す値を取得または設定します。ゼロ (0) の値は、XML ドキュメントのサイズに制限がないことを示します。0 以外の値は、最大サイズを文字数で示します。</summary>
      <returns>XML ドキュメント内の最大文字数。既定値は 0 です。</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.NameTable">
      <summary>最小単位に分割された文字列の比較に使用する <see cref="T:System.Xml.XmlNameTable" /> を取得または設定します。</summary>
      <returns>この <see cref="T:System.Xml.XmlReaderSettings" /> オブジェクトを使用して作成されたすべての <see cref="T:System.Xml.XmlReader" /> インスタンスで使用する、最小単位に分割されたすべての文字列を格納する <see cref="T:System.Xml.XmlNameTable" />。既定値は、null です。この値が null の場合、作成された <see cref="T:System.Xml.XmlReader" /> インスタンスは、新しい空の <see cref="T:System.Xml.NameTable" /> を使用します。</returns>
    </member>
    <member name="M:System.Xml.XmlReaderSettings.Reset">
      <summary>設定クラスのメンバーを既定値にリセットします。</summary>
    </member>
    <member name="T:System.Xml.XmlSpace">
      <summary>現在の xml:space スコープを指定します。</summary>
    </member>
    <member name="F:System.Xml.XmlSpace.Default">
      <summary>xml:space スコープと default は等価です。</summary>
    </member>
    <member name="F:System.Xml.XmlSpace.None">
      <summary>xml:space スコープがありません。</summary>
    </member>
    <member name="F:System.Xml.XmlSpace.Preserve">
      <summary>xml:space スコープと preserve は等価です。</summary>
    </member>
    <member name="T:System.Xml.XmlWriter">
      <summary>XML データが格納されたストリームまたはファイルを、高速かつ非キャッシュで前方のみに生成する方法を提供するライターを表します。</summary>
    </member>
    <member name="M:System.Xml.XmlWriter.#ctor">
      <summary>
        <see cref="T:System.Xml.XmlWriter" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.IO.Stream)">
      <summary>指定されたストリームを使用して新しい <see cref="T:System.Xml.XmlWriter" /> インスタンスを作成します。</summary>
      <returns>
        <see cref="T:System.Xml.XmlWriter" /> オブジェクト。</returns>
      <param name="output">書き込み先のストリーム。<see cref="T:System.Xml.XmlWriter" /> は、XML 1.0 テキスト構文を書き込み、指定されたストリームにそれを付加します。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="stream" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.IO.Stream,System.Xml.XmlWriterSettings)">
      <summary>ストリームと <see cref="T:System.Xml.XmlWriterSettings" /> オブジェクトを使用して新しい <see cref="T:System.Xml.XmlWriter" /> インスタンスを作成します。</summary>
      <returns>
        <see cref="T:System.Xml.XmlWriter" /> オブジェクト。</returns>
      <param name="output">書き込み先のストリーム。<see cref="T:System.Xml.XmlWriter" /> は、XML 1.0 テキスト構文を書き込み、指定されたストリームにそれを付加します。</param>
      <param name="settings">新しい <see cref="T:System.Xml.XmlWriter" /> インスタンスを構成するために使用される <see cref="T:System.Xml.XmlWriterSettings" /> オブジェクト。これが null である場合は、既定の設定で <see cref="T:System.Xml.XmlWriterSettings" /> が使用されます。<see cref="T:System.Xml.XmlWriter" /> を <see cref="M:System.Xml.Xsl.XslCompiledTransform.Transform(System.String,System.Xml.XmlWriter)" /> メソッドと組み合わせて使用する場合は、<see cref="P:System.Xml.Xsl.XslCompiledTransform.OutputSettings" /> プロパティを使って、正しい設定の割り当てられた <see cref="T:System.Xml.XmlWriterSettings" /> オブジェクトを取得する必要があります。これにより、作成された <see cref="T:System.Xml.XmlWriter" /> オブジェクトに正しい出力設定が適用されます。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="stream" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.IO.TextWriter)">
      <summary>指定された <see cref="T:System.IO.TextWriter" /> を使用して新しい <see cref="T:System.Xml.XmlWriter" /> インスタンスを作成します。</summary>
      <returns>
        <see cref="T:System.Xml.XmlWriter" /> オブジェクト。</returns>
      <param name="output">書き込み先の <see cref="T:System.IO.TextWriter" />。<see cref="T:System.Xml.XmlWriter" /> は、XML 1.0 テキスト構文を書き込み、指定された <see cref="T:System.IO.TextWriter" /> にそれを付加します。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="text" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.IO.TextWriter,System.Xml.XmlWriterSettings)">
      <summary>
        <see cref="T:System.IO.TextWriter" /> オブジェクトと <see cref="T:System.Xml.XmlWriterSettings" /> オブジェクトを使用して新しい <see cref="T:System.Xml.XmlWriter" /> インスタンスを作成します。</summary>
      <returns>
        <see cref="T:System.Xml.XmlWriter" /> オブジェクト。</returns>
      <param name="output">書き込み先の <see cref="T:System.IO.TextWriter" />。<see cref="T:System.Xml.XmlWriter" /> は、XML 1.0 テキスト構文を書き込み、指定された <see cref="T:System.IO.TextWriter" /> にそれを付加します。</param>
      <param name="settings">新しい <see cref="T:System.Xml.XmlWriter" /> インスタンスを構成するために使用される <see cref="T:System.Xml.XmlWriterSettings" /> オブジェクト。これが null である場合は、既定の設定で <see cref="T:System.Xml.XmlWriterSettings" /> が使用されます。<see cref="T:System.Xml.XmlWriter" /> を <see cref="M:System.Xml.Xsl.XslCompiledTransform.Transform(System.String,System.Xml.XmlWriter)" /> メソッドと組み合わせて使用する場合は、<see cref="P:System.Xml.Xsl.XslCompiledTransform.OutputSettings" /> プロパティを使って、正しい設定の割り当てられた <see cref="T:System.Xml.XmlWriterSettings" /> オブジェクトを取得する必要があります。これにより、作成された <see cref="T:System.Xml.XmlWriter" /> オブジェクトに正しい出力設定が適用されます。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="text" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.Text.StringBuilder)">
      <summary>指定された <see cref="T:System.Text.StringBuilder" /> を使用して新しい <see cref="T:System.Xml.XmlWriter" /> インスタンスを作成します。</summary>
      <returns>
        <see cref="T:System.Xml.XmlWriter" /> オブジェクト。</returns>
      <param name="output">書き込み先の <see cref="T:System.Text.StringBuilder" />。<see cref="T:System.Xml.XmlWriter" /> で書き込まれた内容が、<see cref="T:System.Text.StringBuilder" /> に付加されます。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="builder" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.Text.StringBuilder,System.Xml.XmlWriterSettings)">
      <summary>
        <see cref="T:System.Text.StringBuilder" /> オブジェクトと <see cref="T:System.Xml.XmlWriterSettings" /> オブジェクトを使用して新しい <see cref="T:System.Xml.XmlWriter" /> インスタンスを作成します。</summary>
      <returns>
        <see cref="T:System.Xml.XmlWriter" /> オブジェクト。</returns>
      <param name="output">書き込み先の <see cref="T:System.Text.StringBuilder" />。<see cref="T:System.Xml.XmlWriter" /> で書き込まれた内容が、<see cref="T:System.Text.StringBuilder" /> に付加されます。</param>
      <param name="settings">新しい <see cref="T:System.Xml.XmlWriter" /> インスタンスを構成するために使用される <see cref="T:System.Xml.XmlWriterSettings" /> オブジェクト。これが null である場合は、既定の設定で <see cref="T:System.Xml.XmlWriterSettings" /> が使用されます。<see cref="T:System.Xml.XmlWriter" /> を <see cref="M:System.Xml.Xsl.XslCompiledTransform.Transform(System.String,System.Xml.XmlWriter)" /> メソッドと組み合わせて使用する場合は、<see cref="P:System.Xml.Xsl.XslCompiledTransform.OutputSettings" /> プロパティを使って、正しい設定の割り当てられた <see cref="T:System.Xml.XmlWriterSettings" /> オブジェクトを取得する必要があります。これにより、作成された <see cref="T:System.Xml.XmlWriter" /> オブジェクトに正しい出力設定が適用されます。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="builder" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.Xml.XmlWriter)">
      <summary>指定された <see cref="T:System.Xml.XmlWriter" /> オブジェクトを使用して新しい <see cref="T:System.Xml.XmlWriter" /> インスタンスを作成します。</summary>
      <returns>指定された <see cref="T:System.Xml.XmlWriter" /> オブジェクトをラップする <see cref="T:System.Xml.XmlWriter" /> オブジェクト。</returns>
      <param name="output">基になるライターとして使用する <see cref="T:System.Xml.XmlWriter" /> オブジェクト。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="writer" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.Xml.XmlWriter,System.Xml.XmlWriterSettings)">
      <summary>
        <see cref="T:System.Xml.XmlWriter" /> オブジェクトと <see cref="T:System.Xml.XmlWriterSettings" /> オブジェクトを使用して新しい <see cref="T:System.Xml.XmlWriter" /> インスタンスを作成します。</summary>
      <returns>指定された <see cref="T:System.Xml.XmlWriter" /> オブジェクトをラップする <see cref="T:System.Xml.XmlWriter" /> オブジェクト。</returns>
      <param name="output">基になるライターとして使用する <see cref="T:System.Xml.XmlWriter" /> オブジェクト。</param>
      <param name="settings">新しい <see cref="T:System.Xml.XmlWriter" /> インスタンスを構成するために使用される <see cref="T:System.Xml.XmlWriterSettings" /> オブジェクト。これが null である場合は、既定の設定で <see cref="T:System.Xml.XmlWriterSettings" /> が使用されます。<see cref="T:System.Xml.XmlWriter" /> を <see cref="M:System.Xml.Xsl.XslCompiledTransform.Transform(System.String,System.Xml.XmlWriter)" /> メソッドと組み合わせて使用する場合は、<see cref="P:System.Xml.Xsl.XslCompiledTransform.OutputSettings" /> プロパティを使って、正しい設定の割り当てられた <see cref="T:System.Xml.XmlWriterSettings" /> オブジェクトを取得する必要があります。これにより、作成された <see cref="T:System.Xml.XmlWriter" /> オブジェクトに正しい出力設定が適用されます。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="writer" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Dispose">
      <summary>
        <see cref="T:System.Xml.XmlWriter" /> クラスの現在のインスタンスによって使用されているすべてのリソースを解放します。</summary>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Dispose(System.Boolean)">
      <summary>
        <see cref="T:System.Xml.XmlWriter" /> によって使用されているアンマネージ リソースを解放し、オプションでマネージ リソースも解放します。</summary>
      <param name="disposing">マネージ リソースとアンマネージ リソースの両方を解放する場合は true。アンマネージ リソースだけを解放する場合は false。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Flush">
      <summary>派生クラスでオーバーライドされると、バッファー内のデータをすべて基になるストリームにフラッシュし、基になるストリームもフラッシュします。</summary>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.FlushAsync">
      <summary>バッファー内のデータをすべて基になるストリームに非同期にフラッシュし、基になるストリームもフラッシュします。</summary>
      <returns>非同期の Flush 操作を表すタスク。</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.LookupPrefix(System.String)">
      <summary>派生クラスでオーバーライドされると、名前空間 URI の現在の名前空間スコープで定義された最も近いプリフィックスを返します。</summary>
      <returns>一致するプリフィックス。現在のスコープに一致する名前空間 URI が見つからない場合は null。</returns>
      <param name="ns">検索対象のプリフィックスを持つ名前空間 URI。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="ns" /> is either null or String.Empty.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="P:System.Xml.XmlWriter.Settings">
      <summary>この <see cref="T:System.Xml.XmlWriter" /> インスタンスを作成するために使用された <see cref="T:System.Xml.XmlWriterSettings" /> オブジェクトを取得します。</summary>
      <returns>このライターのインスタンスを作成するために使用した <see cref="T:System.Xml.XmlWriterSettings" /> オブジェクト。このライターが <see cref="Overload:System.Xml.XmlWriter.Create" /> メソッドを使用して作成されなかった場合、このプロパティは null を返します。</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributes(System.Xml.XmlReader,System.Boolean)">
      <summary>派生クラスによってオーバーライドされると、<see cref="T:System.Xml.XmlReader" /> の現在の位置で見つかったすべての属性を書き込みます。</summary>
      <param name="reader">属性のコピー元の XmlReader。</param>
      <param name="defattr">XmlReader の既定の属性をコピーする場合は true。それ以外の場合は false。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="reader" /> is null. </exception>
      <exception cref="T:System.Xml.XmlException">The reader is not positioned on an element, attribute or XmlDeclaration node. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributesAsync(System.Xml.XmlReader,System.Boolean)">
      <summary>
        <see cref="T:System.Xml.XmlReader" /> の現在の位置で見つかったすべての属性を非同期に書き込みます。</summary>
      <returns>非同期の WriteAttributes 操作を表すタスク。</returns>
      <param name="reader">属性のコピー元の XmlReader。</param>
      <param name="defattr">XmlReader の既定の属性をコピーする場合は true。それ以外の場合は false。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributeString(System.String,System.String)">
      <summary>派生クラスでオーバーライドされると、指定したローカル名と値の属性を書き込みます。</summary>
      <param name="localName">属性のローカル名。</param>
      <param name="value">属性の値。</param>
      <exception cref="T:System.InvalidOperationException">The state of writer is not WriteState.Element or writer is closed. </exception>
      <exception cref="T:System.ArgumentException">The xml:space or xml:lang attribute value is invalid. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributeString(System.String,System.String,System.String)">
      <summary>派生クラスでオーバーライドされると、指定したローカル名、名前空間 URI、および値の属性を書き込みます。</summary>
      <param name="localName">属性のローカル名。</param>
      <param name="ns">属性に関連付ける名前空間 URI。</param>
      <param name="value">属性の値。</param>
      <exception cref="T:System.InvalidOperationException">The state of writer is not WriteState.Element or writer is closed. </exception>
      <exception cref="T:System.ArgumentException">The xml:space or xml:lang attribute value is invalid. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributeString(System.String,System.String,System.String,System.String)">
      <summary>派生クラスでオーバーライドされると、指定したプリフィックス、ローカル名、名前空間 URI、および値の属性を書き込みます。</summary>
      <param name="prefix">属性の名前空間プレフィックス。</param>
      <param name="localName">属性のローカル名。</param>
      <param name="ns">属性の名前空間 URI。</param>
      <param name="value">属性の値。</param>
      <exception cref="T:System.InvalidOperationException">The state of writer is not WriteState.Element or writer is closed. </exception>
      <exception cref="T:System.ArgumentException">The xml:space or xml:lang attribute value is invalid. </exception>
      <exception cref="T:System.Xml.XmlException">The <paramref name="localName" /> or <paramref name="ns" /> is null. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributeStringAsync(System.String,System.String,System.String,System.String)">
      <summary>指定されたプレフィックス、ローカル名、名前空間 URI、および値を使用して属性を非同期に書き込みます。</summary>
      <returns>非同期の WriteAttributeString 操作を表すタスク。</returns>
      <param name="prefix">属性の名前空間プレフィックス。</param>
      <param name="localName">属性のローカル名。</param>
      <param name="ns">属性の名前空間 URI。</param>
      <param name="value">属性の値。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteBase64(System.Byte[],System.Int32,System.Int32)">
      <summary>派生クラスでオーバーライドされると、指定したバイナリ バイトを Base64 としてエンコードし、その結果生成されるテキストを書き込みます。</summary>
      <param name="buffer">エンコードするバイト配列。</param>
      <param name="index">書き込むバイトの開始を示すバッファー内の位置。</param>
      <param name="count">書き込むバイト数。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero. -or-The buffer length minus <paramref name="index" /> is less than <paramref name="count" />.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteBase64Async(System.Byte[],System.Int32,System.Int32)">
      <summary>指定したバイナリ バイトを base64 として非同期にエンコードし、その結果生成されるテキストを書き込みます。</summary>
      <returns>非同期の WriteBase64 操作を表すタスク。</returns>
      <param name="buffer">エンコードするバイト配列。</param>
      <param name="index">書き込むバイトの開始を示すバッファー内の位置。</param>
      <param name="count">書き込むバイト数。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteBinHex(System.Byte[],System.Int32,System.Int32)">
      <summary>派生クラスでオーバーライドされると、指定されたバイナリ バイトを BinHex としてエンコードし、その結果生成されるテキストを書き込みます。</summary>
      <param name="buffer">エンコードするバイト配列。</param>
      <param name="index">書き込むバイトの開始を示すバッファー内の位置。</param>
      <param name="count">書き込むバイト数。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">The writer is closed or in error state.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero. -or-The buffer length minus <paramref name="index" /> is less than <paramref name="count" />.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteBinHexAsync(System.Byte[],System.Int32,System.Int32)">
      <summary>指定したバイナリ バイトを BinHex として非同期にエンコードし、その結果生成されるテキストを書き込みます。</summary>
      <returns>非同期の WriteBinHex 操作を表すタスク。</returns>
      <param name="buffer">エンコードするバイト配列。</param>
      <param name="index">書き込むバイトの開始を示すバッファー内の位置。</param>
      <param name="count">書き込むバイト数。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCData(System.String)">
      <summary>派生クラスでオーバーライドされると、指定したテキストを含む &lt;![CDATA[...]]&gt; ブロックを書き込みます。</summary>
      <param name="text">CDATA ブロック内に配置するテキスト。</param>
      <exception cref="T:System.ArgumentException">The text would result in a non-well formed XML document.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCDataAsync(System.String)">
      <summary>指定したテキストを格納する &lt;![CDATA[...]]&gt; ブロックを非同期に書き込みます。</summary>
      <returns>非同期の WriteCData 操作を表すタスク。</returns>
      <param name="text">CDATA ブロック内に配置するテキスト。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCharEntity(System.Char)">
      <summary>派生クラスでオーバーライドされると、指定した Unicode 文字値の文字エンティティを強制的に生成します。</summary>
      <param name="ch">文字エンティティを生成する Unicode 文字。</param>
      <exception cref="T:System.ArgumentException">The character is in the surrogate pair character range, 0xd800 - 0xdfff.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCharEntityAsync(System.Char)">
      <summary>指定した Unicode 文字値の文字エンティティを非同期に強制的に生成します。</summary>
      <returns>非同期の WriteCharEntity 操作を表すタスク。</returns>
      <param name="ch">文字エンティティを生成する Unicode 文字。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteChars(System.Char[],System.Int32,System.Int32)">
      <summary>派生クラスでオーバーライドされると、一度に 1 つのバッファーにテキストを書き込みます。</summary>
      <param name="buffer">書き込むテキストを格納している文字配列。</param>
      <param name="index">書き込むテキストの開始を示すバッファー内の位置。</param>
      <param name="count">書き込む文字数。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero.-or-The buffer length minus <paramref name="index" /> is less than <paramref name="count" />; the call results in surrogate pair characters being split or an invalid surrogate pair being written.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="buffer" /> parameter value is not valid.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCharsAsync(System.Char[],System.Int32,System.Int32)">
      <summary>一度に 1 つのバッファーにテキストを非同期に書き込みます。</summary>
      <returns>非同期の WriteChars 操作を表すタスク。</returns>
      <param name="buffer">書き込むテキストを格納している文字配列。</param>
      <param name="index">書き込むテキストの開始を示すバッファー内の位置。</param>
      <param name="count">書き込む文字数。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteComment(System.String)">
      <summary>派生クラスでオーバーライドされると、指定したテキストを格納している &lt;!--...--&gt; コメントを書き込みます。</summary>
      <param name="text">コメント内に配置するテキスト。</param>
      <exception cref="T:System.ArgumentException">The text would result in a non-well-formed XML document.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCommentAsync(System.String)">
      <summary>指定したテキストを含むコメント &lt;!--...--&gt; を非同期に書き込みます。</summary>
      <returns>非同期の WriteComment 操作を表すタスク。</returns>
      <param name="text">コメント内に配置するテキスト。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteDocType(System.String,System.String,System.String,System.String)">
      <summary>派生クラスでオーバーライドされると、指定した名前とオプションの属性を含む DOCTYPE 宣言を書き込みます。</summary>
      <param name="name">DOCTYPE の名前。これを空にすることはできません。</param>
      <param name="pubid">null でない場合は、PUBLIC "pubid" "sysid" も書き込みます。<paramref name="pubid" /> と <paramref name="sysid" /> は、指定した引数の値に置き換えられます。</param>
      <param name="sysid">
        <paramref name="pubid" /> が null で <paramref name="sysid" /> が null でない場合は、SYSTEM "sysid" を書き込みます。<paramref name="sysid" /> は、この引数の値に置き換えられます。</param>
      <param name="subset">null でない場合は、[subset] を書き込みます。subset は、この引数の値に置き換えられます。</param>
      <exception cref="T:System.InvalidOperationException">This method was called outside the prolog (after the root element). </exception>
      <exception cref="T:System.ArgumentException">The value for <paramref name="name" /> would result in invalid XML.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteDocTypeAsync(System.String,System.String,System.String,System.String)">
      <summary>指定された名前とオプション属性を使用して DOC 宣言を非同期に書き込みます。</summary>
      <returns>非同期の WriteDocType 操作を表すタスク。</returns>
      <param name="name">DOCTYPE の名前。これを空にすることはできません。</param>
      <param name="pubid">null でない場合は、PUBLIC "pubid" "sysid" も書き込みます。<paramref name="pubid" /> と <paramref name="sysid" /> は、指定した引数の値に置き換えられます。</param>
      <param name="sysid">
        <paramref name="pubid" /> が null で <paramref name="sysid" /> が null でない場合は、SYSTEM "sysid" を書き込みます。<paramref name="sysid" /> は、この引数の値に置き換えられます。</param>
      <param name="subset">null でない場合は、[subset] を書き込みます。subset は、この引数の値に置き換えられます。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteElementString(System.String,System.String)">
      <summary>指定されたローカル名および値を使用して要素を書き込みます。</summary>
      <param name="localName">要素のローカル名。</param>
      <param name="value">要素の値。</param>
      <exception cref="T:System.ArgumentException">The <paramref name="localName" /> value is null or an empty string.-or-The parameter values are not valid.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteElementString(System.String,System.String,System.String)">
      <summary>指定されたローカル名、名前空間 URI、および値を使用して要素を書き込みます。</summary>
      <param name="localName">要素のローカル名。</param>
      <param name="ns">要素に関連付ける名前空間 URI。</param>
      <param name="value">要素の値。</param>
      <exception cref="T:System.ArgumentException">The <paramref name="localName" /> value is null or an empty string.-or-The parameter values are not valid.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteElementString(System.String,System.String,System.String,System.String)">
      <summary>指定されたプレフィックス、ローカル名、名前空間 URI、および値を使用して要素を書き込みます。</summary>
      <param name="prefix">要素のプレフィックス。</param>
      <param name="localName">要素のローカル名。</param>
      <param name="ns">要素の名前空間 URI。</param>
      <param name="value">要素の値。</param>
      <exception cref="T:System.ArgumentException">The <paramref name="localName" /> value is null or an empty string.-or-The parameter values are not valid.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteElementStringAsync(System.String,System.String,System.String,System.String)">
      <summary>指定されたプレフィックス、ローカル名、名前空間 URI、および値を使用して要素を非同期に書き込みます。</summary>
      <returns>非同期の WriteElementString 操作を表すタスク。</returns>
      <param name="prefix">要素のプレフィックス。</param>
      <param name="localName">要素のローカル名。</param>
      <param name="ns">要素の名前空間 URI。</param>
      <param name="value">要素の値。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndAttribute">
      <summary>派生クラスでオーバーライドされると、前の <see cref="M:System.Xml.XmlWriter.WriteStartAttribute(System.String,System.String)" /> 呼び出しを閉じます。</summary>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndAttributeAsync">
      <summary>前の <see cref="M:System.Xml.XmlWriter.WriteStartAttribute(System.String,System.String)" /> 呼び出しを非同期に閉じます。</summary>
      <returns>非同期の WriteEndAttribute 操作を表すタスク。</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndDocument">
      <summary>派生クラスでオーバーライドされると、開いている任意の要素または属性を閉じ、ライターを Start 状態に戻します。</summary>
      <exception cref="T:System.ArgumentException">The XML document is invalid.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndDocumentAsync">
      <summary>開いている要素または属性を非同期に閉じ、ライターを Start 状態に戻します。</summary>
      <returns>非同期の WriteEndDocument 操作を表すタスク。</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndElement">
      <summary>派生クラスでオーバーライドされると、1 つの要素を閉じ、対応する名前空間スコープをポップします。</summary>
      <exception cref="T:System.InvalidOperationException">This results in an invalid XML document.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndElementAsync">
      <summary>1 つの要素を非同期に閉じ、対応する名前空間スコープをポップします。</summary>
      <returns>非同期の WriteEndElement 操作を表すタスク。</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEntityRef(System.String)">
      <summary>派生クラスでオーバーライドされると、&amp;name; などのエンティティ参照を書き込みます。</summary>
      <param name="name">エンティティ参照の名前。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> is either null or String.Empty.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEntityRefAsync(System.String)">
      <summary>エンティティ参照を &amp;name; として非同期的に書き込みます。</summary>
      <returns>非同期の WriteEntityRef 操作を表すタスク。</returns>
      <param name="name">エンティティ参照の名前。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteFullEndElement">
      <summary>派生クラスでオーバーライドされると、1 つの要素を閉じ、対応する名前空間スコープをポップします。</summary>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteFullEndElementAsync">
      <summary>1 つの要素を非同期に閉じ、対応する名前空間スコープをポップします。</summary>
      <returns>非同期の WriteFullEndElement 操作を表すタスク。</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteName(System.String)">
      <summary>派生クラスでオーバーライドされると、指定した名前を書き込み、その名前が W3C 勧告『XML 1.0』(http://www.w3.org/TR/1998/REC-xml-19980210#NT-Name) に準拠した有効な名前であるようにします。</summary>
      <param name="name">書き込む名前。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> is not a valid XML name; or <paramref name="name" /> is either null or String.Empty.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteNameAsync(System.String)">
      <summary>指定した名前が W3C 勧告『XML 1.0』(http://www.w3.org/TR/1998/REC-xml-19980210#NT-Name) に準拠した有効な名前であることを確認し、それを非同期に書き込みます。</summary>
      <returns>非同期の WriteName 操作を表すタスク。</returns>
      <param name="name">書き込む名前。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteNmToken(System.String)">
      <summary>派生クラスでオーバーライドされると、指定した名前を書き込み、その名前が W3C 勧告『XML 1.0』(http://www.w3.org/TR/1998/REC-xml-19980210#NT-Name) に準拠した有効な NmToken であるようにします。</summary>
      <param name="name">書き込む名前。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> is not a valid NmToken; or <paramref name="name" /> is either null or String.Empty.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteNmTokenAsync(System.String)">
      <summary>指定した名前が W3C 勧告『XML 1.0』(http://www.w3.org/TR/1998/REC-xml-19980210#NT-Name) に準拠した有効な NmToken であることを確認し、それを非同期に書き込みます。</summary>
      <returns>非同期の WriteNmToken 操作を表すタスク。</returns>
      <param name="name">書き込む名前。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteNode(System.Xml.XmlReader,System.Boolean)">
      <summary>派生クラスでオーバーライドされると、リーダーのデータをすべてライターにコピーし、リーダーを次の兄弟の開始位置に移動します。</summary>
      <param name="reader">読み取り元の <see cref="T:System.Xml.XmlReader" />。</param>
      <param name="defattr">XmlReader の既定の属性をコピーする場合は true。それ以外の場合は false。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="reader" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="reader" /> contains invalid characters.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteNodeAsync(System.Xml.XmlReader,System.Boolean)">
      <summary>派生クラスでオーバーライドされると、リーダーのデータをすべてライターに非同期にコピーし、リーダーを次の兄弟の開始位置に移動します。</summary>
      <returns>非同期の WriteNode 操作を表すタスク。</returns>
      <param name="reader">読み取り元の <see cref="T:System.Xml.XmlReader" />。</param>
      <param name="defattr">XmlReader の既定の属性をコピーする場合は true。それ以外の場合は false。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteProcessingInstruction(System.String,System.String)">
      <summary>派生クラスでオーバーライドされると、&lt;?name text?&gt; など、名前とテキストの間に空白が入った処理命令を書き込みます。</summary>
      <param name="name">処理命令の名前。</param>
      <param name="text">処理命令に含めるテキスト。</param>
      <exception cref="T:System.ArgumentException">The text would result in a non-well formed XML document.<paramref name="name" /> is either null or String.Empty.This method is being used to create an XML declaration after <see cref="M:System.Xml.XmlWriter.WriteStartDocument" /> has already been called. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteProcessingInstructionAsync(System.String,System.String)">
      <summary>名前とテキストの間にスペースがある処理命令を、次のように非同期的に書き込みます: &lt;?name text?&gt;。</summary>
      <returns>非同期の WriteProcessingInstruction 操作を表すタスク。</returns>
      <param name="name">処理命令の名前。</param>
      <param name="text">処理命令に含めるテキスト。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteQualifiedName(System.String,System.String)">
      <summary>派生クラスでオーバーライドされると、名前空間の限定名を書き込みます。このメソッドは、指定した名前空間のスコープ内にあるプレフィックスを検索します。</summary>
      <param name="localName">書き込むローカル名。</param>
      <param name="ns">名前の名前空間 URI。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="localName" /> is either null or String.Empty.<paramref name="localName" /> is not a valid name. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteQualifiedNameAsync(System.String,System.String)">
      <summary>名前空間の修飾名を非同期に書き込みます。このメソッドは、指定した名前空間のスコープ内にあるプレフィックスを検索します。</summary>
      <returns>非同期の WriteQualifiedName 操作を表すタスク。</returns>
      <param name="localName">書き込むローカル名。</param>
      <param name="ns">名前の名前空間 URI。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteRaw(System.Char[],System.Int32,System.Int32)">
      <summary>派生クラスでオーバーライドされると、手動で文字バッファーから生のマークアップを書き込みます。</summary>
      <param name="buffer">書き込むテキストを格納している文字配列。</param>
      <param name="index">書き込むテキストの開始を示すバッファー内の位置。</param>
      <param name="count">書き込む文字数。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero. -or-The buffer length minus <paramref name="index" /> is less than <paramref name="count" />.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteRaw(System.String)">
      <summary>派生クラスでオーバーライドされると、手動で文字列から生のマークアップを書き込みます。</summary>
      <param name="data">書き込むテキストを格納している文字列。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="data" /> is either null or String.Empty.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteRawAsync(System.Char[],System.Int32,System.Int32)">
      <summary>文字バッファーから手動で生のマークアップを非同期に書き込みます。</summary>
      <returns>非同期の WriteRaw 操作を表すタスク。</returns>
      <param name="buffer">書き込むテキストを格納している文字配列。</param>
      <param name="index">書き込むテキストの開始を示すバッファー内の位置。</param>
      <param name="count">書き込む文字数。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteRawAsync(System.String)">
      <summary>文字列から手動で生のマークアップを非同期に書き込みます。</summary>
      <returns>非同期の WriteRaw 操作を表すタスク。</returns>
      <param name="data">書き込むテキストを格納している文字列。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartAttribute(System.String)">
      <summary>指定されたローカル名を使用して属性の開始を書き込みます。</summary>
      <param name="localName">属性のローカル名。</param>
      <exception cref="T:System.InvalidOperationException">The writer is closed.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartAttribute(System.String,System.String)">
      <summary>指定されたローカル名および名前空間 URI を使用して属性の開始を書き込みます。</summary>
      <param name="localName">属性のローカル名。</param>
      <param name="ns">属性の名前空間 URI。</param>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartAttribute(System.String,System.String,System.String)">
      <summary>派生クラスでオーバーライドされると、指定されたプリフィックス、ローカル名、および名前空間 URI を使用して属性の開始を書き込みます。</summary>
      <param name="prefix">属性の名前空間プレフィックス。</param>
      <param name="localName">属性のローカル名。</param>
      <param name="ns">属性の名前空間 URI。</param>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartAttributeAsync(System.String,System.String,System.String)">
      <summary>指定されたプレフィックス、ローカル名、および名前空間 URI を使用して属性の開始を非同期に書き込みます。</summary>
      <returns>非同期の WriteStartAttribute 操作を表すタスク。</returns>
      <param name="prefix">属性の名前空間プレフィックス。</param>
      <param name="localName">属性のローカル名。</param>
      <param name="ns">属性の名前空間 URI。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartDocument">
      <summary>派生クラスでオーバーライドされると、バージョン "1.0" の XML 宣言を書き込みます。</summary>
      <exception cref="T:System.InvalidOperationException">This is not the first write method called after the constructor.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartDocument(System.Boolean)">
      <summary>派生クラスでオーバーライドされると、バージョン "1.0" の XML 宣言とスタンドアロン属性を書き込みます。</summary>
      <param name="standalone">true の場合は "standalone=yes"、false の場合は "standalone=no" を書き込みます。</param>
      <exception cref="T:System.InvalidOperationException">This is not the first write method called after the constructor. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartDocumentAsync">
      <summary>バージョン "1.0" で XML 宣言を非同期に書き込みます。</summary>
      <returns>非同期の WriteStartDocument 操作を表すタスク。</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartDocumentAsync(System.Boolean)">
      <summary>バージョン "1.0" とスタントアロン属性を使用して XML 宣言を非同期に書き込みます。</summary>
      <returns>非同期の WriteStartDocument 操作を表すタスク。</returns>
      <param name="standalone">true の場合は "standalone=yes"、false の場合は "standalone=no" を書き込みます。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartElement(System.String)">
      <summary>派生クラスでオーバーライドされると、指定したローカル名の開始タグを書き込みます。</summary>
      <param name="localName">要素のローカル名。</param>
      <exception cref="T:System.InvalidOperationException">The writer is closed.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartElement(System.String,System.String)">
      <summary>派生クラスでオーバーライドされると、指定した開始タグを書き込み、指定した名前空間に関連付けます。</summary>
      <param name="localName">要素のローカル名。</param>
      <param name="ns">要素に関連付ける名前空間 URI。この名前空間が既にスコープ内にあり、関連付けられたプリフィックスを持つ場合、ライターは、そのプリフィックスも自動的に書き込みます。</param>
      <exception cref="T:System.InvalidOperationException">The writer is closed.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartElement(System.String,System.String,System.String)">
      <summary>派生クラスでオーバーライドされると、指定した開始タグを書き込み、指定した名前空間とプリフィックスに関連付けます。</summary>
      <param name="prefix">要素の名前空間プリフィックス。</param>
      <param name="localName">要素のローカル名。</param>
      <param name="ns">要素に関連付ける名前空間 URI。</param>
      <exception cref="T:System.InvalidOperationException">The writer is closed.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartElementAsync(System.String,System.String,System.String)">
      <summary>指定した開始タグを非同期に書き込み、指定した名前空間とプレフィックスに関連付けます。</summary>
      <returns>非同期の WriteStartElement 操作を表すタスク。</returns>
      <param name="prefix">要素の名前空間プリフィックス。</param>
      <param name="localName">要素のローカル名。</param>
      <param name="ns">要素に関連付ける名前空間 URI。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="P:System.Xml.XmlWriter.WriteState">
      <summary>派生クラスでオーバーライドされると、ライターの状態を取得します。</summary>
      <returns>
        <see cref="T:System.Xml.WriteState" /> 値のいずれか。</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteString(System.String)">
      <summary>派生クラスでオーバーライドされると、指定したテキスト内容を書き込みます。</summary>
      <param name="text">書き込むテキスト。</param>
      <exception cref="T:System.ArgumentException">The text string contains an invalid surrogate pair.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStringAsync(System.String)">
      <summary>指定したテキストの内容を非同期に書き込みます。</summary>
      <returns>非同期の WriteString 操作を表すタスク。</returns>
      <param name="text">書き込むテキスト。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteSurrogateCharEntity(System.Char,System.Char)">
      <summary>派生クラスでオーバーライドされると、サロゲート文字ペアのサロゲート文字エンティティを生成し、書き込みます。</summary>
      <param name="lowChar">下位サロゲート。この値は、0xDC00 から 0xDFFF の範囲内にある必要があります。</param>
      <param name="highChar">上位サロゲート。この値は、0xD800 から 0xDBFF の範囲内にある必要があります。</param>
      <exception cref="T:System.ArgumentException">An invalid surrogate character pair was passed.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteSurrogateCharEntityAsync(System.Char,System.Char)">
      <summary>サロゲート文字ペアのサロゲート文字エンティティを非同期に生成して書き込みます。</summary>
      <returns>非同期の WriteSurrogateCharEntity 操作を表すタスク。</returns>
      <param name="lowChar">下位サロゲート。この値は、0xDC00 から 0xDFFF の範囲内にある必要があります。</param>
      <param name="highChar">上位サロゲート。この値は、0xD800 から 0xDBFF の範囲内にある必要があります。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Boolean)">
      <summary>
        <see cref="T:System.Boolean" /> 値を書き込みます。</summary>
      <param name="value">書き込む <see cref="T:System.Boolean" /> 値。</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.DateTimeOffset)">
      <summary>
        <see cref="T:System.DateTimeOffset" /> 値を書き込みます。</summary>
      <param name="value">書き込む <see cref="T:System.DateTimeOffset" /> 値。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Decimal)">
      <summary>
        <see cref="T:System.Decimal" /> 値を書き込みます。</summary>
      <param name="value">書き込む <see cref="T:System.Decimal" /> 値。</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Double)">
      <summary>
        <see cref="T:System.Double" /> 値を書き込みます。</summary>
      <param name="value">書き込む <see cref="T:System.Double" /> 値。</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Int32)">
      <summary>
        <see cref="T:System.Int32" /> 値を書き込みます。</summary>
      <param name="value">書き込む <see cref="T:System.Int32" /> 値。</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Int64)">
      <summary>
        <see cref="T:System.Int64" /> 値を書き込みます。</summary>
      <param name="value">書き込む <see cref="T:System.Int64" /> 値。</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Object)">
      <summary>オブジェクト値を書き込みます。</summary>
      <param name="value">書き込むオブジェクト値。メモ   .NET Framework 3.5 のリリースでは、このメソッドは <see cref="T:System.DateTimeOffset" /> をパラメーターとして受け入れます。</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">The writer is closed or in error state.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Single)">
      <summary>単精度浮動小数点数を書き込みます。</summary>
      <param name="value">書き込む単精度浮動小数点数。</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.String)">
      <summary>
        <see cref="T:System.String" /> 値を書き込みます。</summary>
      <param name="value">書き込む <see cref="T:System.String" /> 値。</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteWhitespace(System.String)">
      <summary>派生クラスでオーバーライドされると、指定した空白を書き込みます。</summary>
      <param name="ws">空白文字の文字列。</param>
      <exception cref="T:System.ArgumentException">The string contains non-white space characters.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteWhitespaceAsync(System.String)">
      <summary>指定した空白を非同期に書き込みます。</summary>
      <returns>非同期の WriteWhitespace 操作を表すタスク。</returns>
      <param name="ws">空白文字の文字列。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="P:System.Xml.XmlWriter.XmlLang">
      <summary>派生クラスでオーバーライドされると、現在の xml:lang スコープを取得します。</summary>
      <returns>現在の xml:lang スコープ。</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="P:System.Xml.XmlWriter.XmlSpace">
      <summary>派生クラスでオーバーライドされると、現在の xml:space スコープを表す <see cref="T:System.Xml.XmlSpace" /> を取得します。</summary>
      <returns>現在の xml:space スコープを表す XmlSpace。値説明 Nonexml:space スコープが存在しない場合は、これが既定値になります。Default現在のスコープは、xml:space="default" です。Preserve現在のスコープは、xml:space="preserve" です。</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="T:System.Xml.XmlWriterSettings">
      <summary>
        <see cref="Overload:System.Xml.XmlWriter.Create" /> メソッドで作成された <see cref="T:System.Xml.XmlWriter" /> オブジェクトでサポートする一連の機能を指定します。</summary>
    </member>
    <member name="M:System.Xml.XmlWriterSettings.#ctor">
      <summary>
        <see cref="T:System.Xml.XmlWriterSettings" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.Async">
      <summary>非同期 <see cref="T:System.Xml.XmlWriter" /> メソッドを <see cref="T:System.Xml.XmlWriter" /> の特定のインスタンスで使用できるかどうかを示す値を取得または設定します。</summary>
      <returns>非同期メソッドを使用できる場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.CheckCharacters">
      <summary>ドキュメント内のすべての文字が W3C の「XML 1.0 Recommendation (XML 1.0 勧告)」の「2.2 Characters (2.2 文字)」に準拠していることを XML ライターがチェックする必要があるかどうかを示す値を取得または設定します。</summary>
      <returns>文字をチェックする場合は true。それ以外の場合は false。既定値は、true です。</returns>
    </member>
    <member name="M:System.Xml.XmlWriterSettings.Clone">
      <summary>
        <see cref="T:System.Xml.XmlWriterSettings" /> インスタンスのコピーを作成します。</summary>
      <returns>複製された <see cref="T:System.Xml.XmlWriterSettings" /> オブジェクト。</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.CloseOutput">
      <summary>
        <see cref="M:System.Xml.XmlWriter.Close" /> を呼び出したときに、<see cref="T:System.Xml.XmlWriter" /> が、基になるストリームまたは <see cref="T:System.IO.TextWriter" /> も閉じる必要があるかどうかを示す値を取得または設定します。</summary>
      <returns>基になるストリームまたは <see cref="T:System.IO.TextWriter" /> も閉じる場合は true。それ以外の場合は false。既定値は、false です。</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.ConformanceLevel">
      <summary>XML ライターが XML 出力をチェックする準拠のレベルを取得または設定します。</summary>
      <returns>準拠のレベル (ドキュメント、フラグメント、自動検出) を指定する列挙値のいずれか。既定値は、<see cref="F:System.Xml.ConformanceLevel.Document" /> です。</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.Encoding">
      <summary>使用するテキスト エンコーディングの種類を取得または設定します。</summary>
      <returns>使用するテキスト エンコーディング。既定値は、Encoding.UTF8 です。</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.Indent">
      <summary>要素にインデントを設定するかどうかを示す値を取得または設定します。</summary>
      <returns>各要素を新しい行に書き込んでインデントを設定する場合は true、それ以外の場合は false。既定値は、false です。</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.IndentChars">
      <summary>インデント処理を行うときに使用する文字列を取得または設定します。この設定は、<see cref="P:System.Xml.XmlWriterSettings.Indent" /> プロパティが true に設定されている場合に使用します。</summary>
      <returns>インデント処理を行うときに使用する文字列。これには任意の文字列値を設定できます。ただし、有効な XML にするには、空白、タブ、復帰、ライン フィードなどの有効な空白文字だけを指定する必要があります。既定値は 2 つのスペースです。</returns>
      <exception cref="T:System.ArgumentNullException">The value assigned to the <see cref="P:System.Xml.XmlWriterSettings.IndentChars" /> is null.</exception>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.NamespaceHandling">
      <summary>XML コンテンツの書き込み時に、重複する名前空間宣言を <see cref="T:System.Xml.XmlWriter" /> で削除するかどうかを示す値を取得または設定します。既定の動作では、ライターの名前空間リゾルバーに存在するすべての名前空間宣言がライターによって出力されます。</summary>
      <returns>
        <see cref="T:System.Xml.XmlWriter" /> で重複する名前空間宣言を削除するかどうかを指定するための <see cref="T:System.Xml.NamespaceHandling" /> 列挙体。</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.NewLineChars">
      <summary>改行に使用する文字列を取得または設定します。</summary>
      <returns>改行に使用する文字列。これには任意の文字列値を設定できます。ただし、有効な XML にするには、空白、タブ、復帰、ライン フィードなどの有効な空白文字だけを指定する必要があります。既定値は \r\n (復帰、改行) です。</returns>
      <exception cref="T:System.ArgumentNullException">The value assigned to the <see cref="P:System.Xml.XmlWriterSettings.NewLineChars" /> is null.</exception>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.NewLineHandling">
      <summary>出力内の改行を正規化するかどうかを示す値を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Xml.NewLineHandling" /> 値のいずれか。既定値は、<see cref="F:System.Xml.NewLineHandling.Replace" /> です。</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.NewLineOnAttributes">
      <summary>新しい行に属性を書き込むかどうかを示す値を取得または設定します。</summary>
      <returns>個々の行に属性を書き込む場合に true、それ以外の場合は false。既定値は、false です。メモ<see cref="P:System.Xml.XmlWriterSettings.Indent" /> プロパティ値が false の場合、この設定は無効です。<see cref="P:System.Xml.XmlWriterSettings.NewLineOnAttributes" /> を true に設定すると、各属性は、新しい行にインデントを 1 レベル増やして記述されます。</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.OmitXmlDeclaration">
      <summary>XML 宣言を省略するかどうかを示す値を取得または設定します。</summary>
      <returns>XML 宣言を省略する場合は true、それ以外の場合は false。既定値は false で、XML 宣言が書き込まれます。</returns>
    </member>
    <member name="M:System.Xml.XmlWriterSettings.Reset">
      <summary>設定クラスのメンバーを既定値にリセットします。</summary>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.WriteEndDocumentOnClose">
      <summary>
        <see cref="M:System.Xml.XmlWriter.Close" /> メソッドが呼び出されるときに <see cref="T:System.Xml.XmlWriter" /> がすべての閉じられていない要素タグに終了タグを追加するかどうかを示す値を取得または設定します。</summary>
      <returns>閉じられていない要素タグがすべて閉じられる場合は true。それ以外の場合は false。既定値は true です。</returns>
    </member>
    <member name="T:System.Xml.Schema.XmlSchema">
      <summary>W3C (World Wide Web Consortium) の『XML Schema Part 1: Structures』および『XML Schema Part 2: Datatypes』の仕様で指定されている XML スキーマのインメモリ表現です。</summary>
    </member>
    <member name="T:System.Xml.Schema.XmlSchemaForm">
      <summary>属性または要素を、名前空間プレフィックスで修飾する必要があるかどうかを示します。</summary>
    </member>
    <member name="F:System.Xml.Schema.XmlSchemaForm.None">
      <summary>スキーマには、要素および属性の形式が指定されません。</summary>
    </member>
    <member name="F:System.Xml.Schema.XmlSchemaForm.Qualified">
      <summary>要素および属性は、名前空間プレフィックスで修飾する必要があります。</summary>
    </member>
    <member name="F:System.Xml.Schema.XmlSchemaForm.Unqualified">
      <summary>要素および属性は、名前空間プレフィックスで修飾する必要はありません。</summary>
    </member>
    <member name="T:System.Xml.Serialization.IXmlSerializable">
      <summary>XML シリアル化および逆シリアル化のカスタム書式を提供します。</summary>
    </member>
    <member name="M:System.Xml.Serialization.IXmlSerializable.GetSchema">
      <summary>このメソッドは予約されているため、使用できません。IXmlSerializable インターフェイスを実装する場合、このメソッドから null (Visual Basic では Nothing) を返す必要があります。また、カスタム スキーマの指定が要求されている場合は、このクラスに <see cref="T:System.Xml.Serialization.XmlSchemaProviderAttribute" /> を適用します。</summary>
      <returns>
        <see cref="M:System.Xml.Serialization.IXmlSerializable.WriteXml(System.Xml.XmlWriter)" /> メソッドによって生成され <see cref="M:System.Xml.Serialization.IXmlSerializable.ReadXml(System.Xml.XmlReader)" /> メソッドによって処理されるオブジェクトの XML 表現を記述する <see cref="T:System.Xml.Schema.XmlSchema" />。</returns>
    </member>
    <member name="M:System.Xml.Serialization.IXmlSerializable.ReadXml(System.Xml.XmlReader)">
      <summary>オブジェクトの XML 表現からオブジェクトを生成します。</summary>
      <param name="reader">オブジェクトの逆シリアル化元である <see cref="T:System.Xml.XmlReader" /> ストリーム。</param>
    </member>
    <member name="M:System.Xml.Serialization.IXmlSerializable.WriteXml(System.Xml.XmlWriter)">
      <summary>オブジェクトを XML 表現に変換します。</summary>
      <param name="writer">オブジェクトのシリアル化先の <see cref="T:System.Xml.XmlWriter" /> ストリーム。</param>
    </member>
    <member name="T:System.Xml.Serialization.XmlSchemaProviderAttribute">
      <summary>型に適用された場合、XML スキーマを返す型の静的メソッドの名前と、型のシリアル化を制御する <see cref="T:System.Xml.XmlQualifiedName" /> (または匿名型の <see cref="T:System.Xml.Schema.XmlSchemaType" />) を格納します。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemaProviderAttribute.#ctor(System.String)">
      <summary>型の XML スキーマを提供する静的メソッドの名前を受け取って、<see cref="T:System.Xml.Serialization.XmlSchemaProviderAttribute" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="methodName">実装する必要がある静的メソッドの名前。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlSchemaProviderAttribute.IsAny">
      <summary>ターゲット クラスがワイルドカードかどうか、またはクラスのスキーマに xs:any 要素のみが含まれているかどうかを判断する値を取得または設定します。</summary>
      <returns>クラスがワイルドカードの場合、またはスキーマに xs:any 要素のみが含まれている場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlSchemaProviderAttribute.MethodName">
      <summary>型の XML スキーマおよびその XML スキーマ データ型の名前を提供する静的メソッドの名前を取得します。</summary>
      <returns>XML スキーマを返すために XML インフラストラクチャによって呼び出されるメソッドの名前。</returns>
    </member>
  </members>
</doc>