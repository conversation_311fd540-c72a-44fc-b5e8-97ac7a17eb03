﻿Imports System.IO
Imports DevExpress.XtraEditors
Imports System.Text.RegularExpressions
Imports System.Text
Imports System.Drawing
Imports System.Data
Public Class frmGoogleCaptcha
    Private Const VBQuote As String = """"
    ' تعديل دالة ConvertFile لتأخذ النص من RichTextBox1 وتكتب في ملف
    Private Sub ConvertFile(ByVal SourceText As String, ByVal DestFile As String, ByVal Title As String, Optional ByVal DisableClick As Boolean = False)
        Dim sb As New StringBuilder()
        Dim Buffer As String = SourceText
        ' استبدال النصوص بالحقول المدخلة
        If Not String.IsNullOrEmpty(txt_Link_Google_CAPTCHA.Text) Then
            Buffer = Buffer.Replace("[-Link-]", txt_Link_Google_CAPTCHA.Text)
        End If
        If Not String.IsNullOrEmpty(txt_type_Google_CAPTCHA.Text) Then
            Buffer = Buffer.Replace("[-type-]", txt_type_Google_CAPTCHA.Text)
        Else
            Buffer = Buffer.Replace("[-type-]", "")
        End If
        If Not String.IsNullOrEmpty(txt_Site_key.Text) Then
            Buffer = Buffer.Replace("[-Mrrecaptcha-]", txt_Site_key.Text)
        Else
            Buffer = Buffer.Replace("[-Mrrecaptcha-]", "")
        End If
        ' تحقق من النقر بزر الماوس الأيمن
        If DisableClick Then
            Buffer &= "<script>document.oncontextmenu=new Function(" & VBQuote & "return false" & VBQuote & ")</script>"
        End If
        ' بناء الملف
        sb.AppendLine("<html>")
        sb.AppendLine("<head>")
        sb.AppendLine("<title>" & Title & "</title>")
        sb.AppendLine("<body>")
        sb.AppendLine("<script language=" & VBQuote & "JavaScript" & VBQuote & " type=" & VBQuote & "text/javascript" & VBQuote & ">")
        sb.Append("document.write(unescape('")
        For Each c As Char In Buffer
            sb.Append("%" & Asc(c).ToString("X2"))
        Next c
        sb.Append("'));")
        sb.AppendLine("</script>")
        sb.AppendLine("</body>")
        sb.AppendLine("</html>")
        Dim sw As New StreamWriter(DestFile)
        sw.Write(sb.ToString())
        sw.Close()
        Console.WriteLine("File Successfully written to: " & DestFile)
    End Sub
    Dim linkPattern As String = "^(https?)://[^\s/$.?#].[^\s]*$"
    Private Sub frmGoogleCaptcha_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        If txt_Link_Google_CAPTCHA.Text.Trim <> "" Then
            My.Settings.LinkGoogleCaptcha = txt_Link_Google_CAPTCHA.Text
        End If
        If txt_Site_key.Text.Trim <> "" Then
            My.Settings.SiteKey = txt_Site_key.Text
        End If
        If txt_type_Google_CAPTCHA.Text.Trim <> "" Then
            My.Settings.TypeGoogleCaptcha = txt_type_Google_CAPTCHA.Text
        End If
        ToolTip1.SetToolTip(txt_Link_Google_CAPTCHA, "Please Enter Your Link (Example) > https://www.Example.com")
        ToolTip1.SetToolTip(txt_Site_key, "Enter Your Google reCAPTCHA Site key Go to > https://www.google.com/recaptcha")
        ToolTip1.SetToolTip(txt_type_Google_CAPTCHA, "(Example) Decoding document. Please wait...")
        ToolTip1.SetToolTip(BTN_Reset_Google_CAPTCHA, "Reset All")
        ToolTip1.SetToolTip(PictureBox1, "Open https://www.google.com/recaptcha")

        ' إضافة معالج حدث لتغيير حجم النموذج
        AddHandler Me.Resize, AddressOf frmGoogleCaptcha_Resize

        ' توسيط العناصر عند التحميل
        CenterControlsInForm()
    End Sub



    ''' <summary>
    ''' معالج حدث تغيير حجم النموذج
    ''' </summary>
    Private Sub frmGoogleCaptcha_Resize(sender As Object, e As EventArgs)
        ' توسيط العناصر عند تغيير حجم النموذج
        CenterControlsInForm()
    End Sub

    ''' <summary>
    ''' توسيط جميع العناصر في النموذج
    ''' </summary>
    Private Sub CenterControlsInForm()
        ' توسيط الصورة في الأعلى
        PictureBox1.Left = (Me.ClientSize.Width - PictureBox1.Width) \ 2

        ' توسيط حقول الإدخال والتسميات
        Dim inputWidth As Integer = txt_Link_Google_CAPTCHA.Width ' جميع حقول الإدخال لها نفس العرض

        ' توسيط كل حقل إدخال
        txt_Link_Google_CAPTCHA.Left = (Me.ClientSize.Width - inputWidth) \ 2
        txt_Site_key.Left = (Me.ClientSize.Width - inputWidth) \ 2
        txt_type_Google_CAPTCHA.Left = (Me.ClientSize.Width - inputWidth) \ 2

        ' توسيط التسميات بنفس محاذاة حقول الإدخال
        Label1.Left = txt_Link_Google_CAPTCHA.Left
        Label2.Left = txt_Site_key.Left
        Label3.Left = txt_type_Google_CAPTCHA.Left

        ' توسيط مجموعة الأزرار
        Dim buttonWidth As Integer = BTN_Redirect_Link_Google_Click.Width + btn_Save.Width + BTN_Reset_Google_CAPTCHA.Width + 15 * 2 ' المسافة بين الأزرار
        Dim startX As Integer = (Me.ClientSize.Width - buttonWidth) \ 2

        BTN_Redirect_Link_Google_Click.Left = startX
        btn_Save.Left = BTN_Redirect_Link_Google_Click.Right + 15
        BTN_Reset_Google_CAPTCHA.Left = btn_Save.Right + 15
    End Sub


    Private Sub PictureBox1_Click(sender As Object, e As EventArgs)
        Process.Start("https://www.google.com/recaptcha/about/")
    End Sub


    Private Sub btn_Save_Click(sender As Object, e As EventArgs) Handles btn_Save.Click
        txt_Link_Google_CAPTCHA.Properties.NullValuePrompt = "https://www.Example.com/?="
        txt_Site_key.Properties.NullValuePrompt = "Your Google reCAPTCHA Site key"
        txt_type_Google_CAPTCHA.Properties.NullValuePrompt = "Decoding document. Please wait..."
        ' Validate the fields
        If String.IsNullOrWhiteSpace(txt_Link_Google_CAPTCHA.Text) Then
            txt_Link_Google_CAPTCHA.Focus()
            Return
        End If
        If String.IsNullOrWhiteSpace(txt_Site_key.Text) Then
            txt_Site_key.Focus()
            Return
        End If
        If String.IsNullOrWhiteSpace(txt_type_Google_CAPTCHA.Text) Then
            txt_type_Google_CAPTCHA.Focus()
            Return
        End If
        ' حفظ القيم في الإعدادات
        My.Settings.LinkGoogleCaptcha = txt_Link_Google_CAPTCHA.Text
        My.Settings.SiteKey = txt_Site_key.Text
        My.Settings.TypeGoogleCaptcha = txt_type_Google_CAPTCHA.Text
        ' حفظ الإعدادات في الملف
        My.Settings.Save()
        ' عرض رسالة تأكيد باستخدام DevExpress
        DevExpress.XtraEditors.XtraMessageBox.Show("Settings have been saved Successfully!", "Successfully", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub BTN_Reset_Google_CAPTCHA_Click(sender As Object, e As EventArgs) Handles BTN_Reset_Google_CAPTCHA.Click
        txt_Link_Google_CAPTCHA.Clear()
        txt_Site_key.Clear()
        txt_type_Google_CAPTCHA.Clear()
    End Sub

    Private Sub BTN_Redirect_Link_Google_Click_Click(sender As Object, e As EventArgs) Handles BTN_Redirect_Link_Google_Click.Click
        ' التحقق من صحة الرابط
        If String.IsNullOrWhiteSpace(txt_Link_Google_CAPTCHA.Text) OrElse Not Regex.IsMatch(txt_Link_Google_CAPTCHA.Text, linkPattern) Then
            XtraMessageBox.Show("Please enter a valid link starting with http or https.", "Invalid URL", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txt_Link_Google_CAPTCHA.Focus()
            Exit Sub
        End If
        ' التحقق من وجود نص في RichTextBox1
        If Not String.IsNullOrEmpty(RichTextBox1.Text) Then
            ' فتح نافذة حفظ الملف
            Dim saveFileDialog As New SaveFileDialog() With {
                .Title = "Save Encrypted File",
                .Filter = "HTML Files (*.html)|*.html|All Files (*.*)|*.*",
                .DefaultExt = "html",
                .InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)
            }
            ' إذا قام المستخدم بتحديد موقع واسم الملف
            If saveFileDialog.ShowDialog() = DialogResult.OK Then
                Dim DestFile As String = saveFileDialog.FileName
                ' تخزين النص الأصلي
                Dim originalText As String = RichTextBox1.Text
                ' تعديل النصوص
                If Not String.IsNullOrEmpty(txt_Link_Google_CAPTCHA.Text) Then
                    RichTextBox1.Text = RichTextBox1.Text.Replace("[-Link-]", txt_Link_Google_CAPTCHA.Text)
                End If
                If Not String.IsNullOrEmpty(txt_type_Google_CAPTCHA.Text) Then
                    RichTextBox1.Text = RichTextBox1.Text.Replace("[-type-]", txt_type_Google_CAPTCHA.Text)
                End If
                If Not String.IsNullOrEmpty(txt_Site_key.Text) Then
                    RichTextBox1.Text = RichTextBox1.Text.Replace("[-Mrrecaptcha-]", txt_Site_key.Text)
                End If
                ' استدعاء دالة ConvertFile لتحويل النص
                ConvertFile(RichTextBox1.Text, DestFile, "Microsoft")
                ' استرجاع النص الأصلي
                RichTextBox1.Text = originalText
                ' عرض رسالة بنجاح العملية
                DevExpress.XtraEditors.XtraMessageBox.Show("File Successfully !", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Else
                ' إذا لم يحدد المستخدم الملف
                DevExpress.XtraEditors.XtraMessageBox.Show("File saving was canceled.", "Message", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            End If
        Else
            ' عرض رسالة في حالة عدم وجود نص
            DevExpress.XtraEditors.XtraMessageBox.Show("Source text is empty!", "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        End If
    End Sub
End Class
