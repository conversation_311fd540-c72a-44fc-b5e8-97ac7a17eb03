﻿Imports System.Net
Imports System.IO
Imports System.ComponentModel
Imports System.Data.SqlClient
Imports DevExpress.XtraEditors
Imports System.Net.NetworkInformation
Imports DevExpress.XtraWaitForm
Public Class frmDownloadBSVBSV
    Dim client As WebClient = New WebClient
    Dim dirpath As String = Application.StartupPath
    Dim endofdownload As Boolean = False
    Dim speedTestClient As New WebClient()
    Dim testFileUrl As String = "http://speedtest.ftp.otenet.gr/files/test100k.db"
    Dim uploadTestUrl As String = "http://speedtest.ftp.otenet.gr/"
    Dim testStartTime As DateTime
    Dim testDataSize As Long = 102400 ' 100KB
    Private Sub frmDownloadBSVBSV_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ' إضافة معالج حدث لتغيير حجم النموذج
        AddHandler Me.Resize, AddressOf frmDownloadBSVBSV_Resize

        ' Initialize UI elements
        labelSpeed.Text = "Measuring internet speed..."
        lblUpload.Text = "Measuring..."
        lblUpload.Text = "Measuring..."
        ' Start internet speed measurement
        MeasureInternetSpeed()
        ' Original code with ProgressBarControl1 only
        ProgressPanel1.Visible = True
        endofdownload = False
        ProgressBarControl1.EditValue = 0
        labelDownloaded.Visible = True
        labelDownloaded.Text = "0 MB / 0 MB"
        labelSpeed.Visible = True
        labelSpeed.Text = "Current download speed: 0 KB/s"
        ' Configure ProgressBarControl1
        ProgressBarControl1.Properties.Step = 1
        ProgressBarControl1.Properties.PercentView = True
        ProgressBarControl1.Properties.Maximum = 100
        ProgressBarControl1.Properties.Minimum = 0

        ' توسيط العناصر عند تحميل النموذج
        CenterControlsAsGroup()

        Try
            Dim URL As String = "https://www.dropbox.com/scl/fi/vj0q4n4iexb0du4vdesii/Best-Sender.exe?rlkey=p9bxx1kdnrpj52wiob03dxmbg&dl=1"
            AddHandler client.DownloadProgressChanged, AddressOf client_ProgressChanged
            AddHandler client.DownloadFileCompleted, AddressOf client_DownloadCompleted
            client.DownloadFileAsync(New Uri(URL), dirpath & "\Best Sender_NewVersion.exe")
        Catch ex As Exception
            DevExpress.XtraEditors.XtraMessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    Private Sub MeasureInternetSpeed()
        Try
            AddHandler speedTestClient.DownloadDataCompleted, AddressOf DownloadTestCompleted
            testStartTime = DateTime.Now
            speedTestClient.DownloadDataAsync(New Uri(testFileUrl))
        Catch ex As Exception
            labelSpeed.Text = "Speed measurement error"
            lblUpload.Text = "Error"
            lblUpload.Text = "Error"
        End Try
    End Sub
    Private Sub DownloadTestCompleted(sender As Object, e As DownloadDataCompletedEventArgs)
        Try
            Dim endTime As DateTime = DateTime.Now
            Dim timeTaken As TimeSpan = endTime - testStartTime
            Dim speed As Double = (testDataSize / timeTaken.TotalSeconds) / 1024
            lblUpload.Text = String.Format("{0:0.00} KB/s", speed)
            labelSpeed.Text = String.Format("Current download speed: {0:0.00} KB/s", speed)
            TestUploadSpeed()
        Catch ex As Exception
            lblUpload.Text = "Measurement error"
        End Try
    End Sub
    Private Sub TestUploadSpeed()
        Try
            Dim testData(testDataSize) As Byte
            AddHandler speedTestClient.UploadDataCompleted, AddressOf UploadTestCompleted
            testStartTime = DateTime.Now
            speedTestClient.UploadDataAsync(New Uri(uploadTestUrl), testData)
        Catch ex As Exception
            lblUpload.Text = "Measurement error"
        End Try
    End Sub
    Private Sub UploadTestCompleted(sender As Object, e As UploadDataCompletedEventArgs)
        Try
            Dim endTime As DateTime = DateTime.Now
            Dim timeTaken As TimeSpan = endTime - testStartTime
            Dim speed As Double = (testDataSize / timeTaken.TotalSeconds) / 1024
            lblUpload.Text = String.Format("{0:0.00} KB/s", speed)
            Dim downloadSpeed As Double = Double.Parse(lblUpload.Text.Replace(" KB/s", ""))
            labelSpeed.Text = String.Format("Current download speed: {0:0.00} KB/s", downloadSpeed)
        Catch ex As Exception
            lblUpload.Text = "Measurement error"
        End Try
    End Sub
    Private Sub client_ProgressChanged(ByVal sender As Object, ByVal e As DownloadProgressChangedEventArgs)
        Try
            labelSpeed.Text = String.Format("Current download speed: {0:0.00} KB/s", (e.BytesReceived / 1024.0))
            ProgressBarControl1.EditValue = e.ProgressPercentage
            labelDownloaded.Text = String.Format("{0:0.00} MB / {1:0.00} MB",
                                               (e.BytesReceived / 1024.0 / 1024.0),
                                               (e.TotalBytesToReceive / 1024.0 / 1024.0))
        Catch ex As Exception
            DevExpress.XtraEditors.XtraMessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            client.CancelAsync()
            client.Dispose()
        End Try
    End Sub
    Private Sub client_DownloadCompleted(ByVal sender As Object, ByVal e As System.ComponentModel.AsyncCompletedEventArgs)
        If ProgressBarControl1.Properties.Maximum = 100 Then
            Try
                If File.Exists(dirpath & "\Best Sender_OldVersion.exe") Then File.Delete(dirpath & "\Best Sender_OldVersion.exe")
                Dim sourceFile = dirpath & "\Best Sender.exe"
                Dim NewVerion As String = Path.Combine(dirpath, "Best Sender_NewVersion.exe")
                My.Computer.FileSystem.RenameFile(sourceFile, "Best Sender_OldVersion.exe")
                My.Computer.FileSystem.RenameFile(NewVerion, "Best Sender.exe")
                Dim args As New XtraMessageBoxArgs()
                args.AutoCloseOptions.Delay = 5000
                args.AutoCloseOptions.ShowTimerOnDefaultButton = True
                args.DefaultButtonIndex = 0
                args.Icon = System.Drawing.SystemIcons.Information
                args.Caption = "Auto-close message"
                args.Text = "Update download complete. Restarting in..."
                args.Buttons = New DialogResult() {DialogResult.OK}
                XtraMessageBox.Show(args).ToString()
                System.Diagnostics.Process.Start(Application.ExecutablePath)
                Process.GetCurrentProcess().Kill()
            Catch ex As Exception
                DevExpress.XtraEditors.XtraMessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub

    ''' <summary>
    ''' معالج حدث تغيير حجم النموذج
    ''' </summary>
    Private Sub frmDownloadBSVBSV_Resize(sender As Object, e As EventArgs)
        ' وضع العناصر في المنتصف عند تغيير حجم النموذج
        CenterControlsAsGroup()
    End Sub

    ''' <summary>
    ''' توسيط جميع العناصر كمجموعة واحدة مع الحفاظ على ترتيبها النسبي
    ''' </summary>
    Private Sub CenterControlsAsGroup()
        Try
            ' التحقق من أن النموذج مهيأ بشكل كامل
            If Not Me.IsHandleCreated OrElse Me.IsDisposed Then
                Return
            End If

            ' تحديد العناصر بترتيبها العمودي (من الأعلى للأسفل)
            Dim controlsInOrder As New List(Of Control) From {
                ProgressPanel1,      ' الأعلى
                labelDownloaded,     ' تحت ProgressPanel1
                labelSpeed,          ' تحت labelDownloaded
                lblUpload,           ' تحت labelSpeed
                ProgressBarControl1, ' تحت lblUpload
                Label1,              ' تحت ProgressBarControl1
                Label2               ' الأسفل
            }

            ' التحقق من وجود العناصر
            Dim validControls As New List(Of Control)
            For Each ctrl As Control In controlsInOrder
                If ctrl IsNot Nothing AndAlso ctrl.Visible Then
                    validControls.Add(ctrl)
                End If
            Next

            If validControls.Count = 0 Then Return

            ' حساب المسافات النسبية بين العناصر (الحفاظ على الترتيب الأصلي)
            Dim originalSpacings As New List(Of Integer)
            For i As Integer = 0 To validControls.Count - 2
                Dim spacing As Integer = validControls(i + 1).Top - validControls(i).Bottom
                originalSpacings.Add(spacing)
            Next

            ' حساب الارتفاع الإجمالي للمجموعة
            Dim totalHeight As Integer = 0
            For Each ctrl As Control In validControls
                totalHeight += ctrl.Height
            Next
            For Each spacing As Integer In originalSpacings
                totalHeight += spacing
            Next

            ' حساب نقطة البداية لتوسيط المجموعة عمودياً
            Dim startY As Integer = (Me.ClientSize.Height - totalHeight) \ 2

            ' توسيط العناصر مع الحفاظ على المسافات النسبية
            Dim currentY As Integer = startY
            For i As Integer = 0 To validControls.Count - 1
                Dim ctrl As Control = validControls(i)

                ' توسيط أفقياً
                If ctrl Is ProgressBarControl1 Then
                    ' ProgressBarControl1 يمتد عبر عرض النموذج مع هوامش
                    ctrl.Left = 12
                    ctrl.Width = Me.ClientSize.Width - 24
                Else
                    ' باقي العناصر توسط في المنتصف
                    ctrl.Left = (Me.ClientSize.Width - ctrl.Width) \ 2
                End If

                ' وضع العنصر في الموضع العمودي الصحيح
                ctrl.Top = currentY

                ' تحديث الموضع للعنصر التالي
                currentY = ctrl.Bottom
                If i < originalSpacings.Count Then
                    currentY += originalSpacings(i)
                End If
            Next

        Catch ex As Exception
            ' في حالة حدوث خطأ، لا نفعل شيئاً لتجنب تعطيل النموذج
            System.Diagnostics.Debug.WriteLine($"Error in CenterControlsAsGroup: {ex.Message}")
        End Try
    End Sub
End Class