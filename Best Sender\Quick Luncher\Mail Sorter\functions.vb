﻿Imports System
Imports DnsClient
Imports System.Collections.Generic
Imports System.Linq
Imports System.Text.RegularExpressions
Imports System.Data
Imports System.Collections.ObjectModel
Imports System.IO
Imports Amazon.S3
Friend Class functions
    Public Shared DomainsList As List(Of String) = New List(Of String)({"google", "mx-aol", "yahoodns", "hotmail-com", "outlook-com",
                                                               "outlook", "secureserver.net", "emailsrvr.com", "netease",
                                                               "263.net", "aliyun.com", "zimbra", "amazon", "anazana", "icoremail",
                                                               "earthlink.net", "gmx", "hinet", "icloud", "iinet", "mimecast.com",
                                                               "privateemail.com", "orange.fr", ".qq.", "web.de", "yandex", "zoho",
                                                               "mail.ru", "1and1"})
    Public Shared Function ExtractEmails(ByVal textToScrape As String, ByVal filter As String) As String
        Dim reg As Regex = New Regex("[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,6}", RegexOptions.IgnoreCase)
        Dim match As Match
        filter = If(Equals(filter, "AllMails"), "", filter)
        Dim results = ""
        match = reg.Match(textToScrape)
        While match.Success
            If Not results.Contains(match.Value) And match.Value.Contains(filter) Then results += match.Value & Environment.NewLine
            match = match.NextMatch()
        End While
        results = results.TrimEnd(Microsoft.VisualBasic.Strings.ChrW(13), Microsoft.VisualBasic.Strings.ChrW(10))
        'results = results.Length > 0 ? results = results.Remove(results.Length - 1) : results;
        Return results
    End Function
    Public Shared Function ExtractCombo(ByVal textToScrape As String, ByVal separator As Char) As String
        Dim reg As Regex = New Regex("[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,6}\" & separator & "[^\s]*", RegexOptions.IgnoreCase)
        Dim match As Match
        Dim results = ""
        'textToScrape = textToScrape.Replace(Environment.NewLine, " ");
        match = reg.Match(textToScrape)
        While match.Success
            If Not results.Contains(match.Value) Then results += match.Value & Environment.NewLine
            match = match.NextMatch()
        End While
        results = results.TrimEnd(Microsoft.VisualBasic.Strings.ChrW(13), Microsoft.VisualBasic.Strings.ChrW(10))
        'results = results.Length > 0 ? results = results.Remove(results.Length - 1) : results;
        Return results
    End Function
    Public Shared Function ConvertEmailToUser(ByVal textToScrape As String) As String
        Dim reg As Regex = New Regex("[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,6}\:[^\s]*", RegexOptions.IgnoreCase)
        Dim match As Match
        Dim results = ""
        Dim mailList As List(Of String) = New List(Of String)()
        'textToScrape = textToScrape.Replace(Environment.NewLine, " ");
        match = reg.Match(textToScrape)
        While match.Success
            If Not mailList.Contains(match.Value) Then mailList.Add(match.Value)
            match = match.NextMatch()
        End While
        For i = 0 To mailList.Count - 1
            results += mailList(i).Substring(0, mailList(i).IndexOf("@"c)) & ":"c & mailList(i).Substring(mailList(i).IndexOf(":"c) + 1) & Environment.NewLine
        Next
        results = results.TrimEnd(Microsoft.VisualBasic.Strings.ChrW(13), Microsoft.VisualBasic.Strings.ChrW(10))
        'results = results.Length > 0 ? results = results.Remove(results.Length - 1) : results;
        Return results
    End Function
    Public Shared Function FillDt(ByVal client As ILookupClient, ByVal dt As DataTable, ByVal mailList As List(Of String)) As DataTable
        Dim host, domain As String
        Dim splitted As String()
        Dim Exchange As String
        For Each email In mailList
            Try
                host = email.Substring(email.IndexOf("@"c) + 1)
                Dim result = client.Query(host, QueryType.MX)
                Exchange = CType(Enumerable.ElementAt(result.Answers, 0), DnsClient.Protocol.MxRecord).Exchange
                Exchange = Exchange.TrimEnd("."c)
                splitted = Exchange.Split("."c)
                domain = splitted(splitted.Length - 2) & "."c & splitted(splitted.Length - 1)
                dt.Rows.Add(email, Exchange, domain)
            Catch ex As Exception
            End Try
        Next
        Return dt
    End Function
    Public Shared Sub returndomain(ByVal mxrecord As String, ByVal email As String, ByVal data As DataModel, frm As frmEmailSorter)
        If mxrecord.Contains("google") Then
            data.sortedMails.Add(email)
            'frm.Button27.Invoke(Sub()
            '                        frm.Button27.Text = frm.Data.sortedMails.Count.ToString
            '                        frm.Refresh()
            '                    End Sub)
        ElseIf mxrecord.Contains("mx-aol") Then
            data.sortedMail1.Add(email)
            'frm.Button28.Invoke(Sub()
            '                        frm.Button28.Text = frm.Data.sortedMail1.Count.ToString
            '                        frm.Refresh()
            '                    End Sub)
        ElseIf mxrecord.Contains("yahoodns") Then
            data.sortedMail1.Add(email)
            'frm.Button28.Invoke(Sub()
            '                        frm.Button28.Text = frm.Data.sortedMail1.Count.ToString
            '                        frm.Refresh()
            '                    End Sub)
        ElseIf mxrecord.Contains("hotmail-com") Then
            data.sortedMail2.Add(email)
            'frm.Button29.Invoke(Sub()
            '                        frm.Button29.Text = frm.Data.sortedMail2.Count.ToString
            '                        frm.Refresh()
            '                    End Sub)
        ElseIf mxrecord.Contains("outlook-com") Then
            data.sortedMail3.Add(email)
            'frm.Button30.Invoke(Sub()
            '                        frm.Button30.Text = frm.Data.sortedMail3.Count.ToString
            '                        frm.Refresh()
            '                    End Sub)
        ElseIf mxrecord.Contains("outlook") Then
            data.sortedMail4.Add(email)
            'frm.Button31.Invoke(Sub()
            '                        frm.Button31.Text = frm.Data.sortedMail4.Count.ToString
            '                        frm.Refresh()
            '                    End Sub)
        ElseIf mxrecord.Contains("secureserver.net") Then
            data.sortedMail5.Add(email)
            'frm.Button38.Invoke(Sub()
            '                        frm.Button38.Text = frm.Data.sortedMail5.Count.ToString
            '                        frm.Refresh()
            '                    End Sub)
        ElseIf mxrecord.Contains("emailsrvr.com") Then
            data.sortedMail8.Add(email)
            'frm.Button34.Invoke(Sub()
            '                        frm.Button34.Text = frm.Data.sortedMail8.Count.ToString
            '                        frm.Refresh()
            '                    End Sub)
        ElseIf mxrecord.Contains("netease") Then
            'str = string.Concat("163,", email);
            data.sortedMail6.Add(email)
            'frm.Button32.Invoke(Sub()
            '                        frm.Button32.Text = frm.Data.sortedMail6.Count.ToString
            '                        frm.Refresh()
            '                    End Sub)
        ElseIf (mxrecord.Contains("263.net")) Then
            data.sortedMail11.Add(email)
            'frm.Button51.Invoke(Sub()
            '                        frm.Button51.Text = frm.Data.sortedMail11.Count.ToString
            '                        frm.Refresh()
            '                    End Sub)
            '{
            '	emails[0].Add(mxrecord);
            '	//str = string.Concat("263,", email);
            '}
        ElseIf (mxrecord.Contains("aliyun.com")) Then
            data.sortedMail12.Add(email)
            'frm.Button52.Invoke(Sub()
            '                        frm.Button52.Text = frm.Data.sortedMail12.Count.ToString
            '                        frm.Refresh()
            '                    End Sub)
            '{
            '	emails[0].Add(mxrecord);
            '	//str = string.Concat("Aliyun,", email);
            '}
        ElseIf (mxrecord.Contains("zimbra")) Then
            data.sortedMail26.Add(email)
            'frm.Button88.Invoke(Sub()
            '                        frm.Button88.Text = frm.Data.sortedMail26.Count.ToString
            '                        frm.Refresh()
            '                    End Sub)
        ElseIf (mxrecord.Contains("amazon")) Then
            data.sortedMail28.Add(email)
            'frm.Button80.Invoke(Sub()
            '                        frm.Button80.Text = frm.Data.sortedMail28.Count.ToString
            '                        frm.Refresh()
            '                    End Sub)
        ElseIf (mxrecord.Contains("anazana")) Then
            data.sortedMail13.Add(email)
            'frm.Button53.Invoke(Sub()
            '                        frm.Button53.Text = frm.Data.sortedMail13.Count.ToString
            '                        frm.Refresh()
            '                    End Sub)
        ElseIf (mxrecord.Contains("icoremail")) Then
            data.sortedMail14.Add(email)
            'frm.Button54.Invoke(Sub()
            '                        frm.Button54.Text = frm.Data.sortedMail14.Count.ToString
            '                        frm.Refresh()
            '                    End Sub)
        ElseIf (mxrecord.Contains("earthlink.net")) Then
            data.sortedMail15.Add(email)
            'frm.Button55.Invoke(Sub()
            '                        frm.Button55.Text = frm.Data.sortedMail15.Count.ToString
            '                        frm.Refresh()
            '                    End Sub)
        ElseIf (mxrecord.Contains("gmx")) Then
            data.sortedMail16.Add(email)
            'frm.Button56.Invoke(Sub()
            '                        frm.Button56.Text = frm.Data.sortedMail16.Count.ToString
            '                        frm.Refresh()
            '                    End Sub)
            '{
            '	emails[0].Add(mxrecord);
            '	//str = string.Concat("Gmx,", email);
            '}
        ElseIf (mxrecord.Contains("hinet")) Then
            data.sortedMail17.Add(email)
            'frm.Button57.Invoke(Sub()
            '                        frm.Button57.Text = frm.Data.sortedMail17.Count.ToString
            '                        frm.Refresh()
            '                    End Sub)
            '{
            '	emails[0].Add(mxrecord);
            '	//str = string.Concat("Hinet,", email);
            '}
        ElseIf (mxrecord.Contains("icloud")) Then
            data.sortedMail18.Add(email)
            'frm.Button58.Invoke(Sub()
            '                        frm.Button58.Text = frm.Data.sortedMail18.Count.ToString
            '                        frm.Refresh()
            '                    End Sub)
        ElseIf (mxrecord.Contains("iinet")) Then
            data.sortedMail19.Add(email)
            'frm.Button59.Invoke(Sub()
            '                        frm.Button59.Text = frm.Data.sortedMail19.Count.ToString
            '                        frm.Refresh()
            '                    End Sub)
        ElseIf (mxrecord.Contains("mimecast.com")) Then
            data.sortedMail20.Add(email)
            'frm.Button60.Invoke(Sub()
            '                        frm.Button60.Text = frm.Data.sortedMail20.Count.ToString
            '                        frm.Refresh()
            '                    End Sub)
        ElseIf (mxrecord.Contains("privateemail.com")) Then
            data.sortedMail21.Add(email)
            'frm.Button61.Invoke(Sub()
            '                        frm.Button61.Text = frm.Data.sortedMail21.Count.ToString
            '                        frm.Refresh()
            '                    End Sub)
            '{
            '	emails[0].Add(mxrecord);
            '	//str = string.Concat("NameCheap,", email);
            '}
        ElseIf (mxrecord.Contains("orange.fr")) Then
            data.sortedMail22.Add(email)
            'frm.Button84.Invoke(Sub()
            '                        frm.Button84.Text = frm.Data.sortedMail22.Count.ToString
            '                        frm.Refresh()
            '                    End Sub)
        ElseIf (mxrecord.Contains(".qq.")) Then
            data.sortedMail23.Add(email)
            'frm.Button85.Invoke(Sub()
            '                        frm.Button85.Text = frm.Data.sortedMail23.Count.ToString
            '                        frm.Refresh()
            '                    End Sub)
            '{
            '	emails[5].Add(mxrecord);
            '}
        ElseIf (mxrecord.Contains("web.de")) Then
            data.sortedMail24.Add(email)
            'frm.Button86.Invoke(Sub()
            '                        frm.Button86.Text = frm.Data.sortedMail24.Count.ToString
            '                        frm.Refresh()
            '                    End Sub)
        ElseIf (mxrecord.Contains("yandex")) Then
            data.sortedMail25.Add(email)
        ElseIf (mxrecord.Contains(".ovh") OrElse mxrecord.Contains(".fr")) Then
            data.sortedMail29.Add(email)
            'frm.Button87.Invoke(Sub()
            '                        frm.Button87.Text = frm.Data.sortedMail25.Count.ToString
            '                        frm.Refresh()
            '                    End Sub)
            '{
            '	emails[0].Add(mxrecord);
            '	//str = string.Concat("Yandex,", email);            '}
        ElseIf (mxrecord.Contains("zoho")) Then
            data.sortedMail27.Add(email)
            'frm.Button89.Invoke(Sub()
            '                        frm.Button89.Text = frm.Data.sortedMail27.Count.ToString
            '                        frm.Refresh()
            '                    End Sub)
            '{
            '	emails[0].Add(mxrecord);
            '	//str = string.Concat("Mimecast,", email);
            '}
            '{
            '	emails[0].Add(mxrecord);
            '	//str = string.Concat("Earthlink,", email);
            '}
            '{
            '	emails[0].Add(mxrecord);
            '	//str = string.Concat("Coremail,", email);
            '}
            'else if (mxrecord.Contains("myregisteredsite.com"))
            '{
            '	emails[0].Add(mxrecord);
            '	//str = string.Concat("NetworkSolutions,", email);
            '}
        ElseIf mxrecord.Contains("mail.ru") Then
            'str = string.Concat("Mailru,", email);
            data.sortedMail9.Add(email)
            'frm.Button35.Invoke(Sub()
            '                        frm.Button35.Text = frm.Data.sortedMail9.Count.ToString
            '                        frm.Refresh()
            '                    End Sub)
            'else if (mxrecord.Contains("comcast"))
            '{
            '	emails[0].Add(mxrecord);
            '	//str = string.Concat("Comcast,", email);
            '}
            'else if (mxrecord.Contains("email.rr.com"))
            '{
            '	emails[0].Add(mxrecord);
            '	//str = string.Concat("RR,", email);
            '}
        ElseIf mxrecord.Contains("1and1") Then
            'str = string.Concat("1and1,", email);
            'else if (mxrecord.Contains("hibox"))
            '{
            '	emails[0].Add(mxrecord);
            '	//str = string.Concat("Hibox,", email);
            '}
            'else if ((mxrecord.Contains("securemail-mx1.synaq.com") ? true : mxrecord.Contains("securemail-mx2.synaq.com")))
            '{
            '	emails[0].Add(mxrecord);
            '	//str = string.Concat("Synaq,", email);
            '}
            'else if ((mxrecord.Contains("securemail-mx3.synaq.com") ? true : mxrecord.Contains("securemail-mx4.synaq.com")))
            '{
            '	emails[0].Add(mxrecord);
            '	//str = string.Concat("Mweb,", email);
            '}
            'else if (mxrecord.Contains("chinaemail"))
            '{
            '	emails[0].Add(mxrecord);
            '	//str = string.Concat("ChinaEmail,", email);
            '}
            'else if (mxrecord.Contains("zmail.com"))
            '{
            '	emails[0].Add(mxrecord);
            '	//str = string.Concat("Zmail,", email);
            '}
            'else if (mxrecord.Contains("yizhigher"))
            '{
            '	emails[0].Add(mxrecord);
            '	//str = string.Concat("Yizhigher,", email);
            '}
            'else if (mxrecord.Contains("hiworks"))
            '{
            '	emails[0].Add(mxrecord);
            '	//str = string.Concat("Hiworks,", email);
            '}
            'else if (mxrecord.Contains("cn4e"))
            '{
            '	emails[0].Add(mxrecord);
            '	//str = string.Concat("CN4E,", email);
            '}
            'else if (mxrecord.Contains("domainlocalhost"))
            '{
            '	emails[0].Add(mxrecord);
            '	//str = string.Concat("DomainLocalHost,", email);
            '}
            'else if (mxrecord.Contains("arsmtp.com"))
            '{
            '	emails[0].Add(mxrecord);
            '	//str = string.Concat("ARSMTP,", email);
            '}
            'else if (mxrecord.Contains("aruba"))
            '{
            '	emails[0].Add(mxrecord);
            '	//str = string.Concat("Aruba,", email);
            '}
            'else if (mxrecord.Contains("protonmail"))
            '{
            '	emails[0].Add(mxrecord);
            '	//str = string.Concat("Protonmail,", email);
            '}
            'else if (mxrecord.Contains("naver"))
            '{
            '	emails[0].Add(mxrecord);
            '	//str = string.Concat("Naver,", email);
            '}
            'else if (mxrecord.Contains("mailplug"))
            '{
            '	emails[0].Add(mxrecord);
            '	//str = string.Concat("Mailplug,", email);
            '}
            'else if (mxrecord.Contains("rediff"))
            '{
            '	emails[0].Add(mxrecord);
            '	//str = string.Concat("RediffMailPro,", email);
            '}
            'else if (!mxrecord.Contains("servidoresdns"))
            '{
            '	emails[0].Add(mxrecord);
            '	//str = (!mxrecord.Contains("register.it") ? string.Concat("Others,", email) : string.Concat("Register.it,", email));
            '}
            data.sortedMail7.Add(email)
            'frm.Button33.Invoke(Sub()
            '                        frm.Button33.Text = frm.Data.sortedMail7.Count.ToString
            '                        frm.Refresh()
            '                    End Sub)
        Else
            data.sortedMail10.Add(email)
            'frm.Button36.Invoke(Sub()
            '                        frm.Button36.Text = frm.Data.sortedMail10.Count.ToString
            '                        frm.Refresh()
            '                    End Sub)
            'str = string.Concat("Serviciodecorreo,", email);
        End If
    End Sub
    Public Shared Function generateTxt(ByVal List As ObservableCollection(Of String)) As String
        Dim txt = ""
        For Each ele In List
            txt += Environment.NewLine & ele
        Next
        Return txt.Trim(Environment.NewLine.ToCharArray())
    End Function
    Public Shared Sub SaveLists(ByVal data As DataModel, ByVal path As String)
        Using sw = File.CreateText(path & "Gmail.txt")
            For i = 0 To data.sortedMails.Count - 1
                sw.WriteLine(data.sortedMails(i))
            Next
        End Using
        Using sw = File.CreateText(path & "Yahoo.txt")
            For i = 0 To data.sortedMail1.Count - 1
                sw.WriteLine(data.sortedMail1(i))
            Next
        End Using
        Using sw = File.CreateText(path & "Hotmail.txt")
            For i = 0 To data.sortedMail2.Count - 1
                sw.WriteLine(data.sortedMail2(i))
            Next
        End Using
        Using sw = File.CreateText(path & "Outlook.txt")
            For i = 0 To data.sortedMail3.Count - 1
                sw.WriteLine(data.sortedMail3(i))
            Next
        End Using
        Using sw = File.CreateText(path & "Office365.txt")
            For i = 0 To data.sortedMail4.Count - 1
                sw.WriteLine(data.sortedMail4(i))
            Next
        End Using
        Using sw = File.CreateText(path & "GoDaddy.txt")
            For i = 0 To data.sortedMail5.Count - 1
                sw.WriteLine(data.sortedMail5(i))
            Next
        End Using
        Using sw = File.CreateText(path & "163.txt")
            For i = 0 To data.sortedMail6.Count - 1
                sw.WriteLine(data.sortedMail6(i))
            Next
        End Using
        Using sw = File.CreateText(path & "1 and 1.txt")
            For i = 0 To data.sortedMail7.Count - 1
                sw.WriteLine(data.sortedMail7(i))
            Next
        End Using
        Using sw = File.CreateText(path & "RackSpace.txt")
            For i = 0 To data.sortedMail8.Count - 1
                sw.WriteLine(data.sortedMail8(i))
            Next
        End Using
        Using sw = File.CreateText(path & "MailRu.txt")
            For i = 0 To data.sortedMail9.Count - 1
                sw.WriteLine(data.sortedMail9(i))
            Next
        End Using
        Using sw = File.CreateText(path & "Other.txt")
            For i = 0 To data.sortedMail10.Count - 1
                sw.WriteLine(data.sortedMail10(i))
            Next
        End Using
        Using sw = File.CreateText(path & "OvhCloudFr.txt")
            For i = 0 To data.sortedMail29.Count - 1
                sw.WriteLine(data.sortedMail29(i))
            Next
        End Using
    End Sub
End Class
